{"formatVersion": "1.1", "component": {"group": "io.github.resilience4j", "module": "resilience4j-bom", "version": "2.2.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.4.2"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "platform", "org.gradle.usage": "java-api"}, "dependencyConstraints": [{"group": "io.github.resilience4j", "module": "resilience4j-core", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-ratelimiter", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-cache", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-retry", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-circuitbreaker", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-bulkhead", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-all", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-circularbuffer", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-metrics", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-consumer", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-annotations", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-spring", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-spring6", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-spring-boot2", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-spring-boot3", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-spring-cloud2", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-feign", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-timelimiter", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-rxjava2", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-rxjava3", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-reactor", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-micrometer", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-kotlin", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-vavr", "version": {"requires": "2.2.0"}}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "platform", "org.gradle.usage": "java-runtime"}, "dependencyConstraints": [{"group": "io.github.resilience4j", "module": "resilience4j-core", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-ratelimiter", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-cache", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-retry", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-circuitbreaker", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-bulkhead", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-all", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-circularbuffer", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-metrics", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-consumer", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-annotations", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-spring", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-spring6", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-spring-boot2", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-spring-boot3", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-spring-cloud2", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-feign", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-timelimiter", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-rxjava2", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-rxjava3", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-reactor", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-micrometer", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-kotlin", "version": {"requires": "2.2.0"}}, {"group": "io.github.resilience4j", "module": "resilience4j-vavr", "version": {"requires": "2.2.0"}}]}]}