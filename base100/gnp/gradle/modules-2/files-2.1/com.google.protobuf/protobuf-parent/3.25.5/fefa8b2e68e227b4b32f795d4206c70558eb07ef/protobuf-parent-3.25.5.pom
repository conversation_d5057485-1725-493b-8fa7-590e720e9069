<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.google.protobuf</groupId>
  <artifactId>protobuf-parent</artifactId>
  <version>3.25.5</version>
  <packaging>pom</packaging>

  <name>Protocol Buffers [Parent]</name>
  <inceptionYear>2008</inceptionYear>
  <url>https://developers.google.com/protocol-buffers/</url>
  <description>
    Protocol Buffers are a way of encoding structured data in an efficient yet
    extensible format.
  </description>

  <developers>
    <developer>
      <id>protobuf</id>
      <name>Protocol Buffers</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <organizationUrl>https://protobuf.dev</organizationUrl>
      <timezone>America/Los_Angeles</timezone>
    </developer>
  </developers>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    <!-- These are relative to the submodules -->
    <protobuf.basedir>${project.basedir}/../..</protobuf.basedir>
    <protobuf.source.dir>${protobuf.basedir}/src</protobuf.source.dir>
    <protoc>${protobuf.basedir}/protoc</protoc>
    <test.proto.dir>src/test/proto</test.proto.dir>
    <generated.sources.dir>${project.build.directory}/generated-sources</generated.sources.dir>
    <generated.testsources.dir>${project.build.directory}/generated-test-sources</generated.testsources.dir>
  </properties>

  <licenses>
    <license>
      <name>BSD-3-Clause</name>
      <url>https://opensource.org/licenses/BSD-3-Clause</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <scm>
    <url>https://github.com/protocolbuffers/protobuf</url>
    <connection>scm:git:https://github.com/protocolbuffers/protobuf.git</connection>
  </scm>

  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-staging</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-bom</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.13.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>4.3.1</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>32.0.1-android</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava-testlib</artifactId>
        <version>32.0.1-android</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.google.truth</groupId>
        <artifactId>truth</artifactId>
        <version>1.1.2</version>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.6.1</version>
          <configuration>
            <source>1.7</source>
            <target>1.7</target>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.4</version>
          <executions>
            <execution>
              <id>attach-sources</id>
              <goals>
                <goal>jar-no-fork</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>2.10.3</version>
          <executions>
            <execution>
              <id>attach-javadocs</id>
              <goals>
                <goal>jar</goal>
              </goals>
              <configuration>
                <failOnError>false</failOnError>
              </configuration>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-jar-plugin</artifactId>
          <version>2.6</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>1.10</version>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>3.0.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>3.0.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>3.0.0-M5</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.20</version>
        </plugin>
      </plugins>
    </pluginManagement>
    
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <configuration>
          <signature>
            <groupId>net.sf.androidscents.signature</groupId>
            <artifactId>android-api-level-14</artifactId>
            <version>4.0_r4</version>
          </signature>
          <ignores>
            <ignore>sun.misc.Unsafe</ignore>
          </ignores>
        </configuration>
        <executions>
          <execution>
            <id>android</id>
            <phase>test</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>2.2.1</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>2.10.3</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <failOnError>false</failOnError>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.6</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.sonatype.plugins</groupId>
            <artifactId>nexus-staging-maven-plugin</artifactId>
            <version>1.6.6</version>
            <extensions>true</extensions>
            <configuration>
              <serverId>sonatype-nexus-staging</serverId>
              <nexusUrl>https://oss.sonatype.org/</nexusUrl>
              <autoReleaseAfterClose>false</autoReleaseAfterClose>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.jetbrains.dokka</groupId>
            <artifactId>dokka-maven-plugin</artifactId>
            <version>1.4.32</version>
            <executions>
              <execution>
                <phase>prepare-package</phase>
                <goals>
                  <goal>javadocJar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <modules>
    <module>bom</module>
    <module>lite</module>
    <module>core</module>
    <module>util</module>
    <module>kotlin</module>
    <module>kotlin-lite</module>
  </modules>

</project>
