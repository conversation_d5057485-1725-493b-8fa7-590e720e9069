<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 1997, 2023 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.7</version>
        <relativePath/>
    </parent>

    <groupId>com.sun.istack</groupId>
    <artifactId>istack-commons</artifactId>
    <version>4.1.2</version>
    <packaging>pom</packaging>
    <name>iStack Common Utility Code</name>
    <description>istack common utility code</description>

    <scm>
        <connection>scm:git:ssh://**************/eclipse-ee4j/jaxb-istack-commons.git</connection>
        <developerConnection>scm:git:ssh://**************/eclipse-ee4j/jaxb-istack-commons.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/jaxb-istack-commons</url>
        <tag>HEAD</tag>
    </scm>

    <licenses>
        <license>
            <name>Eclipse Distribution License - v 1.0</name>
            <url>http://www.eclipse.org/org/documents/edl-v10.php</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <name>Lukas Jungmann</name>
            <email><EMAIL></email>
            <organization>Oracle Corporation</organization>
        </developer>
    </developers>

    <issueManagement>
        <system>github</system>
        <url>https://github.com/eclipse-ee4j/jaxb-istack-commons/issues</url>
    </issueManagement>

    <mailingLists>
        <mailingList>
            <name>Eclipse Implementation of JAXB mailing list</name>
            <post><EMAIL></post>
            <subscribe>https://dev.eclipse.org/mailman/listinfo/jaxb-impl-dev</subscribe>
            <unsubscribe>https://dev.eclipse.org/mailman/listinfo/jaxb-impl-dev</unsubscribe>
            <archive>https://dev.eclipse.org/mhonarc/lists/jaxb-impl-dev</archive>
        </mailingList>
    </mailingLists>

    <properties>
        <project.build.commonResourcesDirectory>${project.build.directory}/common-resources</project.build.commonResourcesDirectory>
        <legal.doc.source>${project.build.commonResourcesDirectory}/legal</legal.doc.source>
        <config.dir>${project.build.commonResourcesDirectory}/config</config.dir>
        <copyright.exclude>${config.dir}/copyright-exclude</copyright.exclude>
        <copyright.ignoreyear>false</copyright.ignoreyear>
        <copyright.scmonly>true</copyright.scmonly>
        <copyright.update>false</copyright.update>
        <spotbugs.exclude>${config.dir}/spotbugs-exclude.xml</spotbugs.exclude>
        <spotbugs.skip>false</spotbugs.skip>
        <spotbugs.threshold>Low</spotbugs.threshold>
        <spotbugs.version>*******</spotbugs.version>

        <activation.version>2.1.1</activation.version>
        <maven.api.version>3.9.1</maven.api.version>
        <maven.resolver.version>1.9.7</maven.resolver.version>
        <junit.version>4.13.2</junit.version>
        <ant.version>1.10.13</ant.version>
        <codemodel.version>4.0.2</codemodel.version>
        <plexus-utils.version>3.5.1</plexus-utils.version>
        <maven-plugin-annotations.version>3.8.1</maven-plugin-annotations.version>
        <testng.version>7.7.1</testng.version>
        <args4j.version>2.33</args4j.version>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>${project.build.sourceEncoding}</project.reporting.outputEncoding>
        <maven.compiler.release>11</maven.compiler.release>
        <maven.compiler.testRelease>${maven.compiler.release}</maven.compiler.testRelease>
        <vendor.name>Eclipse Foundation</vendor.name>
    </properties>

    <modules>
        <module>buildtools</module>
        <module>runtime</module>
        <module>test</module>
        <module>tools</module>
        <module>maven-plugin</module>
        <module>import-properties-plugin</module>
        <module>soimp</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>jakarta.activation</groupId>
                <artifactId>jakarta.activation-api</artifactId>
                <version>${activation.version}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.ant</groupId>
                <artifactId>ant</artifactId>
                <version>${ant.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.ant</groupId>
                <artifactId>ant-junit</artifactId>
                <version>${ant.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>codemodel</artifactId>
                <version>${codemodel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-plugin-api</artifactId>
                <version>${maven.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-core</artifactId>
                <version>${maven.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-artifact</artifactId>
                <version>${maven.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-model</artifactId>
                <version>${maven.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.codehaus.plexus</groupId>
                <artifactId>plexus-utils</artifactId>
                <version>${plexus-utils.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.maven.plugin-tools</groupId>
                <artifactId>maven-plugin-annotations</artifactId>
                <version>${maven-plugin-annotations.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>${testng.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-settings</artifactId>
                <version>${maven.api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.maven.resolver</groupId>
                <artifactId>maven-resolver-api</artifactId>
                <version>${maven.resolver.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.maven.resolver</groupId>
                <artifactId>maven-resolver-impl</artifactId>
                <version>${maven.resolver.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>args4j</groupId>
                <artifactId>args4j</artifactId>
                <version>${args4j.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>buildnumber-maven-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <plugin>
                    <groupId>com.github.spotbugs</groupId>
                    <artifactId>spotbugs-maven-plugin</artifactId>
                    <version>${spotbugs.version}</version>
                    <configuration>
                        <skip>${spotbugs.skip}</skip>
                        <threshold>${spotbugs.threshold}</threshold>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.2.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>5.1.8</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.5.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>3.5.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.5.0</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.gmaven</groupId>
                    <artifactId>gmaven-plugin</artifactId>
                    <version>1.5</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.apache.groovy</groupId>
                            <artifactId>groovy</artifactId>
                            <version>4.0.11</version>
                        </dependency>
                        <dependency>
                            <groupId>org.apache.groovy</groupId>
                            <artifactId>groovy-xml</artifactId>
                            <version>4.0.11</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-invoker-plugin</artifactId>
                    <version>3.5.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-plugin-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.9</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-maven</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireJavaVersion>
                                    <version>[${maven.compiler.release},)</version>
                                </requireJavaVersion>
                                <requireMavenVersion>
                                    <version>[3.6.0,)</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <configuration>
                    <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                    <revisionOnScmFailure>false</revisionOnScmFailure>
                    <shortRevisionLength>7</shortRevisionLength>
                </configuration>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>create</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.glassfish.copyright</groupId>
                <artifactId>glassfish-copyright-maven-plugin</artifactId>
                <configuration>
                    <excludeFile>${copyright.exclude}</excludeFile>
                    <!-- skip files not under SCM-->
                    <scmOnly>${copyright.scmonly}</scmOnly>
                    <!-- for use with repair -->
                    <update>${copyright.update}</update>
                    <!-- check that year is correct -->
                    <ignoreYear>${copyright.ignoreyear}</ignoreYear>
                    <quiet>false</quiet>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <id>common-resources</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <inherited>false</inherited>
                        <configuration>
                            <descriptors>
                                <descriptor>src/main/assembly/resources.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>unpack-resource</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>${project.groupId}</groupId>
                                    <artifactId>istack-commons</artifactId>
                                    <version>${project.version}</version>
                                    <classifier>resources</classifier>
                                    <type>zip</type>
                                    <outputDirectory>${project.build.commonResourcesDirectory}</outputDirectory>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                        <arg>-Xdoclint:all,-missing</arg>
                        <arg>-Werror</arg>
                    </compilerArgs>
                    <showDeprecation>true</showDeprecation>
                    <createMissingPackageInfoClass>false</createMissingPackageInfoClass>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultEntries>false</addDefaultEntries>
                        </manifest>
                    </archive>
                    <instructions>
                        <Implementation-Vendor>${vendor.name}</Implementation-Vendor>
                        <Implementation-Vendor-Id>${project.groupId}</Implementation-Vendor-Id>
                        <Implementation-Build-Id>${project.version} - ${buildNumber}</Implementation-Build-Id>
                    </instructions>
                    <niceManifest>true</niceManifest>
                    <noWarningProjectTypes>
                        <noWarningProjectType>pom</noWarningProjectType>
                        <noWarningProjectType>maven-plugin</noWarningProjectType>
                    </noWarningProjectTypes>
                </configuration>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultEntries>false</addDefaultEntries>
                        </manifest>
                    </archive>
                    <excludes>
                        <exclude>META-INF/jpms.args</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <notimestamp>true</notimestamp>
                    <doclint>all,-missing</doclint>
                    <quiet>true</quiet>
                    <failOnWarnings>true</failOnWarnings>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addDefaultEntries>false</addDefaultEntries>
                        </manifest>
                        <manifestEntries>
                            <Implementation-Build-Id>${project.version} - ${buildNumber}</Implementation-Build-Id>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <excludeFilterFile>${spotbugs.exclude}</excludeFilterFile>
                    <failThreshold>High</failThreshold>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-plugin-plugin</artifactId>
                <configuration>
                    <extractors>
                        <extractor>java-annotations</extractor>
                    </extractors>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>coverage</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>default-prepare-agent</id>
                                <goals>
                                    <goal>prepare-agent</goal>
                                </goals>
                            </execution>
                            <execution>
                                <id>default-report</id>
                                <goals>
                                    <goal>report</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>license-check</id>
            <pluginRepositories>
                <pluginRepository>
                    <!-- org.eclipse.dash:license-tool-plugin is nor final nor in central yet -->
                    <id>dash-licenses-snapshots</id>
                    <url>https://repo.eclipse.org/content/repositories/dash-licenses-snapshots/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.eclipse.dash</groupId>
                            <artifactId>license-tool-plugin</artifactId>
                            <version>0.0.1-SNAPSHOT</version>
                        </plugin>
                    </plugins>
                </pluginManagement>
                <plugins>
                    <plugin>
                        <groupId>org.eclipse.dash</groupId>
                        <artifactId>license-tool-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>license-check</id>
                                <phase>validate</phase>
                                <goals>
                                    <goal>license-check</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
