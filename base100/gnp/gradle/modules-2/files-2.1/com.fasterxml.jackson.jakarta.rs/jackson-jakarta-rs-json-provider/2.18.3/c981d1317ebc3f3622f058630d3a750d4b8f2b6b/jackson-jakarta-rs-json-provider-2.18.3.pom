<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson.jakarta.rs</groupId>
    <artifactId>jackson-jakarta-rs-providers</artifactId>
    <version>2.18.3</version>
  </parent>
  <artifactId>jackson-jakarta-rs-json-provider</artifactId>
  <name>Jackson Jakarta-RS: JSON</name>
  <packaging>bundle</packaging>
  <description>Functionality to handle JSON input/output for Jakarta-RS implementations
(like Jersey and RESTeasy) using standard Jackson data binding.
  </description>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <properties>
    <!-- Generate PackageVersion.java into this directory. -->
    <packageVersion.dir>com/fasterxml/jackson/jakarta/rs/json</packageVersion.dir>
    <packageVersion.package>${project.groupId}.json</packageVersion.package>
    <osgi.export>${project.groupId}.json.*;version=${project.version}</osgi.export>
    <!-- NOTE: Jakarta XmlBind annotations module is optional dependency, need to try to mark
         as such here.
      -->
    <osgi.import>jakarta.ws.rs;version="${jakarta.ws.rs.version}"
,jakarta.ws.rs.core;version="${jakarta.ws.rs.version}"
,jakarta.ws.rs.ext;version="${jakarta.ws.rs.version}"
,com.fasterxml.jackson.module.jakarta.xmlbind;resolution:=optional
,*
</osgi.import>
  </properties>

  <dependencies>
    <!-- builds on shared base Jakarta-RS handling code... -->
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>jackson-jakarta-rs-base</artifactId>
      <version>${project.version}</version>
    </dependency>
    <!-- also need Jakarta XmlBind annotation support -->
    <dependency> 
      <groupId>com.fasterxml.jackson.module</groupId>
      <artifactId>jackson-module-jakarta-xmlbind-annotations</artifactId>
    </dependency>

    <!-- test deps should mostly come from parent, including jersey,
         but here we also want to ensure other Jakarta-RS impls can
         at least load jackson provider
      -->

      <!-- 29-May-2021, tatu: Still trying to see that RESTeasy can be used
             although not sure if tests actually verify functioning...
        -->
    <dependency> 
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-jackson2-provider</artifactId>
      <version>4.7.9.Final</version>
      <scope>test</scope>
<!--
      <exclusions>
        <exclusion>
          <groupId>${project.groupId}</groupId>
          <artifactId>jackson-jaxrs-json-provider</artifactId>
        </exclusion>
      </exclusions>
-->
    </dependency>
    <!-- 30-May-2021, tatu: This is now needed with Jakarta/2.13, as per
https://stackoverflow.com/questions/44088493/jersey-stopped-working-with-injectionmanagerfactory-not-found
      -->
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-client</artifactId>
      <version>4.7.9.Final</version>
      <scope>test</scope>
<!--
      <exclusions>
        <exclusion>
          <groupId>${project.groupId}</groupId>
          <artifactId>jackson-jaxrs-json-provider</artifactId>
        </exclusion>
      </exclusions>
-->
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <!-- Inherited from oss-base. Generate PackageVersion.java.-->
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>process-packageVersion</id>
            <phase>process-sources</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <id>no-meta-inf-services</id>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <classifier>no-metainf-services</classifier>
              <excludes>
                <exclude>META-INF/services/**</exclude>
              </excludes>
              <archive>
                <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
              </archive>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.moditect</groupId>
        <artifactId>moditect-maven-plugin</artifactId>
      </plugin>

    </plugins>
  </build>
</project>
