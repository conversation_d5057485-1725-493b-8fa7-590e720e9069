<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>oss-parent</artifactId>
    <groupId>com.fasterxml</groupId>
    <version>50</version>
    <relativePath>../pom.xml/pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.fasterxml.woodstox</groupId>
  <artifactId>woodstox-core</artifactId>
  <packaging>bundle</packaging>
  <name>Woodstox</name>
  <version>6.5.1</version>
  <description>Woodstox is a high-performance XML processor that implements Stax (JSR-173),
SAX2 and Stax2 APIs</description>
  <url>https://github.com/FasterXML/woodstox</url>
  <issueManagement>
    <url>https://github.com/FasterXML/woodstox/issues</url>
  </issueManagement>
  <developers>
    <developer>
      <id>cowtowncoder</id>
      <name>Tatu Saloranta</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <connection>scm:git:**************:FasterXML/woodstox.git</connection>
    <developerConnection>scm:git:**************:FasterXML/woodstox.git</developerConnection>
    <tag>woodstox-core-6.5.1</tag>
    <url>https://github.com/FasterXML/woodstox</url>
  </scm>
  <organization>
    <name>FasterXML</name>
    <url>http://fasterxml.com</url>
  </organization>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <version>1.6.6</version>
        <extensions>true</extensions>
        <configuration>
          <serverId>sonatype-nexus-staging</serverId>
          <nexusUrl>https://oss.sonatype.org/</nexusUrl>
          <stagingProfileId>b34f19b9cc6224</stagingProfileId>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.6</source>
          <target>1.6</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>failing/*.java</exclude>
            <exclude>**/Abstract*.java</exclude>
            <exclude>**/Base*.java</exclude>
            <exclude>**/*$*.java</exclude>
          </excludes>
          <includes>
            <include>**/*Test.java</include>
            <include>**/Test*.java</include>
          </includes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <extensions>true</extensions>
        <configuration>
          <instructions>
            <_removeheaders>Require-Capability</_removeheaders>
          </instructions>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <minimizeJar>false</minimizeJar>
              <artifactSet>
                <includes>
                  <include>net.java.dev.msv:msv-core</include>
                  <include>net.java.dev.msv:xsdlib</include>
                  <include>relaxngDatatype:relaxngDatatype</include>
                  <include>com.sun.xml.bind.jaxb:isorelax</include>
                </includes>
              </artifactSet>
              <filters>
                <filter>
                  <artifact>net.java.dev.msv:msv-core</artifact>
                  <includes>
                    <include>com/sun/msv/**</include>
                  </includes>
                  <excludes>
                    <exclude>com/sun/msv/driver/textui/DebugController*</exclude>
                    <exclude>com/sun/msv/driver/textui/Driver*</exclude>
                    <exclude>com/sun/msv/driver/textui/Messages*</exclude>
                  </excludes>
                </filter>
              </filters>
              <relocations>
                <relocation>
                  <pattern>com.sun.msv.xsd</pattern>
                  <shadedPattern>com.ctc.wstx.shaded.msv.xsd</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>com.sun.xml.util</pattern>
                  <shadedPattern>com.ctc.wstx.shaded.msv.xsd_util</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.relaxng.datatype</pattern>
                  <shadedPattern>com.ctc.wstx.shaded.msv.relaxng_datatype</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.iso_relax</pattern>
                  <shadedPattern>com.ctc.wstx.shaded.msv.org_isorelax</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>jp.gr.xml.relax</pattern>
                  <shadedPattern>com.ctc.wstx.shaded.msv.org_jp_gr_xml</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>com.sun.msv</pattern>
                  <shadedPattern>com.ctc.wstx.shaded.msv_core</shadedPattern>
                </relocation>
              </relocations>
              <transformers>
                <transformer />
                <transformer>
                  <manifestEntries>
                    <Require-Capability>osgi.extender;filter:="(&amp;(osgi.extender=osgi.serviceloader.registrar)(version&gt;=1.0.0)(!(version&gt;=2.0.0)))";resolution:=optional,osgi.ee;filter:="(&amp;(osgi.ee=JavaSE)(version=1.6))"</Require-Capability>
                  </manifestEntries>
                </transformer>
              </transformers>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.moditect</groupId>
        <artifactId>moditect-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>add-module-infos</id>
            <phase>package</phase>
            <goals>
              <goal>add-module-info</goal>
            </goals>
            <configuration>
              <overwriteExistingFiles>true</overwriteExistingFiles>
              <module>
                <moduleInfoFile>src/moditect/module-info.java</moduleInfoFile>
              </module>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>org.codehaus.woodstox</groupId>
      <artifactId>stax2-api</artifactId>
      <version>4.2.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>stax-api</artifactId>
          <groupId>javax.xml.stream</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>osgi.core</artifactId>
      <version>5.0.0</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>biz.aQute.bnd</groupId>
      <artifactId>biz.aQute.bnd.annotation</artifactId>
      <version>6.4.0</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>hamcrest-core</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <reporting>
    <plugins>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <show>private</show>
          <serialwarn>true</serialwarn>
          <author>true</author>
          <doctitle>${project.name} ${project.version} API</doctitle>
          <windowtitle>${project.name} ${project.version} API</windowtitle>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-report-plugin</artifactId>
      </plugin>
    </plugins>
  </reporting>
  <properties>
    <version.msv>2013.6.1</version.msv>
    <javac.target.version>1.6</javac.target.version>
    <version.bnd.annotation>6.4.0</version.bnd.annotation>
    <osgi.export>com.ctc.wstx.*;version=${project.version}</osgi.export>
    <javac.src.version>1.6</javac.src.version>
    <version.plugin.javadoc>3.1.1</version.plugin.javadoc>
  </properties>
</project>
