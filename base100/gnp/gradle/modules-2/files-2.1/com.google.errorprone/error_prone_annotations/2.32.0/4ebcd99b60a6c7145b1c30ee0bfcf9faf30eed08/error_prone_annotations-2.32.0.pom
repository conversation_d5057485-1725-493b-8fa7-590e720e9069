<?xml version="1.0" encoding="UTF-8"?>
<!--
  Copyright 2015 The Error Prone Authors.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.google.errorprone</groupId>
    <artifactId>error_prone_parent</artifactId>
    <version>2.32.0</version>
  </parent>

  <name>error-prone annotations</name>
  <artifactId>error_prone_annotations</artifactId>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <licenses>
    <license>
      <name>Apache 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <compilerArgs combine.self="override" />
        </configuration>
        <executions>
          <execution>
            <id>default-compile</id>
            <configuration>
              <source>1.8</source>
              <target>1.8</target>
              <excludes>
                <exclude>module-info.java</exclude>
              </excludes>
            </configuration>
          </execution>
          <execution>
            <id>compile-java9</id>
            <goals>
              <goal>compile</goal>
            </goals>
            <configuration>
              <source>9</source>
              <target>9</target>
              <release>9</release>
              <multiReleaseOutput>true</multiReleaseOutput>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestEntries>
              <Multi-Release>true</Multi-Release>
            </manifestEntries>
          </archive>
          <excludes>
            <exclude>/META-INF/versions/9/com/**/*.class</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>biz.aQute.bnd</groupId>
        <artifactId>bnd-maven-plugin</artifactId>
        <version>6.4.0</version>
        <executions>
          <execution>
            <id>generate-OSGi-manifest</id>
            <phase>none</phase>
          </execution>
          <execution>
            <id>generate-OSGi-manifest-annotations</id>
            <goals>
              <goal>bnd-process</goal>
            </goals>
            <configuration>
              <!--
                The OSGi bundle build is overridden here to allow for MRJAR classes in the
                versioned class space underneath META-INF.

                The `annotations` module also should not have an `Automatic-Module-Name`.
                Otherwise, these flags should stay in-sync with the same block in the root
                `pom.xml`.
              -->
              <bnd><![CDATA[
                Bundle-SymbolicName: com.google.errorprone.annotations
                -exportcontents: com.google.errorprone*,!META-INF.*
                -noextraheaders: true
                -removeheaders: Private-Package
                -fixupmessages: ^Classes found in the wrong directory: .*
              ]]></bnd>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
