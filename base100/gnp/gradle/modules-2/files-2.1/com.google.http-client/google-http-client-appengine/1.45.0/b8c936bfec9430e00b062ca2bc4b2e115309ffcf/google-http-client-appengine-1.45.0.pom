<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.google.http-client</groupId>
    <artifactId>google-http-client-parent</artifactId>
    <version>1.45.0</version><!-- {x-version-update:google-http-client-parent:current} -->
    <relativePath>../pom.xml</relativePath>
  </parent>
  <artifactId>google-http-client-appengine</artifactId>
  <version>1.45.0</version><!-- {x-version-update:google-http-client-appengine:current} -->
  <name>Google App Engine extensions to the Google HTTP Client Library for Java.</name>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <links>
            <link>http://download.oracle.com/javase/7/docs/api/</link>
            <link>https://cloud.google.com/appengine/docs/standard/java/javadoc/</link>
          </links>
          <doctitle>${project.name} ${project.version}</doctitle>
          <windowtitle>${project.artifactId} ${project.version}</windowtitle>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>
      <!--App Engine uses Java 7 and above-->
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>java17</artifactId>
            <version>1.0</version>
          </signature>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <configuration>
          <!-- This artifact is needed by appengine-testing, but is not declared as a dependency -->
          <ignoredUnusedDeclaredDependencies>com.google.appengine:appengine-api-stubs</ignoredUnusedDeclaredDependencies>
        </configuration>
      </plugin>
       <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>  
            <manifestEntries>
              <Automatic-Module-Name>com.google.api.client.extensions.appengine</Automatic-Module-Name>
            </manifestEntries>
          </archive> 
        </configuration>
      </plugin> 
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.appengine</groupId>
      <artifactId>appengine-api-1.0-sdk</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.google.appengine</groupId>
      <artifactId>appengine-testing</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.appengine</groupId>
      <artifactId>appengine-api-stubs</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
