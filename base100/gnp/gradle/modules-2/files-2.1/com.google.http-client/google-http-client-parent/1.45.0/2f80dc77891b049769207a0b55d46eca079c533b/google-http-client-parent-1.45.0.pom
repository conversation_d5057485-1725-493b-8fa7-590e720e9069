<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.http-client</groupId>
  <artifactId>google-http-client-parent</artifactId>
  <version>1.45.0</version><!-- {x-version-update:google-http-client-parent:current} -->
  <packaging>pom</packaging>
  <name>Parent for the Google HTTP Client Library for Java</name>
  <description>Google HTTP Client Library for Java</description>

  <url>https://github.com/googleapis/google-http-java-client</url>

  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/googleapis/google-http-java-client/issues</url>
  </issueManagement>

  <inceptionYear>2011</inceptionYear>

  <scm>
    <connection>scm:git:https://github.com/googleapis/google-http-java-client.git</connection>
    <developerConnection>scm:git:**************:googleapis/google-http-java-client.git</developerConnection>
    <url>https://github.com/googleapis/google-http-java-client</url>
  </scm>

  <organization>
    <name>Google</name>
    <url>https://www.google.com/</url>
  </organization>

  <developers>
    <developer>
      <id>chingor</id>
      <name>Jeff Ching</name>
      <email><EMAIL></email>
    </developer>
  </developers>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <!--
    If you add a new module, make sure to also add it in the following places:
    * below in the dependencyManagement and maven-javadoc-plugin sections
    * google-http-client-assembly/classpath-include
    * google-http-client-assembly/pom.xml
    * google-http-client-assembly/readme.html
    * google-http-client-assembly/dependencies/<name>-dependencies.html
        (use mvn project-info-reports:dependencies and copy from
        google-http-client-<name>/target/site/dependencies.html)
    * google-http-client-assembly/android-properties/*.properties
  -->
  <modules>
    <module>google-http-client</module>
    <module>google-http-client-assembly</module>
    <module>google-http-client-appengine</module>
    <module>google-http-client-android</module>
    <module>google-http-client-apache-v2</module>
    <module>google-http-client-apache-v5</module>
    <module>google-http-client-protobuf</module>
    <module>google-http-client-gson</module>
    <module>google-http-client-jackson2</module>
    <module>google-http-client-xml</module>

    <module>google-http-client-findbugs</module>
    <module>google-http-client-test</module>
    <module>samples/dailymotion-simple-cmdline-sample</module>

    <!-- A deployable artifact must be last or deploys are skipped -->
    <module>google-http-client-bom</module>
  </modules>

  <pluginRepositories>
    <pluginRepository>
      <releases>
        <updatePolicy>never</updatePolicy>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>central</id>
      <name>Central Repository</name>
      <url>https://repo.maven.apache.org/maven2</url>
    </pluginRepository>
  </pluginRepositories>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>native-image-shared-config</artifactId>
    <version>1.11.0</version>
  </parent>

  <!--
    If you change the version of a dependency, make sure to update the javadoc
    links if required, and any javadoc links of your dependency in the oauth and
    api projects as well.
  -->
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.codehaus.jackson</groupId>
        <artifactId>jackson-core-asl</artifactId>
        <version>${project.jackson-core-asl.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>${project.jackson-core2.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
        <version>${project.gson.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.google.errorprone</groupId>
            <artifactId>error_prone_annotations</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.13.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.truth</groupId>
        <artifactId>truth</artifactId>
        <version>1.1.3</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.google.errorprone</groupId>
        <artifactId>error_prone_annotations</artifactId>
        <version>2.30.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-api-1.0-sdk</artifactId>
        <version>${project.appengine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-testing</artifactId>
        <version>${project.appengine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-api-stubs</artifactId>
        <version>${project.appengine.version}</version>
      </dependency>
      <dependency>
        <groupId>xpp3</groupId>
        <artifactId>xpp3</artifactId>
        <version>${project.xpp3.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>${project.apache-httpclient-4.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore</artifactId>
        <version>${project.apache-httpcore-4.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5</artifactId>
        <version>${project.apache-httpclient-5.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5</artifactId>
        <version>${project.apache-httpcore-5.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${project.guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava-testlib</artifactId>
        <version>${project.guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.findbugs</groupId>
        <artifactId>jsr305</artifactId>
        <version>${project.jsr305.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-java</artifactId>
        <version>${project.protobuf-java.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client</artifactId>
        <version>${project.http-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-apache-v2</artifactId>
        <version>${project.http-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-appengine</artifactId>
        <version>${project.http-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-android</artifactId>
        <version>${project.http-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-protobuf</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-gson</artifactId>
        <version>${project.http-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-jackson2</artifactId>
        <version>${project.http-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-xml</artifactId>
        <version>${project.http-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-findbugs</artifactId>
        <version>${project.http-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-test</artifactId>
        <version>${project.http-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.j2objc</groupId>
        <artifactId>j2objc-annotations</artifactId>
        <version>3.0.0</version>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-context</artifactId>
        <version>1.66.0</version>
      </dependency>
      <dependency>
        <groupId>io.opencensus</groupId>
        <artifactId>opencensus-api</artifactId>
        <version>${project.opencensus.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opencensus</groupId>
        <artifactId>opencensus-contrib-http-util</artifactId>
        <version>${project.opencensus.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opencensus</groupId>
        <artifactId>opencensus-impl</artifactId>
        <version>${project.opencensus.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opencensus</groupId>
        <artifactId>opencensus-testing</artifactId>
        <version>${project.opencensus.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <!-- This is the parent, so only define pluginManagement, not plugins. -->
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.sonatype.plugins</groupId>
          <artifactId>nexus-staging-maven-plugin</artifactId>
          <version>1.7.0</version>
          <extensions>true</extensions>
          <configuration>
            <serverId>ossrh</serverId>
            <nexusUrl>https://google.oss.sonatype.org/</nexusUrl>
            <autoReleaseAfterClose>${deploy.autorelease}</autoReleaseAfterClose>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>3.7.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.13.0</version>
          <configuration>
            <source>1.7</source>
            <target>1.7</target>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>3.1.3</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.3.1</version>
          <executions>
            <execution>
              <id>attach-sources</id>
              <goals>
                <goal>jar-no-fork</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>3.8.0</version>
          <executions>
            <execution>
              <id>attach-javadocs</id>
              <goals>
                <goal>jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.4.2</version>
          <executions>
            <execution>
              <goals>
                <goal>test-jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${project.surefire.version}</version>
          <configuration>
            <argLine>-Xmx1024m</argLine>
            <reportNameSuffix>sponge_log</reportNameSuffix>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>3.4.0</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>findbugs-maven-plugin</artifactId>
          <version>3.0.5</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>2.8</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.24</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>3.6.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.12.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>3.7.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>3.3.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>3.5.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.3.1</version>
          <executions>
            <execution>
              <id>attach-sources</id>
              <goals>
                <goal>jar-no-fork</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.5.0</version><executions>
        <execution>
          <id>enforce-maven</id>
          <goals>
            <goal>enforce</goal>
          </goals>
          <configuration>
            <rules>
              <requireMavenVersion>
                <version>[3.5.2,4.0.0)</version>
              </requireMavenVersion>
              <requireJavaVersion>
                <version>[1.7,)</version>
              </requireJavaVersion>
              <requireUpperBoundDeps/>
            </rules>
          </configuration>
        </execution>
      </executions>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <doclint>none</doclint>
          <source>7</source>
        </configuration>
        <executions>
          <execution>
            <id>aggregate</id>
            <goals>
              <goal>aggregate</goal>
            </goals>
            <phase>site</phase>
            <configuration>
              <links>
                <link>https://download.oracle.com/javase/7/docs/api/</link>
                <link>https://cloud.google.com/appengine/docs/java/javadoc</link>
                <link>https://static.javadoc.io/doc/com.google.code.gson/gson/${project.gson.version}</link>
                <link>https://google.github.io/guava/releases/${project.guava.version}/api/docs/</link>
              </links>
              <doctitle>Google HTTP Client Library for Java ${project.version}</doctitle>
              <excludePackageNames>com.google.api.client.findbugs:com.google.api.client.test.:com.google.api.services</excludePackageNames>
              <overview>${basedir}/overview.html</overview>
              <groups>
                <group>
                  <title>google-http-client</title>
                  <packages>com.google.api.client.http*:com.google.api.client.json*:com.google.api.client.testing*:com.google.api.client.util*</packages>
                </group>
                <group>
                  <title>google-http-client-appengine</title>
                  <packages>com.google.api.client.extensions.appengine*</packages>
                </group>
                <group>
                  <title>google-http-client-android</title>
                  <packages>com.google.api.client.extensions.android*</packages>
                </group>
                <group>
                  <title>google-http-client-protobuf</title>
                  <packages>com.google.api.client.protobuf*:com.google.api.client.http.protobuf*</packages>
                </group>
                <group>
                  <title>google-http-client-gson</title>
                  <packages>com.google.api.client.json.gson*</packages>
                </group>
                <group>
                  <title>google-http-client-jackson2</title>
                  <packages>com.google.api.client.json.jackson2.*</packages>
                </group>
                <group>
                  <title>google-http-client-xml</title>
                  <packages>com.google.api.client.xml*:com.google.api.client.http.xml*</packages>
                </group>
              </groups>
              <windowtitle>google-http-java-client ${project.version}</windowtitle>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <configuration>
          <excludeFilterFile>${basedir}/../findbugs-exclude.xml</excludeFilterFile>
          <plugins>
            <plugin>
              <groupId>com.google.http-client</groupId>
              <artifactId>google-http-client-findbugs</artifactId>
              <version>${project.http-client.version}</version>
            </plugin>
          </plugins>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <configuration>
          <ignoredDifferencesFile>clirr-ignored-differences.xml</ignoredDifferencesFile>
          <logResults>true</logResults>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>java7</id>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <signature>
                <groupId>org.codehaus.mojo.signature</groupId>
                <artifactId>java17</artifactId>
                <version>1.0</version>
              </signature>
            </configuration>
          </execution>
          <execution>
            <id>android</id>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <signature>
                <groupId>net.sf.androidscents.signature</groupId>
                <artifactId>android-api-level-19</artifactId>
                <version>4.4.2_r4</version>
              </signature>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <!-- Build the dependencies report at package time (needed for the assembly artifact). -->
      <plugin>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>3.6.2</version>
        <executions>
          <execution>
            <goals>
              <goal>dependencies</goal>
            </goals>
            <phase>package</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>com.coveo</groupId>
        <artifactId>fmt-maven-plugin</artifactId>
        <version>2.9</version>
        <configuration>
          <style>google</style>
          <verbose>true</verbose>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>com.google.googlejavaformat</groupId>
            <artifactId>google-java-format</artifactId>
            <version>1.7</version>
          </dependency>
        </dependencies>
      </plugin>
    </plugins>
  </build>

  <properties>
    <!--
      NOTE: if you make a change to these versions, you MUST make the same change to:
      - google-http-client-assembly/android-properties (make the filenames match the version here)
      - google-oauth-java-client/pom.xml (versions must match)
      - google-oauth-java-client/google-oauth-client-assembly/android-properties (make the filenames match the version here)
      - google-api-java-client/pom.xml (versions must match)
      - google-api-java-client/google-api-client-assembly/android-properties (make the filenames match the version here)
      - Internally, update the default features.json file
    -->
    <project.http-client.version>1.45.0</project.http-client.version><!-- {x-version-update:google-http-client-parent:current} -->
    <project.appengine.version>2.0.29</project.appengine.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.jsr305.version>3.0.2</project.jsr305.version>
    <project.gson.version>2.11.0</project.gson.version>
    <project.jackson-core2.version>2.17.2</project.jackson-core2.version>
    <project.protobuf-java.version>3.21.12</project.protobuf-java.version>
    <project.guava.version>30.1.1-android</project.guava.version>
    <project.xpp3.version>1.1.4c</project.xpp3.version>
    <project.apache-httpclient-4.version>4.5.14</project.apache-httpclient-4.version>
    <project.apache-httpcore-4.version>4.4.16</project.apache-httpcore-4.version>
    <project.apache-httpclient-5.version>5.3.1</project.apache-httpclient-5.version>
    <project.apache-httpcore-5.version>5.2.5</project.apache-httpcore-5.version>
    <project.opencensus.version>0.31.1</project.opencensus.version>
    <project.root-directory>..</project.root-directory>
    <project.surefire.version>3.4.0</project.surefire.version>
    <deploy.autorelease>false</deploy.autorelease>
  </properties>

  <profiles>
    <profile>
      <id>java21</id>
      <activation>
        <jdk>[21,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>3.13.0</version>
            <configuration>
              <source>1.8</source>
              <target>1.8</target>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.8.0</version>
            <configuration>
              <source>1.8</source>
              <failOnError>false</failOnError>
            </configuration>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>native-tests</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>${surefire.version}</version>
            <configuration>
              <!-- Include all tests during native image testing. -->
              <includes combine.self="override">
                <include>**/*Test</include>
              </includes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>Windows</id>
      <activation>
        <os>
          <family>windows</family>
        </os>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <configuration>
              <skip>true</skip>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>release-sign-artifacts</id>
      <activation>
        <property>
          <name>performRelease</name>
          <value>true</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>3.2.5</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
                <configuration>
                  <gpgArguments>
                    <arg>--pinentry-mode</arg>
                    <arg>loopback</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!-- set project.root-directory property based on where we are -->
    <profile>
      <id>root-directory</id>
      <activation>
        <file>
          <exists>checkstyle-suppressions.xml</exists>
        </file>
      </activation>
      <properties>
        <project.root-directory>.</project.root-directory>
      </properties>
    </profile>

    <profile>
      <!-- Only run checkstyle plugin on Java 8+ (checkstyle artifact only supports Java 8+) -->
      <id>checkstyle-tests</id>
      <activation>
        <jdk>[1.8,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <dependencies>
              <dependency>
                <groupId>com.puppycrawl.tools</groupId>
                <artifactId>checkstyle</artifactId>
                <version>9.3</version>
              </dependency>
            </dependencies>
            <configuration>
              <configLocation>checkstyle.xml</configLocation>
              <consoleOutput>true</consoleOutput>
              <suppressionsLocation>checkstyle-suppressions.xml</suppressionsLocation>
            </configuration>
            <executions>
              <execution>
                <goals>
                  <goal>check</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>docFX</id>
      <activation>
        <property>
          <!-- Activate with -P docFX -->
          <name>docFX</name>
        </property>
      </activation>
      <properties>
        <!-- default config values -->
        <outputpath>${project.build.directory}/docfx-yml</outputpath>
        <projectname>${project.artifactId}</projectname>
        <excludePackages>com\.google\.api\.client\.findbugs:com\.google\.api\.client\.test:com\.google\.api\.services</excludePackages>
        <source>8</source>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.8.0</version>
            <configuration>
              <doclet>com.microsoft.doclet.DocFxDoclet</doclet>
              <useStandardDocletOptions>false</useStandardDocletOptions>
               <!-- custom config with -Dproperty=value -->
              <additionalOptions>
                -outputpath ${outputpath} 
                -projectname ${projectname}
                -excludepackages ${excludePackages}:
              </additionalOptions>
              <doclint>none</doclint>
              <show>protected</show>
              <nohelp>true</nohelp>
              <source>${source}</source>
            </configuration>
          </plugin>  
        </plugins>
      </build>
    </profile>    
  </profiles>
</project>
