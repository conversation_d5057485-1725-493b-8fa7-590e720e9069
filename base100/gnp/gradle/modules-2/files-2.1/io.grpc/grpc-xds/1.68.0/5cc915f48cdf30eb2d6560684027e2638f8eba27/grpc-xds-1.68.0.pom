<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.grpc</groupId>
  <artifactId>grpc-xds</artifactId>
  <version>1.68.0</version>
  <name>io.grpc:grpc-xds</name>
  <description>gRPC: XDS plugin</description>
  <url>https://github.com/grpc/grpc-java</url>
  <licenses>
    <license>
      <name>Apache 2.0</name>
      <url>https://opensource.org/licenses/Apache-2.0</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>grpc.io</id>
      <name>gRPC Contributors</name>
      <email><EMAIL></email>
      <url>https://grpc.io/</url>
      <organization>gRPC Authors</organization>
      <organizationUrl>https://www.google.com</organizationUrl>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/grpc/grpc-java.git</connection>
    <developerConnection>scm:git:**************:grpc/grpc-java.git</developerConnection>
    <url>https://github.com/grpc/grpc-java</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-protobuf</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-stub</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-core</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-util</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-services</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-auth</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-alts</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>33.2.1-android</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.11.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.re2j</groupId>
      <artifactId>re2j</artifactId>
      <version>1.7</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.auto.value</groupId>
      <artifactId>auto-value-annotations</artifactId>
      <version>1.11.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java-util</artifactId>
      <version>3.25.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-netty-shaded</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
