<?xml version="1.0" encoding="UTF-8"?>
<!--
  Copyright 2012 Google LLC

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.google.auto.value</groupId>
    <artifactId>auto-value-parent</artifactId>
    <version>1.11.0</version>
  </parent>

  <artifactId>auto-value-annotations</artifactId>
  <version>1.11.0</version>
  <name>AutoValue Annotations</name>
  <description>
    Immutable value-type code generation for Java 8+.
  </description>
  <url>https://github.com/google/auto/tree/main/value</url>

  <properties>
    <java.version>1.8</java.version>
  </properties>

  <scm>
    <url>http://github.com/google/auto</url>
    <connection>scm:git:git://github.com/google/auto.git</connection>
    <developerConnection>scm:git:ssh://**************/google/auto.git</developerConnection>
    <tag>HEAD</tag>
  </scm>
  <build>
    <sourceDirectory>../src/main/java</sourceDirectory>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <includes>
            <include>com/google/auto/value/*</include>
            <include>com/google/auto/value/extension/memoized/*</include>
            <include>com/google/auto/value/extension/serializable/*</include>
            <include>com/google/auto/value/extension/toprettystring/*</include>
          </includes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-invoker-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>disable-java8-doclint</id>
      <activation>
        <jdk>[1.8,)</jdk>
      </activation>
      <properties>
        <additionalparam>-Xdoclint:none</additionalparam>
      </properties>
    </profile>
  </profiles>
</project>
