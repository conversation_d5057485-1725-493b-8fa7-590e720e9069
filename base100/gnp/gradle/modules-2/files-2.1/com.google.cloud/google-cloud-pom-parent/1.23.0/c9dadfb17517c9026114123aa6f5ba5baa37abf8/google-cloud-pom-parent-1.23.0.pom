<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" child.project.url.inherit.append.path="false">
  <modelVersion>4.0.0</modelVersion>
  <artifactId>google-cloud-pom-parent</artifactId>
  <groupId>com.google.cloud</groupId>
  <version>1.23.0</version><!-- {x-version-update:google-cloud-java:current} -->
  <packaging>pom</packaging>
  <name>Google Cloud POM Parent</name>
  <url>https://github.com/googleapis/google-cloud-java</url>
  <description>
    The top-level parent for all modules in the repository.
  </description>
  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-shared-config</artifactId>
    <version>1.6.0</version>
    <relativePath/>
  </parent>

  <properties>
    <skipUnitTests>false</skipUnitTests>
    <checkstyle.header.file>java.header</checkstyle.header.file>
  </properties>

  <developers>
    <developer>
      <id>suztomo</id>
      <name>Tomo Suzuki</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <organization>
    <name>Google LLC</name>
  </organization>
  <scm child.scm.connection.inherit.append.path="false"  child.scm.developerConnection.inherit.append.path="false"
       child.scm.url.inherit.append.path="false">
    <connection>scm:git:**************:googleapis/google-cloud-java.git</connection>
    <developerConnection>scm:git:**************:googleapis/google-cloud-java.git</developerConnection>
    <url>https://github.com/googleapis/google-cloud-java</url>
    <tag>HEAD</tag>
  </scm>
  <issueManagement>
    <url>https://github.com/googleapis/google-cloud-java/issues</url>
    <system>GitHub Issues</system>
  </issueManagement>

  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <profiles>
    <profile>
      <!-- Only run checkstyle plugin on Java 11+ (checkstyle artifact only supports Java 11+) -->
      <id>checkstyle-tests</id>
      <activation>
        <jdk>[11,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <executions>
              <execution>
                <id>checkstyle</id>
                <phase>validate</phase>
                <goals>
                  <goal>check</goal>
                </goals>
                <configuration>
                  <headerLocation>${checkstyle.header.file}</headerLocation>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>release-non-google-oss-sonatype</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.sonatype.plugins</groupId>
            <artifactId>nexus-staging-maven-plugin</artifactId>
            <configuration>
              <serverId>ossrh</serverId>
              <nexusUrl>https://oss.sonatype.org/</nexusUrl>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <repositories>
    <repository>
      <id>google-maven-central-copy</id>
      <name>Google Maven Central copy</name>
      <url>https://maven-central.storage-download.googleapis.com/maven2</url>
    </repository>
    <repository>
      <id>maven-central</id>
      <name>Maven Central</name>
      <url>https://repo1.maven.org/maven2</url>
    </repository>
  </repositories>
</project>
