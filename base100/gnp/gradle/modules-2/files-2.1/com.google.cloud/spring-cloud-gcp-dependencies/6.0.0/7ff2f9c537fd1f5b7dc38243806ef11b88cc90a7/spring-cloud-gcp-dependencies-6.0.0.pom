<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.google.cloud</groupId>
	<artifactId>spring-cloud-gcp-dependencies</artifactId>
	<version>6.0.0</version><!-- {x-version-update:spring-cloud-gcp:current} -->
	<name>Spring Framework on Google Cloud Dependencies</name>
	<description>Spring Framework on Google Cloud Dependencies</description>
	<url>https://spring.io/projects/spring-cloud-gcp</url>
	<packaging>pom</packaging>

	<scm>
		<url>https://github.com/GoogleCloudPlatform/spring-cloud-gcp</url>
		<connection>scm:git:git://github.com/GoogleCloudPlatform/spring-cloud-gcp.git</connection>
		<developerConnection>scm:git:ssh://**************/GoogleCloudPlatform/spring-cloud-gcp.git</developerConnection>
		<tag>HEAD</tag>
	</scm>

	<issueManagement>
		<system>GitHub Issues</system>
		<url>https://github.com/GoogleCloudPlatform/spring-cloud-gcp/issues</url>
	</issueManagement>

	<distributionManagement>
		<snapshotRepository>
			<id>ossrh</id>
			<url>https://google.oss.sonatype.org/content/repositories/snapshots</url>
		</snapshotRepository>
		<repository>
			<id>ossrh</id>
			<url>https://google.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
		</repository>
	</distributionManagement>

	<properties>
		<gcp-libraries-bom.version>26.54.0</gcp-libraries-bom.version>
		<cloud-sql-socket-factory.version>1.23.0</cloud-sql-socket-factory.version>
		<r2dbc-postgres-driver.version>1.0.7.RELEASE</r2dbc-postgres-driver.version>
		<cloud-spanner-r2dbc.version>1.3.0</cloud-spanner-r2dbc.version>
		<alloydb-jdbc-connector.version>1.1.8</alloydb-jdbc-connector.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-core</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-autoconfigure</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-cloudfoundry</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-pubsub</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-data-firestore</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-data-datastore</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-data-spanner</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-storage</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-logging</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-pubsub-stream-binder</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-security-iap</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-vision</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-bigquery</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-security-firebase</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-secretmanager</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-kms</artifactId>
				<version>${project.version}</version>
			</dependency>

			<!--Starters-->
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-bus-pubsub</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-cloudfoundry</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-pubsub</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-storage</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-data-spanner</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-data-datastore</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-data-firestore</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-firestore</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-sql-mysql</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-sql-postgresql</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-sql-mysql-r2dbc</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-sql-postgres-r2dbc</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-trace</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-logging</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-metrics</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-security-iap</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-security-firebase</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-vision</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-bigquery</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-secretmanager</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-kms</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-spanner-spring-data-r2dbc</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>spring-cloud-gcp-starter-alloydb</artifactId>
				<version>${project.version}</version>
			</dependency>

			<!-- spring-cloud-gcp-starter-alloydb -->
			<dependency>
			    <groupId>com.google.cloud</groupId>
			    <artifactId>alloydb-jdbc-connector</artifactId>
			    <version>${alloydb-jdbc-connector.version}</version>
			</dependency>

			<!-- spring-cloud-gcp-starter-sql -->
			<dependency>
				<groupId>com.google.cloud.sql</groupId>
				<artifactId>jdbc-socket-factory-core</artifactId>
				<version>${cloud-sql-socket-factory.version}</version>
			</dependency>

			<dependency>
				<groupId>com.google.cloud.sql</groupId>
				<artifactId>mysql-socket-factory-connector-j-8</artifactId>
				<version>${cloud-sql-socket-factory.version}</version>
			</dependency>

			<dependency>
				<groupId>com.google.cloud.sql</groupId>
				<artifactId>postgres-socket-factory</artifactId>
				<version>${cloud-sql-socket-factory.version}</version>
			</dependency>

			<!-- spring-cloud-gcp-starter-sql-postgres-r2dbc -->
			<dependency>
				<groupId>com.google.cloud.sql</groupId>
				<artifactId>cloud-sql-connector-r2dbc-postgres</artifactId>
				<version>${cloud-sql-socket-factory.version}</version>
			</dependency>
			<dependency>
				<groupId>org.postgresql</groupId>
				<artifactId>r2dbc-postgresql</artifactId>
				<version>${r2dbc-postgres-driver.version}</version>
			</dependency>

			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>cloud-spanner-r2dbc</artifactId>
				<version>${cloud-spanner-r2dbc.version}</version>
			</dependency>

			<!--Google Cloud Libraries BOM -->
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>libraries-bom</artifactId>
				<version>${gcp-libraries-bom.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<!-- spring dependency overrides -->
			<dependency>
				<groupId>io.opentelemetry</groupId>
				<artifactId>opentelemetry-api</artifactId>
				<version>1.46.0</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<profiles>

    <!--
    		Release profile must be defined here as well since spring-cloud-gcp-dependencies
    		does not inherit from spring-cloud-gcp-parent
    -->
		<profile>
			<id>release</id>
			<activation>
				<property>
					<name>release</name>
				</property>
			</activation>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-gpg-plugin</artifactId>
						<version>3.2.7</version>
						<executions>
							<execution>
								<id>sign-artifacts</id>
								<phase>verify</phase>
								<goals>
									<goal>sign</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>org.sonatype.plugins</groupId>
						<artifactId>nexus-staging-maven-plugin</artifactId>
						<version>1.7.0</version>
						<extensions>true</extensions>
						<configuration>
							<serverId>ossrh</serverId>
							<nexusUrl>https://google.oss.sonatype.org/</nexusUrl>
							<stagingProgressTimeoutMinutes>15</stagingProgressTimeoutMinutes>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>linkage-check</id>
			<!-- Not running as part of normal build because 'install' task interferes this rule when
			    running Maven with multi-thread -->
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-enforcer-plugin</artifactId>
						<version>3.5.0</version>
						<dependencies>
							<dependency>
								<groupId>com.google.cloud.tools</groupId>
								<artifactId>linkage-checker-enforcer-rules</artifactId>
								<version>1.5.13</version>
							</dependency>
						</dependencies>
						<executions>
							<execution>
								<id>enforce-linkage-checker</id>
								<phase>verify</phase>
								<goals>
									<goal>enforce</goal>
								</goals>
								<configuration>
									<fail>true</fail>
									<rules>
										<LinkageCheckerRule
											implementation="com.google.cloud.tools.dependencies.enforcer.LinkageCheckerRule">
											<dependencySection>DEPENDENCY_MANAGEMENT</dependencySection>
											<reportOnlyReachable>true</reportOnlyReachable>
											<exclusionFile>spring-cloud-gcp-dependencies/linkage-checker-exclusion-rules.xml</exclusionFile>
										</LinkageCheckerRule>
									</rules>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<licenses>
		<license>
			<name>Apache License, Version 2.0</name>
			<url>https://www.apache.org/licenses/LICENSE-2.0</url>
			<comments>
				Copyright 2015-2022 the original author or authors.

				Licensed under the Apache License, Version 2.0 (the "License");
				you may not use this file except in compliance with the License.
				You may obtain a copy of the License at

				https://www.apache.org/licenses/LICENSE-2.0

				Unless required by applicable law or agreed to in writing, software
				distributed under the License is distributed on an "AS IS" BASIS,
				WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
				implied.

				See the License for the specific language governing permissions and
				limitations under the License.
			</comments>
		</license>
	</licenses>

	<developers>
		<developer>
			<organization>Google</organization>
			<organizationUrl>http://cloud.google.com</organizationUrl>
		</developer>
	</developers>

</project>
