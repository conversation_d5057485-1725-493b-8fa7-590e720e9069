<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-vision-bom</artifactId>
  <version>3.27.0</version><!-- {x-version-update:google-cloud-vision:current} -->
  <packaging>pom</packaging>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-pom-parent</artifactId>
    <version>1.23.0</version><!-- {x-version-update:google-cloud-java:current} -->
    <relativePath>../../google-cloud-pom-parent/pom.xml</relativePath>
  </parent>

  <name>Google Cloud Vision BOM</name>
  <description>
    BOM for Google Cloud Vision
  </description>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-vision</artifactId>
        <version>3.27.0</version><!-- {x-version-update:google-cloud-vision:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-vision-v1p3beta1</artifactId>
        <version>0.116.0</version><!-- {x-version-update:grpc-google-cloud-vision-v1p3beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-vision-v1p1beta1</artifactId>
        <version>0.116.0</version><!-- {x-version-update:grpc-google-cloud-vision-v1p1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-vision-v1p4beta1</artifactId>
        <version>0.116.0</version><!-- {x-version-update:grpc-google-cloud-vision-v1p4beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-vision-v1p2beta1</artifactId>
        <version>3.27.0</version><!-- {x-version-update:grpc-google-cloud-vision-v1p2beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-vision-v1</artifactId>
        <version>3.27.0</version><!-- {x-version-update:grpc-google-cloud-vision-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-vision-v1p4beta1</artifactId>
        <version>0.116.0</version><!-- {x-version-update:proto-google-cloud-vision-v1p4beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-vision-v1</artifactId>
        <version>3.27.0</version><!-- {x-version-update:proto-google-cloud-vision-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-vision-v1p1beta1</artifactId>
        <version>0.116.0</version><!-- {x-version-update:proto-google-cloud-vision-v1p1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-vision-v1p3beta1</artifactId>
        <version>0.116.0</version><!-- {x-version-update:proto-google-cloud-vision-v1p3beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-vision-v1p2beta1</artifactId>
        <version>3.27.0</version><!-- {x-version-update:proto-google-cloud-vision-v1p2beta1:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
