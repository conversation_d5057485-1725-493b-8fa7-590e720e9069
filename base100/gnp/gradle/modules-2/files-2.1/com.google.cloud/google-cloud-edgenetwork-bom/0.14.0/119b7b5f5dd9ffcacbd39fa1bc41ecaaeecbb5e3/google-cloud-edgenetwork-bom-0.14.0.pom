<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-edgenetwork-bom</artifactId>
  <version>0.14.0</version><!-- {x-version-update:google-cloud-edgenetwork:current} -->
  <packaging>pom</packaging>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-pom-parent</artifactId>
    <version>1.40.0</version><!-- {x-version-update:google-cloud-java:current} -->
    <relativePath>../../google-cloud-pom-parent/pom.xml</relativePath>
  </parent>

  <name>Google Distributed Cloud Edge Network API BOM</name>
  <description>
    BOM for Distributed Cloud Edge Network API
  </description>

  <properties>
    <maven.antrun.skip>true</maven.antrun.skip>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-edgenetwork</artifactId>
        <version>0.14.0</version><!-- {x-version-update:google-cloud-edgenetwork:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-edgenetwork-v1</artifactId>
        <version>0.14.0</version><!-- {x-version-update:grpc-google-cloud-edgenetwork-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-edgenetwork-v1</artifactId>
        <version>0.14.0</version><!-- {x-version-update:proto-google-cloud-edgenetwork-v1:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
