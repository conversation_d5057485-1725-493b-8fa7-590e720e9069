<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-storage-parent</artifactId>
  <packaging>pom</packaging>
  <version>2.43.1</version><!-- {x-version-update:google-cloud-storage:current} -->
  <name>Storage Parent</name>
  <url>https://github.com/googleapis/java-storage</url>
  <description>
    Java idiomatic client for Google Cloud Platform services.
  </description>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>sdk-platform-java-config</artifactId>
    <version>3.36.1</version>
  </parent>

  <developers>
    <developer>
      <id>chingor</id>
      <name><PERSON></name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <organization>
    <name>Google LLC</name>
  </organization>
  <scm>
    <connection>scm:git:**************:googleapis/java-storage.git</connection>
    <developerConnection>scm:git:**************:googleapis/java-storage.git</developerConnection>
    <url>https://github.com/googleapis/java-storage</url>
    <tag>HEAD</tag>
  </scm>
  <issueManagement>
    <url>https://github.com/googleapis/java-storage/issues</url>
    <system>GitHub Issues</system>
  </issueManagement>

  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <github.global.server>github</github.global.server>
    <site.installationModule>google-cloud-storage-parent</site.installationModule>
    <google.cloud.shared-dependencies.version>3.31.0</google.cloud.shared-dependencies.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-shared-dependencies</artifactId>
        <version>${google-cloud-shared-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>5.11.1</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <!--
      Each of the following dependencies declares a dependency on opentelemtry-semconv,
      but they differ in what version. Explicitly declare the version here to break the tie.
      * com.google.cloud.opentelemetry:exporter-metrics
      * com.google.cloud.opentelemetry:detector-resources
      * io.opentelemetry.instrumentation:opentelemetry-resources
      * io.opentelemetry.contrib:opentelemetry-gcp-resources
      -->
      <dependency>
        <groupId>io.opentelemetry.semconv</groupId>
        <artifactId>opentelemetry-semconv</artifactId>
        <version>1.25.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud.opentelemetry</groupId>
        <artifactId>exporter-metrics</artifactId>
        <version>0.31.0</version>
        <exclusions>
          <exclusion>
            <groupId>io.opentelemetry.semconv</groupId>
            <artifactId>opentelemetry-semconv</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.google.cloud.opentelemetry</groupId>
        <artifactId>detector-resources</artifactId>
        <version>0.27.0-alpha</version>
        <exclusions>
          <exclusion>
            <groupId>io.opentelemetry.semconv</groupId>
            <artifactId>opentelemetry-semconv</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-resources</artifactId>
        <version>2.6.0-alpha</version>
        <exclusions>
          <exclusion>
            <groupId>io.opentelemetry.semconv</groupId>
            <artifactId>opentelemetry-semconv</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.contrib</groupId>
        <artifactId>opentelemetry-gcp-resources</artifactId>
        <version>1.37.0-alpha</version>
        <exclusions>
          <exclusion>
            <groupId>io.opentelemetry.semconv</groupId>
            <artifactId>opentelemetry-semconv</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-storage</artifactId>
        <version>2.43.1</version><!-- {x-version-update:google-cloud-storage:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.apis</groupId>
        <artifactId>google-api-services-storage</artifactId>
        <version>v1-rev20240819-2.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-pubsub</artifactId>
        <version>1.132.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>4.11.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-storage-v2</artifactId>
        <version>2.43.1-beta</version><!-- {x-version-update:proto-google-cloud-storage-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-storage-v2</artifactId>
        <version>2.43.1-beta</version><!-- {x-version-update:grpc-google-cloud-storage-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>gapic-google-cloud-storage-v2</artifactId>
        <version>2.43.1-beta</version><!-- {x-version-update:grpc-google-cloud-storage-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-storage-control-v2</artifactId>
        <version>2.43.1</version><!-- {x-version-update:grpc-google-cloud-storage-control-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-storage-control-v2</artifactId>
        <version>2.43.1</version><!-- {x-version-update:proto-google-cloud-storage-control-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-storage-control</artifactId>
        <version>2.43.1</version><!-- {x-version-update:google-cloud-storage-control:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-conformance-tests</artifactId>
        <version>0.3.7</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <groupId>org.checkerframework</groupId>
            <artifactId>checker-qual</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>4.5.14</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpmime</artifactId>
        <version>4.5.14</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore</artifactId>
        <version>4.4.16</version>
        <scope>test</scope>
      </dependency>

      <!-- Test dependencies -->
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.13.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.google.truth</groupId>
        <artifactId>truth</artifactId>
        <version>1.4.4</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <groupId>org.checkerframework</groupId>
            <artifactId>checker-qual</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <modules>
    <module>google-cloud-storage</module>
    <module>grpc-google-cloud-storage-v2</module>
    <module>grpc-google-cloud-storage-control-v2</module>
    <module>proto-google-cloud-storage-v2</module>
    <module>proto-google-cloud-storage-control-v2</module>
    <module>google-cloud-storage-control</module>
    <module>gapic-google-cloud-storage-v2</module>
    <module>google-cloud-storage-bom</module>
  </modules>

  <profiles>
    <profile>
      <id>include-samples</id>
      <modules>
        <module>samples</module>
      </modules>
    </profile>
    <profile>
      <id>include-storage-shared-benchmarking</id>
      <modules>
        <module>storage-shared-benchmarking</module>
      </modules>
    </profile>
  </profiles>
</project>
