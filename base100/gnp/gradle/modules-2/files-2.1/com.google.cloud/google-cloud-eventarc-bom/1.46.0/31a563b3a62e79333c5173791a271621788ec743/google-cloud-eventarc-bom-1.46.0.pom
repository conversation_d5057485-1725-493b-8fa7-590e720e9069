<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-eventarc-bom</artifactId>
  <version>1.46.0</version><!-- {x-version-update:google-cloud-eventarc:current} -->
  <packaging>pom</packaging>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-pom-parent</artifactId>
    <version>1.40.0</version><!-- {x-version-update:google-cloud-java:current} -->
    <relativePath>../../google-cloud-pom-parent/pom.xml</relativePath>
  </parent>

  <name>Google Eventarc BOM</name>
  <description>
    BOM for Eventarc
  </description>

  <properties>
    <maven.antrun.skip>true</maven.antrun.skip>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-eventarc</artifactId>
        <version>1.46.0</version><!-- {x-version-update:google-cloud-eventarc:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-eventarc-v1</artifactId>
        <version>1.46.0</version><!-- {x-version-update:grpc-google-cloud-eventarc-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-eventarc-v1</artifactId>
        <version>1.46.0</version><!-- {x-version-update:proto-google-cloud-eventarc-v1:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
