<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-functions-bom</artifactId>
  <version>2.31.0</version><!-- {x-version-update:google-cloud-functions:current} -->
  <packaging>pom</packaging>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-pom-parent</artifactId>
    <version>1.23.0</version><!-- {x-version-update:google-cloud-java:current} -->
    <relativePath>../../google-cloud-pom-parent/pom.xml</relativePath>
  </parent>

  <name>Google Cloud Functions BOM</name>
  <description>
    BOM for Cloud Functions
  </description>

  <properties>
    <maven.antrun.skip>true</maven.antrun.skip>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-functions</artifactId>
        <version>2.31.0</version><!-- {x-version-update:google-cloud-functions:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-functions-v1</artifactId>
        <version>2.31.0</version><!-- {x-version-update:grpc-google-cloud-functions-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-functions-v2beta</artifactId>
        <version>2.31.0</version><!-- {x-version-update:grpc-google-cloud-functions-v2beta:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-functions-v2alpha</artifactId>
        <version>2.31.0</version><!-- {x-version-update:grpc-google-cloud-functions-v2alpha:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-functions-v2</artifactId>
        <version>2.31.0</version><!-- {x-version-update:grpc-google-cloud-functions-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-functions-v1</artifactId>
        <version>2.31.0</version><!-- {x-version-update:proto-google-cloud-functions-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-functions-v2beta</artifactId>
        <version>2.31.0</version><!-- {x-version-update:proto-google-cloud-functions-v2beta:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-functions-v2alpha</artifactId>
        <version>2.31.0</version><!-- {x-version-update:proto-google-cloud-functions-v2alpha:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-functions-v2</artifactId>
        <version>2.31.0</version><!-- {x-version-update:proto-google-cloud-functions-v2:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
