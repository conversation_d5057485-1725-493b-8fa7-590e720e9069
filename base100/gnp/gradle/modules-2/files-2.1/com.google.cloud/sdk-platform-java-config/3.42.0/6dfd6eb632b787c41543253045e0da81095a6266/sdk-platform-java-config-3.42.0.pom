<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.google.cloud</groupId>
    <artifactId>sdk-platform-java-config</artifactId>
    <packaging>pom</packaging>
    <version>3.42.0</version> <!-- {x-version-update:google-cloud-shared-dependencies:current} -->
    <name>SDK Platform For Java Configurations</name>
    <description>
        Shared build configuration for Google Cloud Java libraries.
    </description>

    <parent>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-shared-config</artifactId>
        <version>1.14.0</version>
    </parent>

    <properties>
        <google-cloud-shared-dependencies.version>3.42.0</google-cloud-shared-dependencies.version> <!-- {x-version-update:google-cloud-shared-dependencies:current} -->
    </properties>
</project>