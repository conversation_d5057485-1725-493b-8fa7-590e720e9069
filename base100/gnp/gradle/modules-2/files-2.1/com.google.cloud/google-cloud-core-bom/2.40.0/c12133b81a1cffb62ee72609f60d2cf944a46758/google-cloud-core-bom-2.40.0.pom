<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-core-bom</artifactId>
  <version>2.40.0</version><!-- {x-version-update:google-cloud-core:current} -->
  <packaging>pom</packaging>

  <parent>
    <groupId>com.google.api</groupId>
    <artifactId>gapic-generator-java-pom-parent</artifactId>
    <version>2.42.0</version><!-- {x-version-update:gapic-generator-java:current} -->
    <relativePath>../../gapic-generator-java-pom-parent</relativePath>
  </parent>

  <name>Google Cloud Core</name>
  <description>
    BOM for Google Cloud Core
  </description>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-core</artifactId>
        <version>2.40.0</version><!-- {x-version-update:google-cloud-core:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-core-grpc</artifactId>
        <version>2.40.0</version><!-- {x-version-update:google-cloud-core:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-core-http</artifactId>
        <version>2.40.0</version><!-- {x-version-update:google-cloud-core:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
