<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-storage</artifactId>
  <version>2.43.1</version>
  <name>Google Cloud Storage</name>
  <description>Java idiomatic client for Google Cloud Storage.</description>
  <url>https://github.com/googleapis/java-storage</url>
  <organization>
    <name>Google LLC</name>
  </organization>
  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>chingor</id>
      <name><PERSON></name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:**************:googleapis/java-storage.git/google-cloud-storage</connection>
    <developerConnection>scm:git:**************:googleapis/java-storage.git/google-cloud-storage</developerConnection>
    <url>https://github.com/googleapis/java-storage/google-cloud-storage</url>
  </scm>
  <issueManagement>
    <system>GitHub Issues</system>
    <url>https://github.com/googleapis/java-storage/issues</url>
  </issueManagement>
  <distributionManagement>
    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://google.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <url>https://google.oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>33.3.0-jre</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>failureaccess</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>listenablefuture</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.checkerframework</groupId>
          <artifactId>checker-qual</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.j2objc</groupId>
          <artifactId>j2objc-annotations</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>failureaccess</artifactId>
      <version>1.0.2</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>listenablefuture</artifactId>
      <version>9999.0-empty-to-avoid-conflict-with-guava</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.errorprone</groupId>
      <artifactId>error_prone_annotations</artifactId>
      <version>2.32.0</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.j2objc</groupId>
      <artifactId>j2objc-annotations</artifactId>
      <version>3.0.0</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client</artifactId>
      <version>1.45.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.apache.httpcomponents</groupId>
          <artifactId>httpclient</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.httpcomponents</groupId>
          <artifactId>httpcore</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.j2objc</groupId>
          <artifactId>j2objc-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-context</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opencensus</groupId>
          <artifactId>opencensus-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opencensus</groupId>
          <artifactId>opencensus-contrib-http-util</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-context</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opencensus</groupId>
      <artifactId>opencensus-contrib-http-util</artifactId>
      <version>0.31.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.opencensus</groupId>
          <artifactId>opencensus-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client-jackson2</artifactId>
      <version>1.45.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-core</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client-gson</artifactId>
      <version>1.45.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.gson</groupId>
          <artifactId>gson</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api-client</groupId>
      <artifactId>google-api-client</artifactId>
      <version>2.7.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>commons-codec</groupId>
          <artifactId>commons-codec</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.oauth-client</groupId>
          <artifactId>google-oauth-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-credentials</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-oauth2-http</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client-gson</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client-apache-v2</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.httpcomponents</groupId>
          <artifactId>httpcore</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.httpcomponents</groupId>
          <artifactId>httpclient</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.17.1</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.oauth-client</groupId>
      <artifactId>google-oauth-client</artifactId>
      <version>1.36.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client-gson</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client-apache-v2</artifactId>
      <version>1.45.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.httpcomponents</groupId>
          <artifactId>httpclient</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.httpcomponents</groupId>
          <artifactId>httpcore</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.apis</groupId>
      <artifactId>google-api-services-storage</artifactId>
      <version>v1-rev20240819-2.0.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.api-client</groupId>
          <artifactId>google-api-client</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.11.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.cloud</groupId>
      <artifactId>google-cloud-core</artifactId>
      <version>2.44.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>gax</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auto.value</groupId>
          <artifactId>auto-value-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java-util</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-common-protos</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-iam-v1</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.threeten</groupId>
          <artifactId>threetenbp</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>api-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-credentials</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-oauth2-http</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client-gson</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.auto.value</groupId>
      <artifactId>auto-value-annotations</artifactId>
      <version>1.11.0</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.cloud</groupId>
      <artifactId>google-cloud-core-http</artifactId>
      <version>2.44.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.cloud</groupId>
          <artifactId>google-cloud-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-credentials</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-oauth2-http</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api-client</groupId>
          <artifactId>google-api-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client-appengine</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>gax</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>gax-httpjson</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opencensus</groupId>
          <artifactId>opencensus-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opencensus</groupId>
          <artifactId>opencensus-contrib-http-util</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>api-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client-appengine</artifactId>
      <version>1.45.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.appengine</groupId>
          <artifactId>appengine-api-1.0-sdk</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>gax-httpjson</artifactId>
      <version>2.54.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>gax</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>api-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-common-protos</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-credentials</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.gson</groupId>
          <artifactId>gson</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.threeten</groupId>
          <artifactId>threetenbp</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-oauth2-http</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client-gson</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java-util</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auto.value</groupId>
          <artifactId>auto-value-annotations</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.cloud</groupId>
      <artifactId>google-cloud-core-grpc</artifactId>
      <version>2.44.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-credentials</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.cloud</groupId>
          <artifactId>google-cloud-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>gax</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>gax-grpc</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>api-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>gax</artifactId>
      <version>2.54.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>api-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-credentials</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-common-protos</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.threeten</groupId>
          <artifactId>threetenbp</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opencensus</groupId>
          <artifactId>opencensus-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-oauth2-http</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.graalvm.sdk</groupId>
          <artifactId>graal-sdk</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-api</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>gax-grpc</artifactId>
      <version>2.54.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>gax</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>api-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-common-protos</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-credentials</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-inprocess</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.threeten</groupId>
          <artifactId>threetenbp</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-oauth2-http</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-alts</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-auth</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-protobuf</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-stub</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-netty-shaded</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-googleapis</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.graalvm.sdk</groupId>
          <artifactId>graal-sdk</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auto.value</groupId>
          <artifactId>auto-value-annotations</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-inprocess</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-alts</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-auth</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-context</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-grpclb</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-protobuf</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-stub</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.conscrypt</groupId>
          <artifactId>conscrypt-openjdk-uber</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-oauth2-http</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-netty-shaded</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-grpclb</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-protobuf</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-stub</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java-util</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>org.conscrypt</groupId>
      <artifactId>conscrypt-openjdk-uber</artifactId>
      <version>2.5.2</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-auth</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-credentials</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.auth</groupId>
      <artifactId>google-auth-library-credentials</artifactId>
      <version>1.27.0</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.auth</groupId>
      <artifactId>google-auth-library-oauth2-http</artifactId>
      <version>1.27.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.auto.value</groupId>
          <artifactId>auto-value-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-credentials</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client-gson</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>api-common</artifactId>
      <version>2.37.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auto.value</groupId>
          <artifactId>auto-value-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.annotation</groupId>
          <artifactId>javax.annotation-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.j2objc</groupId>
          <artifactId>j2objc-annotations</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>javax.annotation</groupId>
      <artifactId>javax.annotation-api</artifactId>
      <version>1.3.2</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opencensus</groupId>
      <artifactId>opencensus-api</artifactId>
      <version>0.31.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-context</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>proto-google-iam-v1</artifactId>
      <version>1.40.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-common-protos</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>3.25.5</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java-util</artifactId>
      <version>3.25.5</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.gson</groupId>
          <artifactId>gson</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.j2objc</groupId>
          <artifactId>j2objc-annotations</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-core</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.gson</groupId>
          <artifactId>gson</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.android</groupId>
          <artifactId>annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.perfmark</groupId>
          <artifactId>perfmark-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-context</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.android</groupId>
      <artifactId>annotations</artifactId>
      <version>4.1.1.4</version>
      <scope>runtime</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>org.codehaus.mojo</groupId>
      <artifactId>animal-sniffer-annotations</artifactId>
      <version>1.24</version>
      <scope>runtime</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.perfmark</groupId>
      <artifactId>perfmark-api</artifactId>
      <version>0.27.0</version>
      <scope>runtime</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-protobuf</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-common-protos</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-protobuf-lite</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-protobuf-lite</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-javalite</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>proto-google-common-protos</artifactId>
      <version>2.45.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>org.threeten</groupId>
      <artifactId>threetenbp</artifactId>
      <version>1.7.0</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>proto-google-cloud-storage-v2</artifactId>
      <version>2.43.1-beta</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-common-protos</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-iam-v1</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>api-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>grpc-google-cloud-storage-v2</artifactId>
      <version>2.43.1-beta</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-stub</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-protobuf</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-cloud-storage-v2</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-iam-v1</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.annotation</groupId>
          <artifactId>javax.annotation-api</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>gapic-google-cloud-storage-v2</artifactId>
      <version>2.43.1-beta</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-stub</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-protobuf</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-cloud-storage-v2</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>grpc-google-cloud-storage-v2</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-common-protos</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.threeten</groupId>
          <artifactId>threetenbp</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>api-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>gax</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-iam-v1</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>gax-grpc</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.annotation</groupId>
          <artifactId>javax.annotation-api</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry</groupId>
      <artifactId>opentelemetry-sdk</artifactId>
      <version>1.42.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-sdk-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-sdk-trace</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-sdk-metrics</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-sdk-logs</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry</groupId>
      <artifactId>opentelemetry-sdk-trace</artifactId>
      <version>1.42.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-sdk-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-api-incubator</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry</groupId>
      <artifactId>opentelemetry-sdk-logs</artifactId>
      <version>1.42.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-sdk-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-api-incubator</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-opentelemetry</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auto.value</groupId>
          <artifactId>auto-value-annotations</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry</groupId>
      <artifactId>opentelemetry-api</artifactId>
      <version>1.42.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-context</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry</groupId>
      <artifactId>opentelemetry-context</artifactId>
      <version>1.42.1</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry</groupId>
      <artifactId>opentelemetry-sdk-metrics</artifactId>
      <version>1.42.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-sdk-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-api-incubator</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry</groupId>
      <artifactId>opentelemetry-api-incubator</artifactId>
      <version>1.42.1-alpha</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-api</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry</groupId>
      <artifactId>opentelemetry-sdk-common</artifactId>
      <version>1.42.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-api</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry</groupId>
      <artifactId>opentelemetry-sdk-extension-autoconfigure-spi</artifactId>
      <version>1.42.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-sdk</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry.semconv</groupId>
      <artifactId>opentelemetry-semconv</artifactId>
      <version>1.25.0-alpha</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.cloud.opentelemetry</groupId>
      <artifactId>exporter-metrics</artifactId>
      <version>0.31.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.auto.value</groupId>
          <artifactId>auto-value-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.cloud</groupId>
          <artifactId>google-cloud-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.cloud</groupId>
          <artifactId>google-cloud-monitoring</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-sdk-metrics</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.cloud.opentelemetry</groupId>
          <artifactId>shared-resourcemapping</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry.semconv</groupId>
          <artifactId>opentelemetry-semconv</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>2.0.9</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.cloud</groupId>
      <artifactId>google-cloud-monitoring</artifactId>
      <version>3.31.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-stub</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-protobuf</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-protobuf-lite</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>api-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auto.value</groupId>
          <artifactId>auto-value-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.annotation</groupId>
          <artifactId>javax.annotation-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-common-protos</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-cloud-monitoring-v3</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>failureaccess</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>listenablefuture</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.checkerframework</groupId>
          <artifactId>checker-qual</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.j2objc</groupId>
          <artifactId>j2objc-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>gax</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-credentials</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opencensus</groupId>
          <artifactId>opencensus-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-context</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>gax-grpc</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-inprocess</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.android</groupId>
          <artifactId>annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-util</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-alts</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-grpclb</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java-util</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.conscrypt</groupId>
          <artifactId>conscrypt-openjdk-uber</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-auth</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-netty-shaded</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.perfmark</groupId>
          <artifactId>perfmark-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-googleapis</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-xds</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opencensus</groupId>
          <artifactId>opencensus-proto</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-services</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.re2j</groupId>
          <artifactId>re2j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.threeten</groupId>
          <artifactId>threetenbp</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auth</groupId>
          <artifactId>google-auth-library-oauth2-http</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.httpcomponents</groupId>
          <artifactId>httpclient</artifactId>
        </exclusion>
        <exclusion>
          <groupId>commons-logging</groupId>
          <artifactId>commons-logging</artifactId>
        </exclusion>
        <exclusion>
          <groupId>commons-codec</groupId>
          <artifactId>commons-codec</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.httpcomponents</groupId>
          <artifactId>httpcore</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opencensus</groupId>
          <artifactId>opencensus-contrib-http-util</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.http-client</groupId>
          <artifactId>google-http-client-gson</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.gson</groupId>
          <artifactId>gson</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>proto-google-cloud-monitoring-v3</artifactId>
      <version>3.31.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api.grpc</groupId>
          <artifactId>proto-google-common-protos</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.api</groupId>
          <artifactId>api-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auto.value</groupId>
          <artifactId>auto-value-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.annotation</groupId>
          <artifactId>javax.annotation-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>failureaccess</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>listenablefuture</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.checkerframework</groupId>
          <artifactId>checker-qual</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.j2objc</groupId>
          <artifactId>j2objc-annotations</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opencensus</groupId>
      <artifactId>opencensus-proto</artifactId>
      <version>0.2.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-protobuf</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-stub</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.2</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>log4j</groupId>
          <artifactId>log4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>logkit</groupId>
          <artifactId>logkit</artifactId>
        </exclusion>
        <exclusion>
          <groupId>avalon-framework</groupId>
          <artifactId>avalon-framework</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.servlet</groupId>
          <artifactId>servlet-api</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.cloud.opentelemetry</groupId>
      <artifactId>shared-resourcemapping</artifactId>
      <version>0.32.0</version>
      <scope>runtime</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry.contrib</groupId>
      <artifactId>opentelemetry-gcp-resources</artifactId>
      <version>1.37.0-alpha</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry</groupId>
          <artifactId>opentelemetry-sdk</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.cloud.opentelemetry</groupId>
          <artifactId>detector-resources-support</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.opentelemetry.semconv</groupId>
          <artifactId>opentelemetry-semconv</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-core</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.cloud.opentelemetry</groupId>
      <artifactId>detector-resources-support</artifactId>
      <version>0.32.0</version>
      <scope>runtime</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>org.checkerframework</groupId>
      <artifactId>checker-qual</artifactId>
      <version>3.47.0</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>2.17.2</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.2</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-api</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>com.google.code.findbugs</groupId>
          <artifactId>jsr305</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-netty-shaded</artifactId>
      <version>1.68.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-util</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.perfmark</groupId>
          <artifactId>perfmark-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-util</artifactId>
      <version>1.68.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-stub</artifactId>
      <version>1.68.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-googleapis</artifactId>
      <version>1.68.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-alts</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-xds</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-xds</artifactId>
      <version>1.68.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-protobuf</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-stub</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-util</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-services</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-auth</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-alts</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.gson</groupId>
          <artifactId>gson</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.re2j</groupId>
          <artifactId>re2j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auto.value</groupId>
          <artifactId>auto-value-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java-util</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-netty-shaded</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-services</artifactId>
      <version>1.68.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-stub</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-protobuf</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-util</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.protobuf</groupId>
          <artifactId>protobuf-java-util</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.code.gson</groupId>
          <artifactId>gson</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.re2j</groupId>
      <artifactId>re2j</artifactId>
      <version>1.7</version>
      <scope>runtime</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-rls</artifactId>
      <version>1.68.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-util</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-protobuf</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.grpc</groupId>
          <artifactId>grpc-stub</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.auto.value</groupId>
          <artifactId>auto-value-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.google.guava</groupId>
          <artifactId>guava</artifactId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
  </dependencies>
</project>
