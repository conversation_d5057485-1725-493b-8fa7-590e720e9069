<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-dataproc-bom</artifactId>
  <version>4.43.0</version><!-- {x-version-update:google-cloud-dataproc:current} -->
  <packaging>pom</packaging>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-pom-parent</artifactId>
    <version>1.40.0</version><!-- {x-version-update:google-cloud-java:current} -->
    <relativePath>../../google-cloud-pom-parent/pom.xml</relativePath>
  </parent>

  <name>Google Cloud Dataproc BOM</name>
  <description>
    BOM for Google Cloud Dataproc
  </description>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dataproc</artifactId>
        <version>4.43.0</version><!-- {x-version-update:google-cloud-dataproc:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-dataproc-v1</artifactId>
        <version>4.43.0</version><!-- {x-version-update:grpc-google-cloud-dataproc-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-dataproc-v1</artifactId>
        <version>4.43.0</version><!-- {x-version-update:proto-google-cloud-dataproc-v1:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
