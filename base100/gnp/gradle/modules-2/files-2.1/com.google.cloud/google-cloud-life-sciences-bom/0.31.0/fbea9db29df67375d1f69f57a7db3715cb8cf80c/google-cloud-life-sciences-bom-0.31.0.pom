<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-life-sciences-bom</artifactId>
  <version>0.31.0</version><!-- {x-version-update:google-cloud-life-sciences:current} -->
  <packaging>pom</packaging>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-pom-parent</artifactId>
    <version>1.23.0</version><!-- {x-version-update:google-cloud-java:current} -->
    <relativePath>../../google-cloud-pom-parent/pom.xml</relativePath>
  </parent>

  <name>Google Cloud Life Sciences BOM</name>
  <description>
    BOM for Cloud Life Sciences
  </description>

  <properties>
    <maven.antrun.skip>true</maven.antrun.skip>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-life-sciences</artifactId>
        <version>0.31.0</version><!-- {x-version-update:google-cloud-life-sciences:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-life-sciences-v2beta</artifactId>
        <version>0.31.0</version><!-- {x-version-update:grpc-google-cloud-life-sciences-v2beta:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-life-sciences-v2beta</artifactId>
        <version>0.31.0</version><!-- {x-version-update:proto-google-cloud-life-sciences-v2beta:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
