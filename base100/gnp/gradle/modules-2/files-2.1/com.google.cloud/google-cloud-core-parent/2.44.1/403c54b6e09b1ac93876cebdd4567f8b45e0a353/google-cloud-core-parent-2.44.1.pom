<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-core-parent</artifactId>
  <packaging>pom</packaging>
  <version>2.44.1</version><!-- {x-version-update:google-cloud-core:current} -->
  <name>Google Cloud Core Parent</name>
  <description>
    Java idiomatic client for Google Cloud Platform services.
  </description>

  <parent>
    <groupId>com.google.api</groupId>
    <artifactId>gapic-generator-java-pom-parent</artifactId>
    <version>2.46.1</version><!-- {x-version-update:gapic-generator-java:current} -->
    <relativePath>../gapic-generator-java-pom-parent</relativePath>
  </parent>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <github.global.server>github</github.global.server>
    <site.installationModule>google-cloud-core-parent</site.installationModule>
    <!-- This module itself does not have integration tests and thus cannot
        be tested with native image plugin. Instead, the client libraries that
        use this java-core module have integration tests on native image. -->
    <skipNativeTests>true</skipNativeTests>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-shared-dependencies</artifactId>
        <version>3.36.1</version><!-- {x-version-update:google-cloud-shared-dependencies:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <!-- Test dependencies -->
      <dependency>
        <groupId>com.google.truth</groupId>
        <artifactId>truth</artifactId>
        <version>1.4.4</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <groupId>org.checkerframework</groupId>
            <artifactId>checker-qual</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.easymock</groupId>
        <artifactId>easymock</artifactId>
        <version>5.4.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-all</artifactId>
        <version>1.10.19</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.google.errorprone</groupId>
        <artifactId>error_prone_annotationsa</artifactId>
        <version>${errorprone.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <modules>
    <module>google-cloud-core</module>
    <module>google-cloud-core-http</module>
    <module>google-cloud-core-grpc</module>
    <module>google-cloud-core-bom</module>
  </modules>
</project>
