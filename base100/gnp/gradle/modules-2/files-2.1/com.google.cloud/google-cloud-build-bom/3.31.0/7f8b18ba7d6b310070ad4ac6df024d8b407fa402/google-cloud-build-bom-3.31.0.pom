<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-build-bom</artifactId>
  <version>3.31.0</version><!-- {x-version-update:google-cloud-build:current} -->
  <packaging>pom</packaging>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-pom-parent</artifactId>
    <version>1.23.0</version><!-- {x-version-update:google-cloud-java:current} -->
    <relativePath>../../google-cloud-pom-parent/pom.xml</relativePath>
  </parent>

  <name>Google Cloud Build BOM</name>
  <description>
    BOM for Google CloudBuild
  </description>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-build</artifactId>
        <version>3.31.0</version><!-- {x-version-update:google-cloud-build:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-build-v1</artifactId>
        <version>3.31.0</version><!-- {x-version-update:grpc-google-cloud-build-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-build-v2</artifactId>
        <version>3.31.0</version><!-- {x-version-update:grpc-google-cloud-build-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-build-v1</artifactId>
        <version>3.31.0</version><!-- {x-version-update:proto-google-cloud-build-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-build-v2</artifactId>
        <version>3.31.0</version><!-- {x-version-update:proto-google-cloud-build-v2:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
