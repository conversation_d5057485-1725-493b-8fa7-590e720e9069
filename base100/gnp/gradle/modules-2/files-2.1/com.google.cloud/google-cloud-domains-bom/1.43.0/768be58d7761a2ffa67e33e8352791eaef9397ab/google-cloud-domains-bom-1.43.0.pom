<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-domains-bom</artifactId>
  <version>1.43.0</version><!-- {x-version-update:google-cloud-domains:current} -->
  <packaging>pom</packaging>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-pom-parent</artifactId>
    <version>1.40.0</version><!-- {x-version-update:google-cloud-java:current} -->
    <relativePath>../../google-cloud-pom-parent/pom.xml</relativePath>
  </parent>

  <name>Google Cloud Domains BOM</name>
  <description>
    BOM for Cloud Domains
  </description>

  <properties>
    <maven.antrun.skip>true</maven.antrun.skip>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-domains</artifactId>
        <version>1.43.0</version><!-- {x-version-update:google-cloud-domains:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-domains-v1beta1</artifactId>
        <version>0.51.0</version><!-- {x-version-update:grpc-google-cloud-domains-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-domains-v1alpha2</artifactId>
        <version>0.51.0</version><!-- {x-version-update:grpc-google-cloud-domains-v1alpha2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-domains-v1</artifactId>
        <version>1.43.0</version><!-- {x-version-update:grpc-google-cloud-domains-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-domains-v1beta1</artifactId>
        <version>0.51.0</version><!-- {x-version-update:proto-google-cloud-domains-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-domains-v1alpha2</artifactId>
        <version>0.51.0</version><!-- {x-version-update:proto-google-cloud-domains-v1alpha2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-domains-v1</artifactId>
        <version>1.43.0</version><!-- {x-version-update:proto-google-cloud-domains-v1:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
