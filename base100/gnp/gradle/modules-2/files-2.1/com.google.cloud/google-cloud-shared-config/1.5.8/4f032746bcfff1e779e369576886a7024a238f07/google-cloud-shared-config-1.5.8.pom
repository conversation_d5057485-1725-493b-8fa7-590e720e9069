<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-shared-config</artifactId>
  <packaging>pom</packaging>
  <version>1.5.8</version><!-- {x-version-update:google-cloud-shared-config:current} -->
  <name>Google Cloud</name>
  <url>https://github.com/googleapis/java-shared-config</url>
  <description>
    Shared build configuration for Google Cloud Java libraries.
  </description>
  <developers>
    <developer>
      <id>chingor13</id>
      <name><PERSON></name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <organization>
    <name>Google LLC</name>
  </organization>
  <scm>
    <connection>scm:git:**************:googleapis/java-shared-config.git</connection>
    <developerConnection>scm:git:**************:googleapis/java-shared-config.git
    </developerConnection>
    <url>https://github.com/googleapis/java-shared-config</url>
    <tag>HEAD</tag>
  </scm>

  <issueManagement>
    <url>https://github.com/googleapis/java-shared-config/issues</url>
    <system>GitHub Issues</system>
  </issueManagement>

  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <url>https://google.oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://google.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>

  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <!-- Template values used in site generation -->
    <site.installationModule>${project.artifactId}</site.installationModule>
    <report.jxr.inherited>false</report.jxr.inherited>
    <skipITs>true</skipITs>
    <auto-value.version>1.10.4</auto-value.version>
    <surefire.version>3.1.2</surefire.version>
    <docRoot>/java/docs/reference/</docRoot>
  </properties>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.sonatype.plugins</groupId>
          <artifactId>nexus-staging-maven-plugin</artifactId>
          <version>1.6.13</version>
          <extensions>true</extensions>
          <configuration>
            <serverId>ossrh</serverId>
            <nexusUrl>https://google.oss.sonatype.org/</nexusUrl>
            <autoReleaseAfterClose>false</autoReleaseAfterClose>
            <stagingProgressTimeoutMinutes>15</stagingProgressTimeoutMinutes>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${surefire.version}</version>
          <configuration>
            <!-- Excludes integration tests and smoke tests when unit tests are run -->
            <excludes>
              <exclude>**/*SmokeTest.java</exclude>
              <exclude>**/IT*.java</exclude>
            </excludes>
            <reportNameSuffix>sponge_log</reportNameSuffix>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>3.4.1</version>
          <dependencies>
            <dependency>
              <groupId>org.codehaus.mojo</groupId>
              <artifactId>extra-enforcer-rules</artifactId>
              <version>1.7.0</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>exec-maven-plugin</artifactId>
          <version>3.1.0</version>
          <configuration>
            <!-- to get rid of the warning: [WARNING] Warning: killAfter is now deprecated. Do you need it ?
                 Please comment on MEXEC-6. see: method execute() in
                 https://github.com/ispringer/exec-maven-plugin/blob/master/src/main/java/org/codehaus/mojo/exec/ExecJavaMojo.java
            -->
            <killAfter>-1</killAfter>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <!-- this plugin is released along with maven-surefire-plugin -->
          <version>${surefire.version}</version>
          <configuration>
            <forkedProcessTimeoutInSeconds>3600</forkedProcessTimeoutInSeconds>
            <reportNameSuffix>sponge_log</reportNameSuffix>
            <includes>
              <include>**/IT*.java</include>
              <include>**/*SmokeTest.java</include>
            </includes>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>org.apache.maven.surefire</groupId>
              <artifactId>surefire-junit47</artifactId>
              <version>${surefire.version}</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.3.0</version>
          <configuration>
            <archive>
              <addMavenDescriptor>true</addMavenDescriptor>
              <index>true</index>
              <manifest>
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              </manifest>
              <manifestEntries>
                <artifactId>${project.artifactId}</artifactId>
                <groupId>${project.groupId}</groupId>
                <version>${project.version}</version>
              </manifestEntries>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.11.0</version>
          <configuration>
            <source>1.8</source>
            <target>1.8</target>
            <encoding>UTF-8</encoding>
            <compilerArgument>-Xlint:unchecked</compilerArgument>
            <compilerArgument>-Xlint:deprecation</compilerArgument>
            <showDeprecation>true</showDeprecation>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.12.1</version>
          <configuration>
            <skipDeploy>true</skipDeploy>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.jacoco</groupId>
          <artifactId>jacoco-maven-plugin</artifactId>
          <version>0.8.10</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>3.5.0</version>
          <configuration>
            <ignoredUnusedDeclaredDependencies>
              <ignoredUnusedDeclaredDependency>javax.annotation:javax.annotation-api
              </ignoredUnusedDeclaredDependency>
            </ignoredUnusedDeclaredDependencies>
            <!-- TODO: Try to remove these exclusions by updating handwritten libs -->
            <ignoredNonTestScopedDependencies>
              <ignoredNonTestScopedDependency>io.grpc:*
              </ignoredNonTestScopedDependency>
              <ignoredNonTestScopedDependency>com.google.protobuf:*
              </ignoredNonTestScopedDependency>
              <ignoredNonTestScopedDependency>com.google.api.grpc:*
              </ignoredNonTestScopedDependency>
            </ignoredNonTestScopedDependencies>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>3.4.0</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>2.8</version>
        </plugin>
        <plugin>
          <groupId>com.coveo</groupId>
          <artifactId>fmt-maven-plugin</artifactId>
          <version>2.9</version>
          <configuration>
            <style>google</style>
            <verbose>true</verbose>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>com.google.googlejavaformat</groupId>
              <artifactId>google-java-format</artifactId>
              <version>1.7</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>flatten-maven-plugin</artifactId>
          <version>1.3.0</version>
          <executions>
            <!-- enable flattening -->
            <execution>
              <id>flatten</id>
              <phase>process-resources</phase>
              <goals>
                <goal>flatten</goal>
              </goals>
            </execution>
            <!-- ensure proper cleanup -->
            <execution>
              <id>flatten.clean</id>
              <phase>clean</phase>
              <goals>
                <goal>clean</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <flattenMode>oss</flattenMode>
            <flattenDependencyMode>all</flattenDependencyMode>
            <pomElements>
              <build>remove</build>
            </pomElements>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireMavenVersion>
                  <version>[3.0,)</version>
                </requireMavenVersion>
                <requireJavaVersion>
                  <version>[1.7,)</version>
                </requireJavaVersion>
                <requireUpperBoundDeps/>
                <banDuplicateClasses>
                  <scopes>
                    <scope>compile</scope>
                    <scope>provided</scope>
                  </scopes>
                  <findAllDuplicates>true</findAllDuplicates>
                  <ignoreWhenIdentical>true</ignoreWhenIdentical>
                </banDuplicateClasses>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>java</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>integration-test</goal>
              <goal>verify</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>add-main-proto-resources</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>add-resource</goal>
            </goals>
            <configuration>
              <resources>
                <resource>
                  <directory>src/main/proto</directory>
                </resource>
              </resources>
            </configuration>
          </execution>
          <execution>
            <id>add-test-proto-resources</id>
            <phase>generate-test-resources</phase>
            <goals>
              <goal>add-test-resource</goal>
            </goals>
            <configuration>
              <resources>
                <resource>
                  <directory>src/test/proto</directory>
                </resource>
              </resources>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <configuration>
          <ignoredDifferencesFile>clirr-ignored-differences.xml</ignoredDifferencesFile>
          <logResults>true</logResults>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>1.23</version>
        <executions>
          <execution>
            <id>java8</id>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <signature>
                <groupId>org.codehaus.mojo.signature</groupId>
                <artifactId>java18</artifactId>
                <version>1.0</version>
              </signature>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>3.4.5</version>
        <reportSets>
          <reportSet>
            <reports>
              <report>index</report>
              <report>dependency-info</report>
              <report>team</report>
              <report>ci-management</report>
              <report>issue-management</report>
              <report>licenses</report>
              <report>scm</report>
              <report>dependency-management</report>
              <report>distribution-management</report>
              <report>summary</report>
              <report>modules</report>
            </reports>
          </reportSet>
        </reportSets>
        <configuration>
          <dependencyDetailsEnabled>true</dependencyDetailsEnabled>
          <artifactId>${site.installationModule}</artifactId>
          <packaging>jar</packaging>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>3.5.0</version>
        <reportSets>
          <reportSet>
            <id>html</id>
            <reports>
              <report>javadoc</report>
              <report>aggregate</report>
              <report>aggregate-jar</report>
            </reports>
          </reportSet>
        </reportSets>
        <configuration>
          <doclint>none</doclint>
          <show>protected</show>
          <nohelp>true</nohelp>
          <outputDirectory>${project.build.directory}/javadoc</outputDirectory>
          <groups>
            <group>
              <title>Test helpers packages</title>
              <packages>com.google.cloud.testing</packages>
            </group>
            <group>
              <title>SPI packages</title>
              <packages>com.google.cloud.spi*</packages>
            </group>
          </groups>

          <links>
            <link>https://googleapis.dev/java/api-common/</link>
            <link>https://googleapis.dev/java/gax/</link>
            <link>https://googleapis.dev/java/google-auth-library/</link>

            <link>https://developers.google.com/protocol-buffers/docs/reference/java/</link>
            <link>https://googleapis.github.io/common-protos-java/apidocs/</link>
            <link>https://grpc.io/grpc-java/javadoc/</link>
          </links>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>3.3.0</version>
        <configuration>
          <linkJavadoc>true</linkJavadoc>
        </configuration>
        <reportSets>
          <reportSet>
            <id>aggregate</id>
            <inherited>${report.jxr.inherited}</inherited>
            <reports>
              <report>jxr</report>
              <report>aggregate</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
    </plugins>
  </reporting>

  <dependencyManagement>
    <dependencies>
      <!--
      We would prefer this be defined in our shared-dependencies, however due to
      auto value being part of the compiler annotation processor, we are defining
      it here to reduce the locations for version management.
      -->
      <dependency>
        <groupId>com.google.auto.value</groupId>
        <artifactId>auto-value-annotations</artifactId>
        <version>${auto-value.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <profiles>
    <profile>
      <id>release</id>
      <activation>
        <property>
          <name>performRelease</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>3.3.0</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.5.0</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <configuration>
                  <doclint>none</doclint>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>3.1.0</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
                <configuration>
                  <gpgArguments>
                    <arg>--pinentry-mode</arg>
                    <arg>loopback</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>allow-snapshots</id>
      <repositories>
        <repository>
          <id>sonatype-snapshots</id>
          <url>https://google.oss.sonatype.org/content/repositories/snapshots</url>
          <releases>
            <enabled>false</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </repository>
      </repositories>
    </profile>
    <profile>
      <!-- Only run checkstyle plugin on Java 11+ (checkstyle artifact only supports Java 11+) -->
      <id>checkstyle-tests</id>
      <activation>
        <jdk>[1.11,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <version>3.3.0</version>
            <dependencies>
              <dependency>
                <groupId>com.puppycrawl.tools</groupId>
                <artifactId>checkstyle</artifactId>
                <version>10.12.3</version>
              </dependency>
            </dependencies>
            <executions>
              <execution>
                <id>checkstyle</id>
                <phase>validate</phase>
                <goals>
                  <goal>check</goal>
                </goals>
                <configuration>
                  <headerLocation>java.header</headerLocation>
                  <configLocation>license-checks.xml</configLocation>
                  <consoleOutput>true</consoleOutput>
                  <failOnViolation>true</failOnViolation>
                  <violationSeverity>error</violationSeverity>
                  <failsOnError>true</failsOnError>
                  <includeTestSourceDirectory>true</includeTestSourceDirectory>
                  <!-- Explicitly set the source directory to avoid running checkstyle on generated sources. -->
                  <sourceDirectories>
                    <sourceDirectory>src/main</sourceDirectory>
                  </sourceDirectories>
                  <testSourceDirectories>
                    <testSourceDirectory>src/test</testSourceDirectory>
                  </testSourceDirectories>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>enable-integration-tests</id>
      <properties>
        <skipITs>false</skipITs>
      </properties>
    </profile>

    <profile>
      <!-- cloud RAD generation -->
      <id>docFX</id>
      <activation>
        <property>
          <!-- activate with -P docFX -->
          <name>docFX</name>
        </property>
      </activation>
      <properties>
        <!-- default config values -->
        <docletName>java-docfx-doclet-1.9.0</docletName>
        <docletPath>${env.KOKORO_GFILE_DIR}/${docletName}.jar</docletPath>
        <outputpath>${project.build.directory}/docfx-yml</outputpath>
        <projectname>${project.artifactId}</projectname>
        <excludeclasses></excludeclasses>
        <excludePackages></excludePackages>
        <source>8</source>            
        <sourceFileExclude></sourceFileExclude>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.5.0</version>
            <configuration>
              <doclet>com.microsoft.doclet.DocFxDoclet</doclet>
              <useStandardDocletOptions>false</useStandardDocletOptions>              
              <additionalOptions>
                -outputpath ${outputpath} 
                -projectname ${projectname} 
                -excludeclasses ${excludeclasses}: 
                -excludepackages ${excludePackages}:
              </additionalOptions>
              <doclint>none</doclint>
              <show>protected</show>
              <nohelp>true</nohelp>
              <source>${source}</source>
              <sourceFileExcludes>
                <exclude>${sourceFileExclude}</exclude>
              </sourceFileExcludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
    
    <profile>
      <id>autovalue-java8</id>
      <activation>
        <jdk>[1.8,)</jdk>
        <file>
          <exists>${basedir}/EnableAutoValue.txt</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-compiler-plugin</artifactId>
            <configuration>
              <annotationProcessorPaths>
                <path>
                  <groupId>com.google.auto.value</groupId>
                  <artifactId>auto-value</artifactId>
                  <version>${auto-value.version}</version>
                </path>
                <!--
                Manually pull in auto-service-annotations so that it is part of the
                processor path because auto-value has it set to provided scope.

                This dependency is needed due to the retention change in
                https://github.com/google/auto/commit/628df548685b4fc0f2a9af856f97cc2a68da246b
                where the RetentionPolicy changed from SOURCE to CLASS.

                Due to the RetentionPolicy change to CLASS we must have the
                annotations available on the processor path otherwise the following
                error will be thrown. (This is a particular problem with the
                annotation processor configuration in IntelliJ)

                Error:java: java.lang.NoClassDefFoundError: com/google/auto/service/AutoService
                  com.google.auto.service.AutoService
                -->
                <path>
                  <groupId>com.google.auto.service</groupId>
                  <artifactId>auto-service-annotations</artifactId>
                  <version>1.1.1</version>
                </path>
              </annotationProcessorPaths>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <!-- This profile is used to enable GraalVM native image testing -->
      <id>native</id>
      <dependencies>
        <!-- Add this dependency to resolve class org.opentest4j.TestAbortedException -->
        <dependency>
          <groupId>org.opentest4j</groupId>
          <artifactId>opentest4j</artifactId>
          <version>1.3.0</version>
        </dependency>
        <dependency>
          <groupId>org.junit.vintage</groupId>
          <artifactId>junit-vintage-engine</artifactId>
          <version>5.9.3</version>
          <scope>test</scope>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <dependencies>
              <dependency>
                <groupId>org.junit.vintage</groupId>
                <artifactId>junit-vintage-engine</artifactId>
                <version>5.9.3</version>
              </dependency>
            </dependencies>
            <configuration>
              <!-- Include all tests during native image testing. -->
              <excludes combine.self="override" />
              <includes>
                <include>**/IT*.java</include>
                <!-- Enable unit tests in generated libraries for native image testing. -->
                <include>**/*ClientTest.java</include>
              </includes>
            </configuration>
          </plugin>

          <plugin>
            <groupId>org.graalvm.buildtools</groupId>
            <artifactId>native-maven-plugin</artifactId>
            <version>0.9.23</version>
            <extensions>true</extensions>
            <executions>
              <execution>
                <id>test-native</id>
                <goals>
                  <goal>test</goal>
                </goals>
                <phase>test</phase>
              </execution>
            </executions>
            <configuration>
              <buildArgs>
                <buildArg>--no-fallback</buildArg>
                <buildArg>--no-server</buildArg>
              </buildArgs>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <!-- JDK 9+ has the "release" option to ensure the generated bytecode is
        compatible with Java 8. Maven-compiler-plugin's "<target>1.8</target>" is
        not sufficient. -->
      <id>compiler-release-8</id>
      <activation>
        <jdk>[9,]</jdk>
      </activation>
      <properties>
        <maven.compiler.release>8</maven.compiler.release>
      </properties>
    </profile>
  </profiles>
</project>
