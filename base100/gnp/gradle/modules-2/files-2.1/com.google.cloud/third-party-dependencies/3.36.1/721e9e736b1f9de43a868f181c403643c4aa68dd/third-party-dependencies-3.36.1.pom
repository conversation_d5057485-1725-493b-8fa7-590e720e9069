<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>third-party-dependencies</artifactId>
  <packaging>pom</packaging>
  <version>3.36.1</version><!-- {x-version-update:google-cloud-shared-dependencies:current} -->
  <name>Google Cloud Third-party Shared Dependencies</name>
  <description>
    Shared third-party dependencies for Google Cloud Java libraries.
  </description>

  <parent>
    <groupId>com.google.api</groupId>
    <artifactId>gapic-generator-java-pom-parent</artifactId>
    <version>2.46.1</version><!-- {x-version-update:gapic-generator-java:current} -->
    <relativePath>../../gapic-generator-java-pom-parent</relativePath>
  </parent>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <site.installationModule>${project.artifactId}</site.installationModule>

    <threeten-extra.version>1.8.0</threeten-extra.version>
    <animal-sniffer.version>1.24</animal-sniffer.version>
    <opencensus.version>0.31.1</opencensus.version>
    <findbugs.version>3.0.2</findbugs.version>
    <jackson.version>2.17.2</jackson.version>
    <errorprone.version>2.32.0</errorprone.version>
    <codec.version>1.17.1</codec.version>
    <httpcomponents.httpcore.version>4.4.16</httpcomponents.httpcore.version>
    <httpcomponents.httpclient.version>4.5.14</httpcomponents.httpclient.version>
    <!-- ensure checker-qual version matches what Guava uses -->
    <checker-qual.version>3.47.0</checker-qual.version>
    <perfmark-api.version>0.27.0</perfmark-api.version>
    <j2objc-annotations.version>3.0.0</j2objc-annotations.version>
    <google.cloud.opentelemetry.version>0.32.0</google.cloud.opentelemetry.version>
    <opentelemetry-grpc-instrumentation.version>2.1.0-alpha</opentelemetry-grpc-instrumentation.version>
    <opentelemetry-semconv.version>1.26.0-alpha</opentelemetry-semconv.version>
    <flogger.version>0.8</flogger.version>
    <arrow.version>15.0.2</arrow.version>
    <dev.cel.version>0.7.1</dev.cel.version>
    <com.google.crypto.tink.version>1.15.0</com.google.crypto.tink.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.arrow</groupId>
        <artifactId>arrow-memory-core</artifactId>
        <version>${arrow.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.arrow</groupId>
        <artifactId>arrow-memory-netty</artifactId>
        <version>${arrow.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.arrow</groupId>
        <artifactId>arrow-vector</artifactId>
        <version>${arrow.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore</artifactId>
        <version>${httpcomponents.httpcore.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>${httpcomponents.httpclient.version}</version>
      </dependency>
      <dependency>
        <groupId>org.threeten</groupId>
        <artifactId>threetenbp</artifactId>
        <version>${threetenbp.version}</version>
      </dependency>
      <dependency>
        <groupId>org.threeten</groupId>
        <artifactId>threeten-extra</artifactId>
        <version>${threeten-extra.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.annotation</groupId>
        <artifactId>javax.annotation-api</artifactId>
        <version>${javax.annotation-api.version}</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-annotations</artifactId>
        <version>${animal-sniffer.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.findbugs</groupId>
        <artifactId>jsr305</artifactId>
        <version>${findbugs.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.errorprone</groupId>
        <artifactId>error_prone_annotations</artifactId>
        <version>${errorprone.version}</version>
      </dependency>
      <!-- Legacy exemption of java-pubsublite's flogger dependencies.
      In general, let's avoid declaring logging backend dependencies -->
      <dependency>
        <groupId>com.google.flogger</groupId>
        <artifactId>google-extensions</artifactId>
        <version>${flogger.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.flogger</groupId>
        <artifactId>flogger-system-backend</artifactId>
        <version>${flogger.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson</groupId>
        <artifactId>jackson-bom</artifactId>
        <version>${jackson.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>${codec.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-bom</artifactId>
        <version>${opentelemetry.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-grpc-1.6</artifactId>
        <version>${opentelemetry-grpc-instrumentation.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-semconv</artifactId>
        <version>${opentelemetry-semconv.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud.opentelemetry</groupId>
        <artifactId>exporter-metrics</artifactId>
        <version>${google.cloud.opentelemetry.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud.opentelemetry</groupId>
        <artifactId>shared-resourcemapping</artifactId>
        <version>${google.cloud.opentelemetry.version}</version>
      </dependency>

      <!-- TODO: replace with opencensus-bom when available -->
      <dependency>
        <groupId>io.opencensus</groupId>
        <artifactId>opencensus-api</artifactId>
        <version>${opencensus.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opencensus</groupId>
        <artifactId>opencensus-contrib-grpc-util</artifactId>
        <version>${opencensus.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opencensus</groupId>
        <artifactId>opencensus-contrib-http-util</artifactId>
        <version>${opencensus.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opencensus</groupId>
        <artifactId>opencensus-contrib-zpages</artifactId>
        <version>${opencensus.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opencensus</groupId>
        <artifactId>opencensus-exporter-stats-stackdriver</artifactId>
        <version>${opencensus.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opencensus</groupId>
        <artifactId>opencensus-exporter-trace-stackdriver</artifactId>
        <version>${opencensus.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opencensus</groupId>
        <artifactId>opencensus-impl</artifactId>
        <version>${opencensus.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opencensus</groupId>
        <artifactId>opencensus-impl-core</artifactId>
        <version>${opencensus.version}</version>
      </dependency>
      <dependency>
        <groupId>org.checkerframework</groupId>
        <artifactId>checker-qual</artifactId>
        <version>${checker-qual.version}</version>
      </dependency>
      <dependency>
        <groupId>io.perfmark</groupId>
        <artifactId>perfmark-api</artifactId>
        <version>${perfmark-api.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.j2objc</groupId>
        <artifactId>j2objc-annotations</artifactId>
        <version>${j2objc-annotations.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud.opentelemetry</groupId>
        <artifactId>detector-resources-support</artifactId>
        <version>${google.cloud.opentelemetry.version}</version>
      </dependency>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>${junit.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>dev.cel</groupId>
        <artifactId>cel</artifactId>
        <version>${dev.cel.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.crypto.tink</groupId>
        <artifactId>tink</artifactId>
        <version>${com.google.crypto.tink.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
