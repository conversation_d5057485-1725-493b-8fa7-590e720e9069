<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-pubsub-bom</artifactId>
  <version>1.136.1</version><!-- {x-version-update:google-cloud-pubsub:current} -->
  <packaging>pom</packaging>
  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>sdk-platform-java-config</artifactId>
    <version>3.42.0</version>
  </parent>

  <name>Google Cloud pubsub BOM</name>
  <url>https://github.com/googleapis/java-pubsub</url>
  <description>
    BOM for Google Cloud Pub/Sub
  </description>

  <organization>
    <name>Google LLC</name>
  </organization>

  <developers>
    <developer>
      <id>chingor13</id>
      <name>Jeff Ching</name>
      <email><EMAIL></email>
      <organization>Google LLC</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>

  <scm>
    <connection>scm:git:https://github.com/googleapis/java-pubsub.git</connection>
    <developerConnection>scm:git:**************:googleapis/java-pubsub.git</developerConnection>
    <url>https://github.com/googleapis/java-pubsub</url>
  </scm>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-pubsub</artifactId>
        <version>1.136.1</version><!-- {x-version-update:google-cloud-pubsub:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-pubsub-v1</artifactId>
        <version>1.118.1</version><!-- {x-version-update:grpc-google-cloud-pubsub-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-pubsub-v1</artifactId>
        <version>1.118.1</version><!-- {x-version-update:proto-google-cloud-pubsub-v1:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
