<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-shared-dependencies</artifactId>
  <packaging>pom</packaging>
  <version>3.36.1</version><!-- {x-version-update:google-cloud-shared-dependencies:current} -->
  <modules>
    <module>first-party-dependencies</module>
    <module>third-party-dependencies</module>
  </modules>
  <name>Google Cloud Shared Dependencies</name>
  <description>
    Shared build configuration for Google Cloud Java libraries.
  </description>

  <parent>
    <groupId>com.google.api</groupId>
    <artifactId>gapic-generator-java-pom-parent</artifactId>
    <version>2.46.1</version><!-- {x-version-update:gapic-generator-java:current} -->
    <relativePath>../gapic-generator-java-pom-parent</relativePath>
  </parent>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <site.installationModule>${project.artifactId}</site.installationModule>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>first-party-dependencies</artifactId>
        <version>3.36.1</version><!-- {x-version-update:google-cloud-shared-dependencies:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>third-party-dependencies</artifactId>
        <version>3.36.1</version><!-- {x-version-update:google-cloud-shared-dependencies:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
