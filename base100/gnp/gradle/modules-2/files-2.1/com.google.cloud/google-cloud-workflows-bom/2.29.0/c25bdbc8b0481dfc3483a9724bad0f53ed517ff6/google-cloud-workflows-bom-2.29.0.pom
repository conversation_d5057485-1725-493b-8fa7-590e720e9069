<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-workflows-bom</artifactId>
  <version>2.29.0</version><!-- {x-version-update:google-cloud-workflows:current} -->
  <packaging>pom</packaging>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-pom-parent</artifactId>
    <version>1.23.0</version><!-- {x-version-update:google-cloud-java:current} -->
    <relativePath>../../google-cloud-pom-parent/pom.xml</relativePath>
  </parent>

  <name>Google Cloud Workflows BOM</name>
  <description>
    BOM for Cloud Workflows
  </description>

  <properties>
    <maven.antrun.skip>true</maven.antrun.skip>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-workflows</artifactId>
        <version>2.29.0</version><!-- {x-version-update:google-cloud-workflows:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-workflows-v1beta</artifactId>
        <version>0.35.0</version><!-- {x-version-update:grpc-google-cloud-workflows-v1beta:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-workflows-v1</artifactId>
        <version>2.29.0</version><!-- {x-version-update:grpc-google-cloud-workflows-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-workflows-v1beta</artifactId>
        <version>0.35.0</version><!-- {x-version-update:proto-google-cloud-workflows-v1beta:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-workflows-v1</artifactId>
        <version>2.29.0</version><!-- {x-version-update:proto-google-cloud-workflows-v1:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
