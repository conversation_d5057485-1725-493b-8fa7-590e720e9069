<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>gapic-libraries-bom</artifactId>
  <packaging>pom</packaging>
  <version>1.51.0</version><!-- {x-version-update:google-cloud-java:current} -->
  <name>Google Cloud Java BOM</name>
  <description>
    BOM for the libraries in google-cloud-java repository. Users should not
    depend on this artifact explicitly because this BOM is an implementation
    detail of the Libraries BOM.
  </description>

  <parent>
    <artifactId>google-cloud-pom-parent</artifactId>
    <groupId>com.google.cloud</groupId>
    <version>1.51.0</version><!-- {x-version-update:google-cloud-java:current} -->
    <relativePath>../google-cloud-pom-parent/pom.xml</relativePath>
  </parent>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.analytics</groupId>
        <artifactId>google-analytics-admin-bom</artifactId>
        <version>0.67.0</version><!-- {x-version-update:google-analytics-admin:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.analytics</groupId>
        <artifactId>google-analytics-data-bom</artifactId>
        <version>0.68.0</version><!-- {x-version-update:google-analytics-data:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.area120</groupId>
        <artifactId>google-area120-tables-bom</artifactId>
        <version>0.61.0</version><!-- {x-version-update:google-area120-tables:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-accessapproval-bom</artifactId>
        <version>2.58.0</version><!-- {x-version-update:google-cloud-accessapproval:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-advisorynotifications-bom</artifactId>
        <version>0.46.0</version><!-- {x-version-update:google-cloud-advisorynotifications:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-aiplatform-bom</artifactId>
        <version>3.58.0</version><!-- {x-version-update:google-cloud-aiplatform:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-alloydb-bom</artifactId>
        <version>0.46.0</version><!-- {x-version-update:google-cloud-alloydb:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-alloydb-connectors-bom</artifactId>
        <version>0.35.0</version><!-- {x-version-update:google-cloud-alloydb-connectors:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-analyticshub-bom</artifactId>
        <version>0.54.0</version><!-- {x-version-update:google-cloud-analyticshub:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-api-gateway-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-api-gateway:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-apigee-connect-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-apigee-connect:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-apigee-registry-bom</artifactId>
        <version>0.57.0</version><!-- {x-version-update:google-cloud-apigee-registry:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-apihub-bom</artifactId>
        <version>0.10.0</version><!-- {x-version-update:google-cloud-apihub:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-apikeys-bom</artifactId>
        <version>0.55.0</version><!-- {x-version-update:google-cloud-apikeys:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-appengine-admin-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-appengine-admin:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-apphub-bom</artifactId>
        <version>0.21.0</version><!-- {x-version-update:google-cloud-apphub:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-artifact-registry-bom</artifactId>
        <version>1.56.0</version><!-- {x-version-update:google-cloud-artifact-registry:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-asset-bom</artifactId>
        <version>3.61.0</version><!-- {x-version-update:google-cloud-asset:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-assured-workloads-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-assured-workloads:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-automl-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-automl:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-backupdr-bom</artifactId>
        <version>0.16.0</version><!-- {x-version-update:google-cloud-backupdr:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bare-metal-solution-bom</artifactId>
        <version>0.57.0</version><!-- {x-version-update:google-cloud-bare-metal-solution:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-batch-bom</artifactId>
        <version>0.57.0</version><!-- {x-version-update:google-cloud-batch:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-beyondcorp-appconnections-bom</artifactId>
        <version>0.55.0</version><!-- {x-version-update:google-cloud-beyondcorp-appconnections:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-beyondcorp-appconnectors-bom</artifactId>
        <version>0.55.0</version><!-- {x-version-update:google-cloud-beyondcorp-appconnectors:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-beyondcorp-appgateways-bom</artifactId>
        <version>0.55.0</version><!-- {x-version-update:google-cloud-beyondcorp-appgateways:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-beyondcorp-clientconnectorservices-bom</artifactId>
        <version>0.55.0</version><!-- {x-version-update:google-cloud-beyondcorp-clientconnectorservices:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-beyondcorp-clientgateways-bom</artifactId>
        <version>0.55.0</version><!-- {x-version-update:google-cloud-beyondcorp-clientgateways:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-biglake-bom</artifactId>
        <version>0.45.0</version><!-- {x-version-update:google-cloud-biglake:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigquery-data-exchange-bom</artifactId>
        <version>2.52.0</version><!-- {x-version-update:google-cloud-bigquery-data-exchange:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigqueryconnection-bom</artifactId>
        <version>2.59.0</version><!-- {x-version-update:google-cloud-bigqueryconnection:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigquerydatapolicy-bom</artifactId>
        <version>0.54.0</version><!-- {x-version-update:google-cloud-bigquerydatapolicy:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigquerydatatransfer-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-bigquerydatatransfer:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigquerymigration-bom</artifactId>
        <version>0.60.0</version><!-- {x-version-update:google-cloud-bigquerymigration:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigqueryreservation-bom</artifactId>
        <version>2.58.0</version><!-- {x-version-update:google-cloud-bigqueryreservation:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-billing-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-billing:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-billingbudgets-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-billingbudgets:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-binary-authorization-bom</artifactId>
        <version>1.56.0</version><!-- {x-version-update:google-cloud-binary-authorization:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-build-bom</artifactId>
        <version>3.59.0</version><!-- {x-version-update:google-cloud-build:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-certificate-manager-bom</artifactId>
        <version>0.60.0</version><!-- {x-version-update:google-cloud-certificate-manager:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-channel-bom</artifactId>
        <version>3.61.0</version><!-- {x-version-update:google-cloud-channel:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-chat-bom</artifactId>
        <version>0.21.0</version><!-- {x-version-update:google-cloud-chat:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-cloudcommerceconsumerprocurement-bom</artifactId>
        <version>0.55.0</version><!-- {x-version-update:google-cloud-cloudcommerceconsumerprocurement:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-cloudcontrolspartner-bom</artifactId>
        <version>0.21.0</version><!-- {x-version-update:google-cloud-cloudcontrolspartner:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-cloudquotas-bom</artifactId>
        <version>0.25.0</version><!-- {x-version-update:google-cloud-cloudquotas:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-cloudsupport-bom</artifactId>
        <version>0.41.0</version><!-- {x-version-update:google-cloud-cloudsupport:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-compute-bom</artifactId>
        <version>1.67.0</version><!-- {x-version-update:google-cloud-compute:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-confidentialcomputing-bom</artifactId>
        <version>0.43.0</version><!-- {x-version-update:google-cloud-confidentialcomputing:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-connectgateway-bom</artifactId>
        <version>0.9.0</version><!-- {x-version-update:google-cloud-connectgateway:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-contact-center-insights-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-contact-center-insights:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-container-bom</artifactId>
        <version>2.60.0</version><!-- {x-version-update:google-cloud-container:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-containeranalysis-bom</artifactId>
        <version>2.58.0</version><!-- {x-version-update:google-cloud-containeranalysis:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-contentwarehouse-bom</artifactId>
        <version>0.53.0</version><!-- {x-version-update:google-cloud-contentwarehouse:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-data-fusion-bom</artifactId>
        <version>1.57.0</version><!-- {x-version-update:google-cloud-data-fusion:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-datacatalog-bom</artifactId>
        <version>1.63.0</version><!-- {x-version-update:google-cloud-datacatalog:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dataflow-bom</artifactId>
        <version>0.61.0</version><!-- {x-version-update:google-cloud-dataflow:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dataform-bom</artifactId>
        <version>0.56.0</version><!-- {x-version-update:google-cloud-dataform:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-datalabeling-bom</artifactId>
        <version>0.177.0</version><!-- {x-version-update:google-cloud-datalabeling:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-datalineage-bom</artifactId>
        <version>0.49.0</version><!-- {x-version-update:google-cloud-datalineage:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dataplex-bom</artifactId>
        <version>1.55.0</version><!-- {x-version-update:google-cloud-dataplex:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dataproc-bom</artifactId>
        <version>4.54.0</version><!-- {x-version-update:google-cloud-dataproc:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dataproc-metastore-bom</artifactId>
        <version>2.58.0</version><!-- {x-version-update:google-cloud-dataproc-metastore:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-datastream-bom</artifactId>
        <version>1.56.0</version><!-- {x-version-update:google-cloud-datastream:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-debugger-client-bom</artifactId>
        <version>1.57.0</version><!-- {x-version-update:google-cloud-debugger-client:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-deploy-bom</artifactId>
        <version>1.55.0</version><!-- {x-version-update:google-cloud-deploy:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-developerconnect-bom</artifactId>
        <version>0.14.0</version><!-- {x-version-update:google-cloud-developerconnect:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dialogflow-bom</artifactId>
        <version>4.63.0</version><!-- {x-version-update:google-cloud-dialogflow:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dialogflow-cx-bom</artifactId>
        <version>0.68.0</version><!-- {x-version-update:google-cloud-dialogflow-cx:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-discoveryengine-bom</artifactId>
        <version>0.53.0</version><!-- {x-version-update:google-cloud-discoveryengine:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-distributedcloudedge-bom</artifactId>
        <version>0.54.0</version><!-- {x-version-update:google-cloud-distributedcloudedge:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dlp-bom</artifactId>
        <version>3.61.0</version><!-- {x-version-update:google-cloud-dlp:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dms-bom</artifactId>
        <version>2.56.0</version><!-- {x-version-update:google-cloud-dms:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-dns</artifactId>
        <version>2.55.0</version><!-- {x-version-update:google-cloud-dns:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-document-ai-bom</artifactId>
        <version>2.61.0</version><!-- {x-version-update:google-cloud-document-ai:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-domains-bom</artifactId>
        <version>1.54.0</version><!-- {x-version-update:google-cloud-domains:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-edgenetwork-bom</artifactId>
        <version>0.25.0</version><!-- {x-version-update:google-cloud-edgenetwork:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-enterpriseknowledgegraph-bom</artifactId>
        <version>0.53.0</version><!-- {x-version-update:google-cloud-enterpriseknowledgegraph:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-errorreporting-bom</artifactId>
        <version>0.178.0-beta</version><!-- {x-version-update:google-cloud-errorreporting:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-essential-contacts-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-essential-contacts:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-eventarc-bom</artifactId>
        <version>1.57.0</version><!-- {x-version-update:google-cloud-eventarc:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-eventarc-publishing-bom</artifactId>
        <version>0.57.0</version><!-- {x-version-update:google-cloud-eventarc-publishing:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-filestore-bom</artifactId>
        <version>1.58.0</version><!-- {x-version-update:google-cloud-filestore:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-functions-bom</artifactId>
        <version>2.59.0</version><!-- {x-version-update:google-cloud-functions:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-gdchardwaremanagement-bom</artifactId>
        <version>0.12.0</version><!-- {x-version-update:google-cloud-gdchardwaremanagement:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-gke-backup-bom</artifactId>
        <version>0.56.0</version><!-- {x-version-update:google-cloud-gke-backup:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-gke-connect-gateway-bom</artifactId>
        <version>0.58.0</version><!-- {x-version-update:google-cloud-gke-connect-gateway:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-gke-multi-cloud-bom</artifactId>
        <version>0.56.0</version><!-- {x-version-update:google-cloud-gke-multi-cloud:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-gkehub-bom</artifactId>
        <version>1.57.0</version><!-- {x-version-update:google-cloud-gkehub:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-gsuite-addons-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-gsuite-addons:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-iamcredentials-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-iamcredentials:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-iap-bom</artifactId>
        <version>0.13.0</version><!-- {x-version-update:google-cloud-iap:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-ids-bom</artifactId>
        <version>1.56.0</version><!-- {x-version-update:google-cloud-ids:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-infra-manager-bom</artifactId>
        <version>0.34.0</version><!-- {x-version-update:google-cloud-infra-manager:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-iot-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-iot:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-kms-bom</artifactId>
        <version>2.60.0</version><!-- {x-version-update:google-cloud-kms:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-kmsinventory-bom</artifactId>
        <version>0.46.0</version><!-- {x-version-update:google-cloud-kmsinventory:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-language-bom</artifactId>
        <version>2.58.0</version><!-- {x-version-update:google-cloud-language:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-life-sciences-bom</artifactId>
        <version>0.59.0</version><!-- {x-version-update:google-cloud-life-sciences:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-live-stream-bom</artifactId>
        <version>0.59.0</version><!-- {x-version-update:google-cloud-live-stream:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-managed-identities-bom</artifactId>
        <version>1.55.0</version><!-- {x-version-update:google-cloud-managed-identities:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-managedkafka-bom</artifactId>
        <version>0.13.0</version><!-- {x-version-update:google-cloud-managedkafka:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-mediatranslation-bom</artifactId>
        <version>0.63.0</version><!-- {x-version-update:google-cloud-mediatranslation:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-meet-bom</artifactId>
        <version>0.24.0</version><!-- {x-version-update:google-cloud-meet:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-memcache-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-memcache:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-migrationcenter-bom</artifactId>
        <version>0.39.0</version><!-- {x-version-update:google-cloud-migrationcenter:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-monitoring-bom</artifactId>
        <version>3.58.0</version><!-- {x-version-update:google-cloud-monitoring:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-monitoring-dashboard-bom</artifactId>
        <version>2.59.0</version><!-- {x-version-update:google-cloud-monitoring-dashboard:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-monitoring-metricsscope-bom</artifactId>
        <version>0.51.0</version><!-- {x-version-update:google-cloud-monitoring-metricsscope:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-netapp-bom</artifactId>
        <version>0.36.0</version><!-- {x-version-update:google-cloud-netapp:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-network-management-bom</artifactId>
        <version>1.58.0</version><!-- {x-version-update:google-cloud-network-management:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-network-security-bom</artifactId>
        <version>0.60.0</version><!-- {x-version-update:google-cloud-network-security:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-networkconnectivity-bom</artifactId>
        <version>1.56.0</version><!-- {x-version-update:google-cloud-networkconnectivity:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-networkservices-bom</artifactId>
        <version>0.13.0</version><!-- {x-version-update:google-cloud-networkservices:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-notebooks-bom</artifactId>
        <version>1.55.0</version><!-- {x-version-update:google-cloud-notebooks:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-notification</artifactId>
        <version>0.175.0-beta</version><!-- {x-version-update:google-cloud-notification:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-optimization-bom</artifactId>
        <version>1.55.0</version><!-- {x-version-update:google-cloud-optimization:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-oracledatabase-bom</artifactId>
        <version>0.6.0</version><!-- {x-version-update:google-cloud-oracledatabase:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-orchestration-airflow-bom</artifactId>
        <version>1.57.0</version><!-- {x-version-update:google-cloud-orchestration-airflow:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-orgpolicy-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-orgpolicy:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-os-config-bom</artifactId>
        <version>2.59.0</version><!-- {x-version-update:google-cloud-os-config:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-os-login-bom</artifactId>
        <version>2.56.0</version><!-- {x-version-update:google-cloud-os-login:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-parallelstore-bom</artifactId>
        <version>0.20.0</version><!-- {x-version-update:google-cloud-parallelstore:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-parametermanager-bom</artifactId>
        <version>0.1.0</version><!-- {x-version-update:google-cloud-parametermanager:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-phishingprotection-bom</artifactId>
        <version>0.88.0</version><!-- {x-version-update:google-cloud-phishingprotection:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-policy-troubleshooter-bom</artifactId>
        <version>1.56.0</version><!-- {x-version-update:google-cloud-policy-troubleshooter:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-policysimulator-bom</artifactId>
        <version>0.36.0</version><!-- {x-version-update:google-cloud-policysimulator:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-private-catalog-bom</artifactId>
        <version>0.59.0</version><!-- {x-version-update:google-cloud-private-catalog:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-privilegedaccessmanager-bom</artifactId>
        <version>0.11.0</version><!-- {x-version-update:google-cloud-privilegedaccessmanager:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-profiler-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-profiler:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-publicca-bom</artifactId>
        <version>0.54.0</version><!-- {x-version-update:google-cloud-publicca:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-rapidmigrationassessment-bom</artifactId>
        <version>0.40.0</version><!-- {x-version-update:google-cloud-rapidmigrationassessment:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-recaptchaenterprise-bom</artifactId>
        <version>3.54.0</version><!-- {x-version-update:google-cloud-recaptchaenterprise:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-recommendations-ai-bom</artifactId>
        <version>0.64.0</version><!-- {x-version-update:google-cloud-recommendations-ai:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-recommender-bom</artifactId>
        <version>2.59.0</version><!-- {x-version-update:google-cloud-recommender:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-redis-bom</artifactId>
        <version>2.60.0</version><!-- {x-version-update:google-cloud-redis:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-redis-cluster-bom</artifactId>
        <version>0.29.0</version><!-- {x-version-update:google-cloud-redis-cluster:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-resource-settings-bom</artifactId>
        <version>1.57.0</version><!-- {x-version-update:google-cloud-resource-settings:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-resourcemanager-bom</artifactId>
        <version>1.59.0</version><!-- {x-version-update:google-cloud-resourcemanager:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-retail-bom</artifactId>
        <version>2.59.0</version><!-- {x-version-update:google-cloud-retail:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-run-bom</artifactId>
        <version>0.57.0</version><!-- {x-version-update:google-cloud-run:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-scheduler-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-scheduler:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-secretmanager-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-secretmanager:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-securesourcemanager-bom</artifactId>
        <version>0.27.0</version><!-- {x-version-update:google-cloud-securesourcemanager:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-security-private-ca-bom</artifactId>
        <version>2.59.0</version><!-- {x-version-update:google-cloud-security-private-ca:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-securitycenter-bom</artifactId>
        <version>2.65.0</version><!-- {x-version-update:google-cloud-securitycenter:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-securitycenter-settings-bom</artifactId>
        <version>0.60.0</version><!-- {x-version-update:google-cloud-securitycenter-settings:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-securitycentermanagement-bom</artifactId>
        <version>0.25.0</version><!-- {x-version-update:google-cloud-securitycentermanagement:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-securityposture-bom</artifactId>
        <version>0.22.0</version><!-- {x-version-update:google-cloud-securityposture:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-service-control-bom</artifactId>
        <version>1.57.0</version><!-- {x-version-update:google-cloud-service-control:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-service-management-bom</artifactId>
        <version>3.55.0</version><!-- {x-version-update:google-cloud-service-management:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-service-usage-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-service-usage:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-servicedirectory-bom</artifactId>
        <version>2.58.0</version><!-- {x-version-update:google-cloud-servicedirectory:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-servicehealth-bom</artifactId>
        <version>0.24.0</version><!-- {x-version-update:google-cloud-servicehealth:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-shell-bom</artifactId>
        <version>2.56.0</version><!-- {x-version-update:google-cloud-shell:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-speech-bom</artifactId>
        <version>4.52.0</version><!-- {x-version-update:google-cloud-speech:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-storage-transfer-bom</artifactId>
        <version>1.57.0</version><!-- {x-version-update:google-cloud-storage-transfer:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-storageinsights-bom</artifactId>
        <version>0.42.0</version><!-- {x-version-update:google-cloud-storageinsights:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-talent-bom</artifactId>
        <version>2.58.0</version><!-- {x-version-update:google-cloud-talent:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-tasks-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-tasks:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-telcoautomation-bom</artifactId>
        <version>0.27.0</version><!-- {x-version-update:google-cloud-telcoautomation:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-texttospeech-bom</artifactId>
        <version>2.58.0</version><!-- {x-version-update:google-cloud-texttospeech:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-tpu-bom</artifactId>
        <version>2.58.0</version><!-- {x-version-update:google-cloud-tpu:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-trace-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-trace:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-translate-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-translate:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-valkey-bom</artifactId>
        <version>0.3.0</version><!-- {x-version-update:google-cloud-valkey:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-vertexai-bom</artifactId>
        <version>1.17.0</version><!-- {x-version-update:google-cloud-vertexai:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-video-intelligence-bom</artifactId>
        <version>2.56.0</version><!-- {x-version-update:google-cloud-video-intelligence:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-video-stitcher-bom</artifactId>
        <version>0.57.0</version><!-- {x-version-update:google-cloud-video-stitcher:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-video-transcoder-bom</artifactId>
        <version>1.56.0</version><!-- {x-version-update:google-cloud-video-transcoder:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-vision-bom</artifactId>
        <version>3.55.0</version><!-- {x-version-update:google-cloud-vision:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-visionai-bom</artifactId>
        <version>0.14.0</version><!-- {x-version-update:google-cloud-visionai:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-vmmigration-bom</artifactId>
        <version>1.57.0</version><!-- {x-version-update:google-cloud-vmmigration:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-vmwareengine-bom</artifactId>
        <version>0.51.0</version><!-- {x-version-update:google-cloud-vmwareengine:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-vpcaccess-bom</artifactId>
        <version>2.58.0</version><!-- {x-version-update:google-cloud-vpcaccess:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-webrisk-bom</artifactId>
        <version>2.56.0</version><!-- {x-version-update:google-cloud-webrisk:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-websecurityscanner-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-websecurityscanner:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-workflow-executions-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-workflow-executions:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-workflows-bom</artifactId>
        <version>2.57.0</version><!-- {x-version-update:google-cloud-workflows:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-workspaceevents-bom</artifactId>
        <version>0.21.0</version><!-- {x-version-update:google-cloud-workspaceevents:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-workstations-bom</artifactId>
        <version>0.45.0</version><!-- {x-version-update:google-cloud-workstations:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-iam-admin-bom</artifactId>
        <version>3.52.0</version><!-- {x-version-update:google-iam-admin:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-iam-policy-bom</artifactId>
        <version>1.55.0</version><!-- {x-version-update:google-iam-policy:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-identity-accesscontextmanager-bom</artifactId>
        <version>1.58.0</version><!-- {x-version-update:google-identity-accesscontextmanager:current} -->
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.grafeas</groupId>
        <artifactId>grafeas</artifactId>
        <version>2.58.0</version><!-- {x-version-update:grafeas:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>