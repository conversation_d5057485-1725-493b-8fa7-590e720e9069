<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-redis-bom</artifactId>
  <version>2.60.0</version><!-- {x-version-update:google-cloud-redis:current} -->
  <packaging>pom</packaging>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-pom-parent</artifactId>
    <version>1.51.0</version><!-- {x-version-update:google-cloud-java:current} -->
    <relativePath>../../google-cloud-pom-parent/pom.xml</relativePath>
  </parent>

  <name>Google Cloud Redis BOM</name>
  <description>
    BOM for Google Cloud Redis
  </description>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-redis</artifactId>
        <version>2.60.0</version><!-- {x-version-update:google-cloud-redis:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-redis-v1beta1</artifactId>
        <version>0.148.0</version><!-- {x-version-update:grpc-google-cloud-redis-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-redis-v1</artifactId>
        <version>2.60.0</version><!-- {x-version-update:grpc-google-cloud-redis-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-redis-v1</artifactId>
        <version>2.60.0</version><!-- {x-version-update:proto-google-cloud-redis-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-redis-v1beta1</artifactId>
        <version>0.148.0</version><!-- {x-version-update:proto-google-cloud-redis-v1beta1:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
