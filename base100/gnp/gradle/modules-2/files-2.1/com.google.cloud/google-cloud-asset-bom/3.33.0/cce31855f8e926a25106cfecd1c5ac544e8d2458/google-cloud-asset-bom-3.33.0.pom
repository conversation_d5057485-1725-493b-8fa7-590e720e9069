<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-asset-bom</artifactId>
  <version>3.33.0</version><!-- {x-version-update:google-cloud-asset:current} -->
  <packaging>pom</packaging>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-pom-parent</artifactId>
    <version>1.23.0</version><!-- {x-version-update:google-cloud-java:current} -->
    <relativePath>../../google-cloud-pom-parent/pom.xml</relativePath>
  </parent>

  <name>Google Cloud Asset BOM</name>
  <description>
    BOM for Google Cloud Asset
  </description>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-asset</artifactId>
        <version>3.33.0</version><!-- {x-version-update:google-cloud-asset:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-asset-v1</artifactId>
        <version>3.33.0</version><!-- {x-version-update:grpc-google-cloud-asset-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-asset-v1p1beta1</artifactId>
        <version>0.133.0</version><!-- {x-version-update:grpc-google-cloud-asset-v1p1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-asset-v1p2beta1</artifactId>
        <version>0.133.0</version><!-- {x-version-update:grpc-google-cloud-asset-v1p2beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-asset-v1p5beta1</artifactId>
        <version>0.133.0</version><!-- {x-version-update:grpc-google-cloud-asset-v1p5beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-asset-v1p7beta1</artifactId>
        <version>3.33.0</version><!-- {x-version-update:grpc-google-cloud-asset-v1p7beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-asset-v1</artifactId>
        <version>3.33.0</version><!-- {x-version-update:proto-google-cloud-asset-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-asset-v1p1beta1</artifactId>
        <version>0.133.0</version><!-- {x-version-update:proto-google-cloud-asset-v1p1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-asset-v1p2beta1</artifactId>
        <version>0.133.0</version><!-- {x-version-update:proto-google-cloud-asset-v1p2beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-asset-v1p5beta1</artifactId>
        <version>0.133.0</version><!-- {x-version-update:proto-google-cloud-asset-v1p5beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-asset-v1p7beta1</artifactId>
        <version>3.33.0</version><!-- {x-version-update:proto-google-cloud-asset-v1p7beta1:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
