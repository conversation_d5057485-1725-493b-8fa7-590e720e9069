<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-os-config-bom</artifactId>
  <version>2.31.0</version><!-- {x-version-update:google-cloud-os-config:current} -->
  <packaging>pom</packaging>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-pom-parent</artifactId>
    <version>1.23.0</version><!-- {x-version-update:google-cloud-java:current} -->
    <relativePath>../../google-cloud-pom-parent/pom.xml</relativePath>
  </parent>

  <name>Google OS Config API BOM</name>
  <description>
    BOM for OS Config API
  </description>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-os-config</artifactId>
        <version>2.31.0</version><!-- {x-version-update:google-cloud-os-config:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-os-config-v1</artifactId>
        <version>2.31.0</version><!-- {x-version-update:grpc-google-cloud-os-config-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-os-config-v1beta</artifactId>
        <version>2.31.0</version><!-- {x-version-update:grpc-google-cloud-os-config-v1beta:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-os-config-v1alpha</artifactId>
        <version>2.31.0</version><!-- {x-version-update:grpc-google-cloud-os-config-v1alpha:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-os-config-v1</artifactId>
        <version>2.31.0</version><!-- {x-version-update:proto-google-cloud-os-config-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-os-config-v1alpha</artifactId>
        <version>2.31.0</version><!-- {x-version-update:proto-google-cloud-os-config-v1alpha:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-os-config-v1beta</artifactId>
        <version>2.31.0</version><!-- {x-version-update:proto-google-cloud-os-config-v1beta:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
