<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-gkehub-bom</artifactId>
  <version>1.29.0</version><!-- {x-version-update:google-cloud-gkehub:current} -->
  <packaging>pom</packaging>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-pom-parent</artifactId>
    <version>1.23.0</version><!-- {x-version-update:google-cloud-java:current} -->
    <relativePath>../../google-cloud-pom-parent/pom.xml</relativePath>
  </parent>

  <name>Google GKE Hub API BOM</name>
  <description>
    BOM for GKE Hub API
  </description>

  <properties>
    <maven.antrun.skip>true</maven.antrun.skip>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-gkehub</artifactId>
        <version>1.29.0</version><!-- {x-version-update:google-cloud-gkehub:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-gkehub-v1beta1</artifactId>
        <version>0.35.0</version><!-- {x-version-update:grpc-google-cloud-gkehub-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-gkehub-v1</artifactId>
        <version>1.29.0</version><!-- {x-version-update:grpc-google-cloud-gkehub-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-gkehub-v1alpha</artifactId>
        <version>0.35.0</version><!-- {x-version-update:grpc-google-cloud-gkehub-v1alpha:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-gkehub-v1beta</artifactId>
        <version>0.35.0</version><!-- {x-version-update:grpc-google-cloud-gkehub-v1beta:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-gkehub-v1alpha2</artifactId>
        <version>0.35.0</version><!-- {x-version-update:grpc-google-cloud-gkehub-v1alpha2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-gkehub-v1beta1</artifactId>
        <version>0.35.0</version><!-- {x-version-update:proto-google-cloud-gkehub-v1beta1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-gkehub-v1</artifactId>
        <version>1.29.0</version><!-- {x-version-update:proto-google-cloud-gkehub-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-gkehub-v1alpha</artifactId>
        <version>0.35.0</version><!-- {x-version-update:proto-google-cloud-gkehub-v1alpha:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-gkehub-v1alpha2</artifactId>
        <version>0.35.0</version><!-- {x-version-update:proto-google-cloud-gkehub-v1alpha2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-gkehub-v1beta</artifactId>
        <version>0.35.0</version><!-- {x-version-update:proto-google-cloud-gkehub-v1beta:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
