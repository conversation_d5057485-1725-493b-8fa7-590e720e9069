<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-bom</artifactId>
  <packaging>pom</packaging>
  <version>0.235.0</version><!-- {x-version-update:google-cloud-bom:current} -->
  <name>Google Cloud Java BOM</name>
  <url>https://github.com/googleapis/java-cloud-bom</url>
  <description>
    BOM for google-cloud-java
  </description>
  <developers>
    <developer>
      <id>garrettjonesgoogle</id>
      <name><PERSON></name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>pongad</id>
      <name>Michael Darakananda</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>shinfan</id>
      <name>Shin Fan</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>michaelbausor</id>
      <name>Micheal Bausor</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>vam-google</id>
      <name>Vadym Matsishevskyi</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>tswast</id>
      <name>Tim Swast</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>neozwu</id>
      <name>Neo Wu</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>lesv</id>
      <name>Les Vogel</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>schmidt_sebastian</id>
      <name>Sebastian Schmidt</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>andreamlin</id>
      <name>Andrea Lin</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>hzyi-google</id>
      <name>Hanzhen Yi</name>
      <email><EMAIL></email>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>jart</id>
      <name>Justine Tunney</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>jean-philippe-martin</id>
      <name>Jean-Philippe Martin</name>
      <email><EMAIL></email>
      <organization>Verily</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>neenushaji</id>
      <name>Neenu Shaji</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <organization>
    <name>Google</name>
  </organization>
  <scm>
    <connection>scm:git:**************:googleapis/java-cloud-bom.git</connection>
    <developerConnection>scm:git:**************:googleapis/java-cloud-bom-java.git</developerConnection>
    <url>https://github.com/googleapis/java-cloud-bom</url>
    <tag>HEAD</tag>
  </scm>
  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <url>https://google.oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://google.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>
  <licenses>
    <license>
      <name>Google Cloud Software License</name>
      <url>https://raw.githubusercontent.com/googleapis/google-cloud-java/main/LICENSE</url>
    </license>
  </licenses>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <!-- Artifacts from google-cloud-java monorepo -->
        <groupId>com.google.cloud</groupId>
        <artifactId>gapic-libraries-bom</artifactId>
        <version>1.51.0</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigquery</artifactId>
        <version>2.47.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigquerystorage-bom</artifactId>
        <version>3.11.2</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-bigtable-bom</artifactId>
        <version>2.51.2</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-datastore-bom</artifactId>
        <version>2.26.0</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-firestore-bom</artifactId>
        <version>3.30.6</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-logging-bom</artifactId>
        <version>3.21.2</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-logging-logback</artifactId>
        <version>0.132.2-alpha</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-nio</artifactId>
        <version>0.127.30</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-pubsub-bom</artifactId>
        <version>1.136.1</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-pubsublite-bom</artifactId>
        <version>1.15.2</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-spanner-bom</artifactId>
        <version>6.86.0</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-spanner-jdbc</artifactId>
        <version>2.26.1</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-storage-bom</artifactId>
        <version>2.48.1</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <version>1.7.0</version>
        <extensions>true</extensions>
        <configuration>
          <serverId>sonatype-nexus-staging</serverId>
          <nexusUrl>https://google.oss.sonatype.org/</nexusUrl>
          <autoReleaseAfterClose>false</autoReleaseAfterClose>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <version>3.21.0</version>
        <configuration>
          <skipDeploy>true</skipDeploy>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.coveo</groupId>
        <artifactId>fmt-maven-plugin</artifactId>
        <version>2.13</version>
        <configuration>
          <style>google</style>
          <verbose>true</verbose>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>com.google.googlejavaformat</groupId>
            <artifactId>google-java-format</artifactId>
            <version>1.24.0</version>
          </dependency>
        </dependencies>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <version>3.8.0</version>
        <reportSets>
          <reportSet>
            <reports>
              <report>index</report>
              <report>dependency-info</report>
              <report>team</report>
              <report>ci-management</report>
              <report>issue-management</report>
              <report>licenses</report>
              <report>scm</report>
              <report>dependency-management</report>
              <report>distribution-management</report>
              <report>summary</report>
              <report>modules</report>
            </reports>
          </reportSet>
        </reportSets>
        <configuration>
          <skip>true</skip>
          <dependencyDetailsEnabled>true</dependencyDetailsEnabled>
          <artifactId>google-cloud-bom</artifactId>
          <packaging>jar</packaging>
        </configuration>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>release</id>
      <activation>
        <property>
          <name>performRelease</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>3.2.7</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
                <configuration>
                  <gpgArguments>
                    <arg>--pinentry-mode</arg>
                    <arg>loopback</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>3.3.1</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
