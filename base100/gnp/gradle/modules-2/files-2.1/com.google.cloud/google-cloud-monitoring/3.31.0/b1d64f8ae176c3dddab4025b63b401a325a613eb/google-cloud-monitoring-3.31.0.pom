<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-monitoring</artifactId>
  <version>3.31.0</version>
  <name>Google Cloud Monitoring</name>
  <description>Java idiomatic client for Stackdriver Monitoring</description>
  <url>https://github.com/googleapis/google-cloud-java</url>
  <organization>
    <name>Google LLC</name>
  </organization>
  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>suztomo</id>
      <name><PERSON><PERSON></name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <scm child.scm.connection.inherit.append.path="false" child.scm.developerConnection.inherit.append.path="false" child.scm.url.inherit.append.path="false">
    <connection>scm:git:**************:googleapis/google-cloud-java.git</connection>
    <developerConnection>scm:git:**************:googleapis/google-cloud-java.git</developerConnection>
    <url>https://github.com/googleapis/google-cloud-java</url>
  </scm>
  <issueManagement>
    <system>GitHub Issues</system>
    <url>https://github.com/googleapis/google-cloud-java/issues</url>
  </issueManagement>
  <distributionManagement>
    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://google.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <url>https://google.oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-api</artifactId>
      <version>1.59.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.2</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.errorprone</groupId>
      <artifactId>error_prone_annotations</artifactId>
      <version>2.22.0</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-stub</artifactId>
      <version>1.59.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>grpc-api</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-protobuf</artifactId>
      <version>1.59.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>grpc-api</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protobuf-java</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
        <exclusion>
          <artifactId>proto-google-common-protos</artifactId>
          <groupId>com.google.api.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-protobuf-lite</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-protobuf-lite</artifactId>
      <version>1.59.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>grpc-api</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protobuf-javalite</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>api-common</artifactId>
      <version>2.20.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>auto-value-annotations</artifactId>
          <groupId>com.google.auto.value</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>javax.annotation-api</artifactId>
          <groupId>javax.annotation</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.auto.value</groupId>
      <artifactId>auto-value-annotations</artifactId>
      <version>1.10.4</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>javax.annotation</groupId>
      <artifactId>javax.annotation-api</artifactId>
      <version>1.3.2</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>3.24.4</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>proto-google-common-protos</artifactId>
      <version>2.28.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>protobuf-java</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>proto-google-cloud-monitoring-v3</artifactId>
      <version>3.31.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>protobuf-java</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
        <exclusion>
          <artifactId>proto-google-common-protos</artifactId>
          <groupId>com.google.api.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>api-common</artifactId>
          <groupId>com.google.api</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>32.1.2-jre</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>failureaccess</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>listenablefuture</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>checker-qual</artifactId>
          <groupId>org.checkerframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>j2objc-annotations</artifactId>
          <groupId>com.google.j2objc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>srczip</artifactId>
          <groupId>jdk</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>failureaccess</artifactId>
      <version>1.0.1</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>listenablefuture</artifactId>
      <version>9999.0-empty-to-avoid-conflict-with-guava</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>org.checkerframework</groupId>
      <artifactId>checker-qual</artifactId>
      <version>3.39.0</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.j2objc</groupId>
      <artifactId>j2objc-annotations</artifactId>
      <version>2.8</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>gax</artifactId>
      <version>2.37.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>api-common</artifactId>
          <groupId>com.google.api</groupId>
        </exclusion>
        <exclusion>
          <artifactId>google-auth-library-credentials</artifactId>
          <groupId>com.google.auth</groupId>
        </exclusion>
        <exclusion>
          <artifactId>proto-google-common-protos</artifactId>
          <groupId>com.google.api.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protobuf-java</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
        <exclusion>
          <artifactId>threetenbp</artifactId>
          <groupId>org.threeten</groupId>
        </exclusion>
        <exclusion>
          <artifactId>opencensus-api</artifactId>
          <groupId>io.opencensus</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>google-auth-library-oauth2-http</artifactId>
          <groupId>com.google.auth</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>graal-sdk</artifactId>
          <groupId>org.graalvm.sdk</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.auth</groupId>
      <artifactId>google-auth-library-credentials</artifactId>
      <version>1.20.0</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opencensus</groupId>
      <artifactId>opencensus-api</artifactId>
      <version>0.31.1</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>grpc-context</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-context</artifactId>
      <version>1.59.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>grpc-api</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>gax-grpc</artifactId>
      <version>2.37.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>gax</artifactId>
          <groupId>com.google.api</groupId>
        </exclusion>
        <exclusion>
          <artifactId>api-common</artifactId>
          <groupId>com.google.api</groupId>
        </exclusion>
        <exclusion>
          <artifactId>proto-google-common-protos</artifactId>
          <groupId>com.google.api.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>google-auth-library-credentials</artifactId>
          <groupId>com.google.auth</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-api</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-inprocess</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>threetenbp</artifactId>
          <groupId>org.threeten</groupId>
        </exclusion>
        <exclusion>
          <artifactId>google-auth-library-oauth2-http</artifactId>
          <groupId>com.google.auth</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-alts</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-auth</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-protobuf</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-stub</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-netty-shaded</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-googleapis</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>graal-sdk</artifactId>
          <groupId>org.graalvm.sdk</groupId>
        </exclusion>
        <exclusion>
          <artifactId>auto-value-annotations</artifactId>
          <groupId>com.google.auto.value</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-inprocess</artifactId>
      <version>1.59.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>grpc-core</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-core</artifactId>
      <version>1.59.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>grpc-api</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>gson</artifactId>
          <groupId>com.google.code.gson</groupId>
        </exclusion>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>com.google.android</groupId>
        </exclusion>
        <exclusion>
          <artifactId>animal-sniffer-annotations</artifactId>
          <groupId>org.codehaus.mojo</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>perfmark-api</artifactId>
          <groupId>io.perfmark</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-context</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-util</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.android</groupId>
      <artifactId>annotations</artifactId>
      <version>4.1.1.4</version>
      <scope>runtime</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>org.codehaus.mojo</groupId>
      <artifactId>animal-sniffer-annotations</artifactId>
      <version>1.23</version>
      <scope>runtime</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-util</artifactId>
      <version>1.59.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>grpc-core</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>animal-sniffer-annotations</artifactId>
          <groupId>org.codehaus.mojo</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-alts</artifactId>
      <version>1.59.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>grpc-auth</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-grpclb</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-protobuf</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-stub</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protobuf-java</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
        <exclusion>
          <artifactId>conscrypt-openjdk-uber</artifactId>
          <groupId>org.conscrypt</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>google-auth-library-oauth2-http</artifactId>
          <groupId>com.google.auth</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-netty-shaded</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-grpclb</artifactId>
      <version>1.59.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>grpc-core</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-protobuf</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-stub</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protobuf-java</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protobuf-java-util</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java-util</artifactId>
      <version>3.24.4</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>protobuf-java</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>gson</artifactId>
          <groupId>com.google.code.gson</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>j2objc-annotations</artifactId>
          <groupId>com.google.j2objc</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>org.conscrypt</groupId>
      <artifactId>conscrypt-openjdk-uber</artifactId>
      <version>2.5.2</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-auth</artifactId>
      <version>1.59.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>grpc-api</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>google-auth-library-credentials</artifactId>
          <groupId>com.google.auth</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-netty-shaded</artifactId>
      <version>1.59.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>perfmark-api</artifactId>
          <groupId>io.perfmark</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-core</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.perfmark</groupId>
      <artifactId>perfmark-api</artifactId>
      <version>0.26.0</version>
      <scope>runtime</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-googleapis</artifactId>
      <version>1.59.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>grpc-api</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-alts</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-core</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-xds</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-xds</artifactId>
      <version>1.59.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>grpc-protobuf</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-stub</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>opencensus-proto</artifactId>
          <groupId>io.opencensus</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-core</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-services</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-auth</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-alts</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>gson</artifactId>
          <groupId>com.google.code.gson</groupId>
        </exclusion>
        <exclusion>
          <artifactId>re2j</artifactId>
          <groupId>com.google.re2j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>auto-value-annotations</artifactId>
          <groupId>com.google.auto.value</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protobuf-java-util</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-netty-shaded</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opencensus</groupId>
      <artifactId>opencensus-proto</artifactId>
      <version>0.2.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>protobuf-java</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-protobuf</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-stub</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-services</artifactId>
      <version>1.59.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>grpc-protobuf</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-stub</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>grpc-util</artifactId>
          <groupId>io.grpc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>protobuf-java-util</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>j2objc-annotations</artifactId>
          <groupId>com.google.j2objc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>gson</artifactId>
          <groupId>com.google.code.gson</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.re2j</groupId>
      <artifactId>re2j</artifactId>
      <version>1.7</version>
      <scope>runtime</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>org.threeten</groupId>
      <artifactId>threetenbp</artifactId>
      <version>1.6.8</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.auth</groupId>
      <artifactId>google-auth-library-oauth2-http</artifactId>
      <version>1.20.0</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>auto-value-annotations</artifactId>
          <groupId>com.google.auto.value</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>google-auth-library-credentials</artifactId>
          <groupId>com.google.auth</groupId>
        </exclusion>
        <exclusion>
          <artifactId>google-http-client</artifactId>
          <groupId>com.google.http-client</groupId>
        </exclusion>
        <exclusion>
          <artifactId>google-http-client-gson</artifactId>
          <groupId>com.google.http-client</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client</artifactId>
      <version>1.43.3</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>httpclient</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
        <exclusion>
          <artifactId>httpcore</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>j2objc-annotations</artifactId>
          <groupId>com.google.j2objc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>opencensus-api</artifactId>
          <groupId>io.opencensus</groupId>
        </exclusion>
        <exclusion>
          <artifactId>opencensus-contrib-http-util</artifactId>
          <groupId>io.opencensus</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.14</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>httpcore</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-codec</artifactId>
          <groupId>commons-codec</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.2</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>log4j</artifactId>
          <groupId>log4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>logkit</artifactId>
          <groupId>logkit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>avalon-framework</artifactId>
          <groupId>avalon-framework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>servlet-api</artifactId>
          <groupId>javax.servlet</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.16.0</version>
      <scope>runtime</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpcore</artifactId>
      <version>4.4.16</version>
      <scope>runtime</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>io.opencensus</groupId>
      <artifactId>opencensus-contrib-http-util</artifactId>
      <version>0.31.1</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>opencensus-api</artifactId>
          <groupId>io.opencensus</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client-gson</artifactId>
      <version>1.43.3</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <artifactId>google-http-client</artifactId>
          <groupId>com.google.http-client</groupId>
        </exclusion>
        <exclusion>
          <artifactId>gson</artifactId>
          <groupId>com.google.code.gson</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.10.1</version>
      <scope>runtime</scope>
      <optional>false</optional>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <id>google-maven-central-copy</id>
      <name>Google Maven Central copy</name>
      <url>https://maven-central.storage-download.googleapis.com/maven2</url>
    </repository>
    <repository>
      <id>maven-central</id>
      <name>Maven Central</name>
      <url>https://repo1.maven.org/maven2</url>
    </repository>
  </repositories>
</project>
