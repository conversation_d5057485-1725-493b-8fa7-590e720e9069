<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-spanner-bom</artifactId>
  <version>6.71.0</version><!-- {x-version-update:google-cloud-spanner:current} -->
  <packaging>pom</packaging>
  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>sdk-platform-java-config</artifactId>
    <version>3.32.0</version>
  </parent>

  <name>Google Cloud Spanner BOM</name>
  <url>https://github.com/googleapis/java-spanner</url>
  <description>
    BOM for Google Cloud Spanner
  </description>

  <organization>
    <name>Google LLC</name>
  </organization>

  <developers>
    <developer>
      <id>chingor13</id>
      <name>Jeff Ching</name>
      <email><EMAIL></email>
      <organization>Google LLC</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>

  <scm>
    <connection>scm:git:https://github.com/googleapis/java-spanner.git</connection>
    <developerConnection>scm:git:**************:googleapis/java-spanner.git</developerConnection>
    <url>https://github.com/googleapis/java-spanner</url>
  </scm>


  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-spanner</artifactId>
        <version>6.71.0</version><!-- {x-version-update:google-cloud-spanner:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-spanner</artifactId>
        <type>test-jar</type>
        <version>6.71.0</version><!-- {x-version-update:google-cloud-spanner:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-spanner-v1</artifactId>
        <version>6.71.0</version><!-- {x-version-update:grpc-google-cloud-spanner-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-spanner-admin-instance-v1</artifactId>
        <version>6.71.0</version><!-- {x-version-update:grpc-google-cloud-spanner-admin-instance-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-spanner-admin-database-v1</artifactId>
        <version>6.71.0</version><!-- {x-version-update:grpc-google-cloud-spanner-admin-database-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-spanner-admin-instance-v1</artifactId>
        <version>6.71.0</version><!-- {x-version-update:proto-google-cloud-spanner-admin-instance-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-spanner-v1</artifactId>
        <version>6.71.0</version><!-- {x-version-update:proto-google-cloud-spanner-v1:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-spanner-admin-database-v1</artifactId>
        <version>6.71.0</version><!-- {x-version-update:proto-google-cloud-spanner-admin-database-v1:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <!-- TODO: Remove this once the shared configuration updates to use Java 8 -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.13.0</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>UTF-8</encoding>
          <compilerArgument>-Xlint:unchecked</compilerArgument>
          <compilerArgument>-Xlint:deprecation</compilerArgument>
          <showDeprecation>true</showDeprecation>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
