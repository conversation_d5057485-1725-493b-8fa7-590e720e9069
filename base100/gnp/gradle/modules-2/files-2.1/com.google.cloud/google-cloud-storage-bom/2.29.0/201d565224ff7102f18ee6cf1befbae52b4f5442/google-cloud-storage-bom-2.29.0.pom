<?xml version='1.0' encoding='UTF-8'?>
<!--
  ~ Copyright 2022 Google LLC
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud</groupId>
  <artifactId>google-cloud-storage-bom</artifactId>
  <version>2.29.0</version><!-- {x-version-update:google-cloud-storage:current} -->
  <packaging>pom</packaging>
  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-shared-config</artifactId>
    <version>1.6.0</version>
    <relativePath/>
  </parent>

  <name>Google Cloud Storage BOM</name>
  <url>https://github.com/googleapis/java-storage</url>
  <description>
    BOM for Google Cloud Storage
  </description>

  <organization>
    <name>Google LLC</name>
  </organization>

  <developers>
    <developer>
      <id>BenWhitehead</id>
      <name>Ben Whitehead</name>
      <email>benwhitehead [at] google.com</email>
      <organization>Google LLC</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>

  <scm>
    <connection>scm:git:https://github.com/googleapis/java-storage.git</connection>
    <developerConnection>scm:git:**************:googleapis/java-storage.git</developerConnection>
    <url>https://github.com/googleapis/java-storage</url>
  </scm>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>google-cloud-storage</artifactId>
        <version>2.29.0</version><!-- {x-version-update:google-cloud-storage:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>gapic-google-cloud-storage-v2</artifactId>
        <version>2.29.0-alpha</version><!-- {x-version-update:gapic-google-cloud-storage-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-cloud-storage-v2</artifactId>
        <version>2.29.0-alpha</version><!-- {x-version-update:grpc-google-cloud-storage-v2:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-cloud-storage-v2</artifactId>
        <version>2.29.0-alpha</version><!-- {x-version-update:proto-google-cloud-storage-v2:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
