<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>


  <groupId>ch.qos.logback</groupId>
  <artifactId>logback-parent</artifactId>
  <version>1.5.18</version>
  <packaging>pom</packaging>

  <name>Logback-Parent</name>
  <description>logback project pom.xml file</description>

  <url>http://logback.qos.ch</url>

  <organization>
    <name>QOS.ch</name>
    <url>http://www.qos.ch</url>
  </organization>
  <inceptionYear>2005</inceptionYear>

  <licenses>
    <license>
      <name>Eclipse Public License - v 1.0</name>
      <url>http://www.eclipse.org/legal/epl-v10.html</url>
    </license>

    <license>
      <name>GNU Lesser General Public License</name>
      <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
    </license>
  </licenses>

  <scm>
    <url>https://github.com/qos-ch/logback</url>
    <connection>scm:**************:qos-ch/logback.git</connection>
  </scm>

  <modules>
    <module>logback-core</module>
    <module>logback-core-blackbox</module>
    <module>logback-classic</module>
    <module>logback-classic-blackbox</module>
    <module>logback-access</module>
    <module>logback-examples</module>
  </modules>

  <properties>
    <!-- yyyy-MM-dd'T'HH:mm:ss'Z' -->
    <project.build.outputTimestamp>2025-03-18T12:42:00Z</project.build.outputTimestamp>

    <!-- minimal JDK version at runtime -->
    <jdk.version>11</jdk.version>
    <!-- See https://maven.apache.org/plugins/maven-compiler-plugin/examples/set-compiler-release.html -->
    <maven.compiler.release>${jdk.version}</maven.compiler.release>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    <junit-jupiter-api.version>5.9.1</junit-jupiter-api.version>
    <junit-vintage-engine.version>5.9.1</junit-vintage-engine.version>
    <junit-jupiter-params.version>5.9.1</junit-jupiter-params.version>
    <junit-platform.version>1.10.5</junit-platform.version>
    <assertj-core.version>3.23.1</assertj-core.version>
    <hamcrest.version>2.2</hamcrest.version>
    <jakarta.mail.version>2.1.0</jakarta.mail.version>
    <jakarta.activation.version>2.1.0</jakarta.activation.version>
    <jakarta.angus-mail.version>1.0.0</jakarta.angus-mail.version>

    <jakarta.servlet.version>5.0.0</jakarta.servlet.version>
    <greenmail.version>2.0.0-alpha-1</greenmail.version>

    <janino.version>3.1.8</janino.version>
    <!-- slf4j.version property is used below, in
         logback-classic/pom.xml, /logback-examples/src/main/resources/setClasspath.cmd, download.html
    -->
    <slf4j.version>2.0.17</slf4j.version>
    <cal10n.version>0.8.1</cal10n.version>
    <consolePlugin.version>1.1.0</consolePlugin.version>
    <jackson.version>2.15.0</jackson.version>
    <xz.version>1.10</xz.version>

    <!--<jansi.version>1.18</jansi.version>-->
    <jansi.version>2.4.0</jansi.version>

    <mockito-core.version>4.8.0</mockito-core.version>
    <byte-buddy.version>1.12.14</byte-buddy.version>

    <maven-compiler-plugin.version>3.10.1</maven-compiler-plugin.version>
    <maven-surefire-plugin.version>3.0.0-M7</maven-surefire-plugin.version>
    <maven-site-plugin.version>3.7.1</maven-site-plugin.version>
    <maven-install-plugin.version>3.0.0-M1</maven-install-plugin.version>
    <maven-javadoc-plugin.version>3.3.0</maven-javadoc-plugin.version>
    <maven-source-plugin.version>3.2.0</maven-source-plugin.version>
    <maven-resources-plugin.version>3.1.0</maven-resources-plugin.version>
    <license-maven-plugin.version>3.0</license-maven-plugin.version>
    <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
    <maven-jxr-plugin.version>3.6.0</maven-jxr-plugin.version>
    <maven-release-plugin.version>3.0.0-M4</maven-release-plugin.version>
    <maven-deploy-plugin.version>3.0.0-M1</maven-deploy-plugin.version>
    <maven-dependency-plugin.version>3.2.0</maven-dependency-plugin.version>
    <maven-bundle-plugin.version>5.1.9</maven-bundle-plugin.version>
    <maven-antrun-plugin.version>3.1.0</maven-antrun-plugin.version>
    <ant.version>1.10.13</ant.version>
    <cobertura.maven.plugin.version>2.7</cobertura.maven.plugin.version>
    <jetty.version>12.0.13</jetty.version>
  </properties>

  <developers>
    <developer>
      <id>ceki</id>
      <name>Ceki Gulcu</name>
      <email><EMAIL></email>
    </developer>

    <developer>
      <id>hixi</id>
      <name>Joern Huxhorn</name>
      <email><EMAIL></email>
    </developer>
  </developers>

  <dependencies>

    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>${assertj-core.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <version>${junit-jupiter-api.version}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>${junit-jupiter-api.version}</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-library</artifactId>
      <version>${hamcrest.version}</version>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <dependencyManagement>

    <dependencies>

      <!-- start of dependencies duplicated from logback-bom/pom.xml -->
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-access</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
      </dependency>
      <!-- end of dependencies duplicated from logback-bom/pom.xml -->

      <dependency>
        <groupId>org.tukaani</groupId>
        <artifactId>xz</artifactId>
        <version>${xz.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>janino</artifactId>
        <version>${janino.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>commons-compiler</artifactId>
        <version>${janino.version}</version>
      </dependency>

      <dependency>
        <groupId>org.fusesource.jansi</groupId>
        <artifactId>jansi</artifactId>
        <version>${jansi.version}</version>
      </dependency>

      <dependency>
        <groupId>jakarta.mail</groupId>
        <artifactId>jakarta.mail-api</artifactId>
        <version>${jakarta.mail.version}</version>
      </dependency>

      <dependency>
        <groupId>jakarta.activation</groupId>
        <artifactId>jakarta.activation-api</artifactId>
        <version>${jakarta.activation.version}</version>
      </dependency>

      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>angus-mail</artifactId>
        <version>${jakarta.angus-mail.version}</version>
      </dependency>

      <dependency>
        <groupId>jakarta.servlet</groupId>
        <artifactId>jakarta.servlet-api</artifactId>
        <version>${jakarta.servlet.version}</version>
      </dependency>

      <dependency>
        <groupId>com.icegreen</groupId>
        <artifactId>greenmail</artifactId>
        <version>${greenmail.version}</version>
      </dependency>

      <dependency>
        <groupId>org.dom4j</groupId>
        <artifactId>dom4j</artifactId>
        <version>2.0.3</version>
      </dependency>

      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>${mockito-core.version}</version>
      </dependency>

      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy</artifactId>
        <version>${byte-buddy.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>


  <build>
    <extensions>
      <extension>
        <groupId>org.apache.maven.wagon</groupId>
        <artifactId>wagon-ssh</artifactId>
        <version>2.10</version>
      </extension>
    </extensions>

    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>3.0.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven-compiler-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>${maven-deploy-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>${maven-install-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>${maven-resources-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>${maven-site-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${maven-jar-plugin.version}</version>
          <configuration>
            <outputTimestamp>${project.build.outputTimestamp}</outputTimestamp>
            <archive>
              <index>true</index>
              <manifest>
                <!-- Add version information into the MANIFEST so it is accessible by
                     calling Package#getImplementationVersion()
                 -->
                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
              </manifest>
            </archive>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${maven-javadoc-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>${maven-dependency-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>${maven-release-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>${maven-source-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>findbugs-maven-plugin</artifactId>
          <version>3.0.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>${maven-bundle-plugin.version}</version>
        </plugin>
    </plugins>

    </pluginManagement>

    <plugins>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>

        <configuration>
          <release>${jdk.version}</release>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <executions>
          <execution>
            <id>default-cli</id>
            <goals>
              <goal>aggregate</goal>
            </goals>
          </execution>
        </executions>

        <configuration>
          <linksource>true</linksource>
          <doclint>none</doclint>
          <skippedModules>
            logback-core-blackbox,logback-classic-blackbox,logback-examples
          </skippedModules>
          <additionalOptions>
            <additionalOption>-Xdoclint:none</additionalOption>
          </additionalOptions>

          <links>
            <link>
              http://docs.oracle.com/javase/7/docs/api/
            </link>
          </links>

          <groups>
            <group>
              <title>Logback Core</title>
              <packages>ch.qos.logback.core:ch.qos.logback.core.*
              </packages>
            </group>
            <group>
              <title>Logback Classic</title>
              <packages>
                ch.qos.logback:ch.qos.logback.classic:ch.qos.logback.classic.*
              </packages>
            </group>
          </groups>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>${maven-jxr-plugin.version}</version>
        <configuration>
          <aggregate>true</aggregate>
          <!--<javadocDir>target/site/apidocs/</javadocDir>-->
          <linkJavadoc>false</linkJavadoc>
        </configuration>
      </plugin>


    </plugins>
  </build>

  <distributionManagement>

    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>

  </distributionManagement>

  <reporting>
    <plugins>
      <!--
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>2.5</version>
        <configuration>
          <aggregate>true</aggregate>
          <javadocDir>target/site/apidocs/</javadocDir>
          <linkJavadoc>true</linkJavadoc>
        </configuration>
      </plugin>
      -->





    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>testSkip</id>
      <properties>
        <maven.test.skip>true</maven.test.skip>
      </properties>
    </profile>
    <profile>
      <id>license</id>
      <build>
        <plugins>
          <plugin>
            <groupId>com.mycila</groupId>
            <artifactId>license-maven-plugin</artifactId>
            <version>${license-maven-plugin.version}</version>
            <configuration>
              <header>src/main/licenseHeader.txt</header>
              <quiet>false</quiet>
              <failIfMissing>true</failIfMissing>
              <aggregate>true</aggregate>
              <includes>
                <include>src/**/*.java</include>
                <include>src/**/*.groovy</include>
              </includes>
              <useDefaultExcludes>true</useDefaultExcludes>
              <useDefaultMapping>true</useDefaultMapping>
              <properties>
                <year>1999</year>
              </properties>
              <headerDefinitions>
                <headerDefinition>src/main/javadocHeaders.xml</headerDefinition>
              </headerDefinitions>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>javadocjar</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>${maven-javadoc-plugin.version}</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <doclint>none</doclint>
              <sourceFileExcludes>
                <sourceFileExclude>**/module-info.java</sourceFileExclude>
              </sourceFileExcludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>sign-artifacts</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>1.6</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

  </profiles>

</project>
