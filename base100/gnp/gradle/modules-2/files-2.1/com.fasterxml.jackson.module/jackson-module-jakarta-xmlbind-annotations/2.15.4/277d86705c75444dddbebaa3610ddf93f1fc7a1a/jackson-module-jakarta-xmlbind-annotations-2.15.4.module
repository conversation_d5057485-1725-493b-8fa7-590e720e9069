{"formatVersion": "1.1", "component": {"group": "com.fasterxml.jackson.module", "module": "jackson-module-jakarta-xmlbind-annotations", "version": "2.15.4", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"maven": {"version": "3.9.3"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.fasterxml.jackson.core", "module": "jackson-annotations", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-core", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.15.4"}}, {"group": "jakarta.xml.bind", "module": "jakarta.xml.bind-api", "version": {"requires": "3.0.1"}}, {"group": "jakarta.activation", "module": "jakarta.activation-api", "version": {"requires": "2.1.0"}}, {"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.15.4"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-module-jakarta-xmlbind-annotations-2.15.4.jar", "url": "jackson-module-jakarta-xmlbind-annotations-2.15.4.jar", "size": 30905, "sha512": "6e7fefa2a22a9cb37145501e8c259bb97f68ed85ba89e17438e757e96fa5d95abb90569262306e7aa716a6f804a7252c006ffa95a8b8440aab8280b47cfee167", "sha256": "39fdedb42048a420632a063befedf1b88446a9253d7b2be4b1e297b3649e8b6b", "sha1": "dfdfb8b38e680d4884027e871529fa3925a54190", "md5": "797537fd0eede15e7fc753838ff81fc7"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.fasterxml.jackson.core", "module": "jackson-annotations", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-core", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.15.4"}}, {"group": "jakarta.xml.bind", "module": "jakarta.xml.bind-api", "version": {"requires": "3.0.1"}}, {"group": "jakarta.activation", "module": "jakarta.activation-api", "version": {"requires": "2.1.0"}}, {"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.15.4"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-module-jakarta-xmlbind-annotations-2.15.4.jar", "url": "jackson-module-jakarta-xmlbind-annotations-2.15.4.jar", "size": 30905, "sha512": "6e7fefa2a22a9cb37145501e8c259bb97f68ed85ba89e17438e757e96fa5d95abb90569262306e7aa716a6f804a7252c006ffa95a8b8440aab8280b47cfee167", "sha256": "39fdedb42048a420632a063befedf1b88446a9253d7b2be4b1e297b3649e8b6b", "sha1": "dfdfb8b38e680d4884027e871529fa3925a54190", "md5": "797537fd0eede15e7fc753838ff81fc7"}]}]}