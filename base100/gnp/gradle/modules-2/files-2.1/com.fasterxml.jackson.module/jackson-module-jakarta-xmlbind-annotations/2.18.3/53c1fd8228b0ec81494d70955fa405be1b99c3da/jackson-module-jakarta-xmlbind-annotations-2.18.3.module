{"formatVersion": "1.1", "component": {"group": "com.fasterxml.jackson.module", "module": "jackson-module-jakarta-xmlbind-annotations", "version": "2.18.3", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"maven": {"version": "3.9.3"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.fasterxml.jackson.core", "module": "jackson-annotations", "version": {"requires": "2.18.3"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-core", "version": {"requires": "2.18.3"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.18.3"}}, {"group": "jakarta.xml.bind", "module": "jakarta.xml.bind-api", "version": {"requires": "3.0.1"}}, {"group": "jakarta.activation", "module": "jakarta.activation-api", "version": {"requires": "2.1.0"}}, {"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.18.3"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-module-jakarta-xmlbind-annotations-2.18.3.jar", "url": "jackson-module-jakarta-xmlbind-annotations-2.18.3.jar", "size": 31442, "sha512": "3a56928a80143e051ef86d6699794ad5c8f6d59ace343d442f79ec6405e2a6734b002a6a3a1e16f80225e96aa48bdff62aa12a4ef24579714a3e6389255179f7", "sha256": "5e722deb18d272d5d1a13bb1a6fb74106c08a1e16e7759dbfd988be1b957b7b5", "sha1": "e5a1543ce9dd0cda1849084ea85f831c636c8ab1", "md5": "2cc36e6c0ae343b872a2fae12d4487d3"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.fasterxml.jackson.core", "module": "jackson-annotations", "version": {"requires": "2.18.3"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-core", "version": {"requires": "2.18.3"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.18.3"}}, {"group": "jakarta.xml.bind", "module": "jakarta.xml.bind-api", "version": {"requires": "3.0.1"}}, {"group": "jakarta.activation", "module": "jakarta.activation-api", "version": {"requires": "2.1.0"}}, {"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.18.3"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-module-jakarta-xmlbind-annotations-2.18.3.jar", "url": "jackson-module-jakarta-xmlbind-annotations-2.18.3.jar", "size": 31442, "sha512": "3a56928a80143e051ef86d6699794ad5c8f6d59ace343d442f79ec6405e2a6734b002a6a3a1e16f80225e96aa48bdff62aa12a4ef24579714a3e6389255179f7", "sha256": "5e722deb18d272d5d1a13bb1a6fb74106c08a1e16e7759dbfd988be1b957b7b5", "sha1": "e5a1543ce9dd0cda1849084ea85f831c636c8ab1", "md5": "2cc36e6c0ae343b872a2fae12d4487d3"}]}]}