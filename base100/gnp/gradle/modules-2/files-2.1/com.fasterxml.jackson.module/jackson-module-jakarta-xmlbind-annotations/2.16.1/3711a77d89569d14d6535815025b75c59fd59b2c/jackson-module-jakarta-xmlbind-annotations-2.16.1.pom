<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson.module</groupId>
    <artifactId>jackson-modules-base</artifactId>
    <version>2.16.1</version>
  </parent>
  <artifactId>jackson-module-jakarta-xmlbind-annotations</artifactId>
  <name>Jackson module: Jakarta XML Bind Annotations (jakarta.xml.bind)</name>
  <packaging>bundle</packaging>

  <description>Support for using Jakarta XML Bind (aka JAXB 3.0) annotations as an alternative
  to "native" Jackson annotations, for configuring data-binding.
</description>
  <url>https://github.com/FasterXML/jackson-modules-base</url>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <properties>
   <!-- Generate PackageVersion.java into this directory. -->
    <packageVersion.dir>com/fasterxml/jackson/module/jakarta/xmlbind</packageVersion.dir>
    <packageVersion.package>${project.groupId}.jakarta.xmlbind</packageVersion.package>
    <!-- 22-Mar-2019, tatu: [modules-base#80]: Presence of JAF on the bundle classpath is only necessary
           when ser/deser'ing javax.activation.DataHandlers
      -->
    <!-- 02-Oct-2023, tatu: [modules-base#219]: Need to relax constraints for bind-annotations
      -->
    <!-- 20-Dec-2023, tatu: [modules-base#231]: And make sure to use "right" actiovation dep
      -->
    <osgi.import>jakarta.activation;resolution:=optional,
      jakarta.xml.bind;version="[3.0,4.0.100)",
      jakarta.xml.bind.*;version="[3.0,4.0.100)",
      *
    </osgi.import>

    <version.xmlbind.api>3.0.1</version.xmlbind.api>
  </properties>
  <dependencies>
    <!-- Extends Jackson core and mapper; minor dep on annotations too (JsonInclude) -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>

    <!--  and actual Jakarta Xml Bind annotations, types -->
    <dependency>
      <groupId>jakarta.xml.bind</groupId>
      <artifactId>jakarta.xml.bind-api</artifactId>
      <version>${version.xmlbind.api}</version>
    </dependency>

    <dependency>
      <groupId>jakarta.activation</groupId>
      <artifactId>jakarta.activation-api</artifactId>
      <version>2.1.0</version>
    </dependency>

    <!-- may also need JAXB impl for tests -->
    <dependency>
      <groupId>org.glassfish.jaxb</groupId>
      <artifactId>jaxb-runtime</artifactId>
      <version>${version.xmlbind.api}</version>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
      </plugin>
      <plugin>
        <groupId>org.moditect</groupId>
        <artifactId>moditect-maven-plugin</artifactId>
      </plugin>

    </plugins>
  </build>
</project>
