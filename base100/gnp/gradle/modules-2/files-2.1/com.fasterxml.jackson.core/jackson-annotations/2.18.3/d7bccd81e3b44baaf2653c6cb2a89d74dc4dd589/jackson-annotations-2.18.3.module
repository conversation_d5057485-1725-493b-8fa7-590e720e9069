{"formatVersion": "1.1", "component": {"group": "com.fasterxml.jackson.core", "module": "jackson-annotations", "version": "2.18.3", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"maven": {"version": "3.9.3"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.18.3"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-annotations-2.18.3.jar", "url": "jackson-annotations-2.18.3.jar", "size": 78502, "sha512": "a872242613d5be7537db8ad5d0408aa8928d376e1670bebd473b75e0953370abe77b725e11600e67b8bfb7650739e8c0d69a142dbe843719c5178b00b5deef96", "sha256": "8aa5740d80b5a5025508b41bbadbaa1fb3772267c628b2e30681a4f45f8b8931", "sha1": "7fa21cf7da4598f8240e4ebd9779249622af1acd", "md5": "cae46e2c56e1b40b67dcfcfc9b6e275a"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.18.3"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-annotations-2.18.3.jar", "url": "jackson-annotations-2.18.3.jar", "size": 78502, "sha512": "a872242613d5be7537db8ad5d0408aa8928d376e1670bebd473b75e0953370abe77b725e11600e67b8bfb7650739e8c0d69a142dbe843719c5178b00b5deef96", "sha256": "8aa5740d80b5a5025508b41bbadbaa1fb3772267c628b2e30681a4f45f8b8931", "sha1": "7fa21cf7da4598f8240e4ebd9779249622af1acd", "md5": "cae46e2c56e1b40b67dcfcfc9b6e275a"}]}]}