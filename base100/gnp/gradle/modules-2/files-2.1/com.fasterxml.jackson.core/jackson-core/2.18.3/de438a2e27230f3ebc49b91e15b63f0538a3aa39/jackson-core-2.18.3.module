{"formatVersion": "1.1", "component": {"group": "com.fasterxml.jackson.core", "module": "jackson-core", "version": "2.18.3", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"maven": {"version": "3.9.9"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.18.3"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-core-2.18.3.jar", "url": "jackson-core-2.18.3.jar", "size": 598026, "sha512": "f741ece5a41f1d27c04bdbe839092fec0fac1a7b139d9f5fc92141b4a396f622b7f668f4fa97e42da0d6d4237334e117841753e209adb49d1ac0a1a227cfacc1", "sha256": "56bc4d3e5e53ce821450fa97b3f9e0f8dde125cf6da6884353bb1f09582e1d9", "sha1": "78f80c259268200e588aa204dd97ecf09b76916e", "md5": "b36e17ef5ba214242b700f8e621e6f12"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.18.3"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-core-2.18.3.jar", "url": "jackson-core-2.18.3.jar", "size": 598026, "sha512": "f741ece5a41f1d27c04bdbe839092fec0fac1a7b139d9f5fc92141b4a396f622b7f668f4fa97e42da0d6d4237334e117841753e209adb49d1ac0a1a227cfacc1", "sha256": "56bc4d3e5e53ce821450fa97b3f9e0f8dde125cf6da6884353bb1f09582e1d9", "sha1": "78f80c259268200e588aa204dd97ecf09b76916e", "md5": "b36e17ef5ba214242b700f8e621e6f12"}]}]}