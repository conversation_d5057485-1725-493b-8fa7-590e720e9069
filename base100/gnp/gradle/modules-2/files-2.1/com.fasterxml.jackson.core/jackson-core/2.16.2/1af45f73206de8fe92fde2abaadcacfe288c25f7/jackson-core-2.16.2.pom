<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>jackson-base</artifactId>
    <groupId>com.fasterxml.jackson</groupId>
    <version>2.16.2</version>
    <relativePath>../pom.xml/pom.xml</relativePath>
  </parent>
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.fasterxml.jackson.core</groupId>
  <artifactId>jackson-core</artifactId>
  <name>Jackson-core</name>
  <version>2.16.2</version>
  <description>Core Jackson processing abstractions (aka Streaming API), implementation for JSON</description>
  <url>https://github.com/FasterXML/jackson-core</url>
  <inceptionYear>2008</inceptionYear>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <connection>scm:git:**************:FasterXML/jackson-core.git</connection>
    <developerConnection>scm:git:**************:FasterXML/jackson-core.git</developerConnection>
    <tag>jackson-core-2.16.2</tag>
    <url>https://github.com/FasterXML/jackson-core</url>
  </scm>
  <build>
    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-properties</id>
            <phase>validate</phase>
            <goals>
              <goal>enforce</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-site-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${version.plugin.surefire}</version>
        <configuration>
          <redirectTestOutputToFile>${surefire.redirectTestOutputToFile}</redirectTestOutputToFile>
          <excludes>
            <exclude>**/failing/**/*.java</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
      </plugin>
      <plugin>
        <groupId>org.moditect</groupId>
        <artifactId>moditect-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <id>shade-jackson-core</id>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <filters>
                <filter>
                  <artifact>ch.randelshofer:fastdoubleparser</artifact>
                  <excludes>
                    <exclude>META-INF/versions/**/module-info.*</exclude>
                    <exclude>META-INF/versions/22/**/*.*</exclude>
                  </excludes>
                </filter>
              </filters>
              <relocations>
                <relocation>
                  <pattern>ch/randelshofer/fastdoubleparser</pattern>
                  <shadedPattern>com/fasterxml/jackson/core/io/doubleparser</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>META-INF/LICENSE</pattern>
                  <shadedPattern>META-INF/FastDoubleParser-LICENSE</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>META-INF/NOTICE</pattern>
                  <shadedPattern>META-INF/FastDoubleParser-NOTICE</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>META-INF/jackson-core-LICENSE</pattern>
                  <shadedPattern>META-INF/LICENSE</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>META-INF/jackson-core-NOTICE</pattern>
                  <shadedPattern>META-INF/NOTICE</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>META-INF/versions/11/ch/randelshofer/fastdoubleparser</pattern>
                  <shadedPattern>META-INF/versions/11/com/fasterxml/jackson/core/io/doubleparser</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>META-INF/versions/17/ch/randelshofer/fastdoubleparser</pattern>
                  <shadedPattern>META-INF/versions/17/com/fasterxml/jackson/core/io/doubleparser</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>META-INF/versions/21/ch/randelshofer/fastdoubleparser</pattern>
                  <shadedPattern>META-INF/versions/21/com/fasterxml/jackson/core/io/doubleparser</shadedPattern>
                </relocation>
              </relocations>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <promoteTransitiveDependencies>true</promoteTransitiveDependencies>
          <createDependencyReducedPom>true</createDependencyReducedPom>
          <minimizeJar>true</minimizeJar>
        </configuration>
      </plugin>
      <plugin>
        <groupId>de.jjohannes</groupId>
        <artifactId>gradle-module-metadata-maven-plugin</artifactId>
        <configuration>
          <removedDependencies>
            <dependency>
              <groupId>ch.randelshofer</groupId>
              <artifactId>fastdoubleparser</artifactId>
            </dependency>
          </removedDependencies>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestEntries>
              <Multi-Release>true</Multi-Release>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>io.github.floverfelt</groupId>
        <artifactId>find-and-replace-maven-plugin</artifactId>
        <version>1.1.0</version>
        <executions>
          <execution>
            <id>exec</id>
            <phase>package</phase>
            <goals>
              <goal>find-and-replace</goal>
            </goals>
            <configuration>
              <replacementType>file-contents</replacementType>
              <baseDir>${basedir}</baseDir>
              <findRegex>&lt;modelVersion&gt;4.0.0&lt;/modelVersion&gt;</findRegex>
              <fileMask>dependency-reduced-pom.xml</fileMask>
              <replaceValue>&lt;!-- This module was also published with a richer model, Gradle metadata,  --&gt;
  &lt;!-- which should be used instead. Do not delete the following line which  --&gt;
  &lt;!-- is to indicate to Gradle or any Gradle module metadata file consumer  --&gt;
  &lt;!-- that they should prefer consuming it instead. --&gt;
  &lt;!-- do_not_remove: published-with-gradle-metadata --&gt;
  &lt;modelVersion&gt;4.0.0&lt;/modelVersion&gt;</replaceValue>
              <recursive>false</recursive>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>${version.plugin.animal-sniffer}</version>
        <configuration>
          <signature>
            <groupId>com.toasttab.android</groupId>
            <artifactId>gummy-bears-api-${version.android.sdk}</artifactId>
            <version>${version.android.sdk.signature}</version>
          </signature>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots />
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </repository>
  </repositories>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>5.9.3</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <properties>
    <version.android.sdk.signature>0.5.1</version.android.sdk.signature>
    <packageVersion.dir>com/fasterxml/jackson/core/json</packageVersion.dir>
    <osgi.import>!ch.randelshofer.fastdoubleparser, *</osgi.import>
    <osgi.export>com.fasterxml.jackson.core;version=${project.version},
com.fasterxml.jackson.core.*;version=${project.version}</osgi.export>
    <version.android.sdk>26</version.android.sdk>
    <project.build.outputTimestamp>2024-03-09T18:40:39Z</project.build.outputTimestamp>
    <packageVersion.package>${project.groupId}.json</packageVersion.package>
    <version.plugin.animal-sniffer>1.23</version.plugin.animal-sniffer>
  </properties>
</project>
