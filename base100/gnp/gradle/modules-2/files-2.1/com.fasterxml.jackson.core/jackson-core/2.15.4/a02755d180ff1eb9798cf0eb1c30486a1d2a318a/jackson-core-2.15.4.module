{"formatVersion": "1.1", "component": {"group": "com.fasterxml.jackson.core", "module": "jackson-core", "version": "2.15.4", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"maven": {"version": "3.9.3"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.15.4"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-core-2.15.4.jar", "url": "jackson-core-2.15.4.jar", "size": 548185, "sha512": "c71a7bd565a5974c1dffc4ba84e1b89b23c7b1df726b35dd5a0929a6acf2bac80ad41872e8474bacdd37044210e3e145a335f6ab6aba4f323a8750b1e997b212", "sha256": "8dc9210dd285db366f45f518dd1e6a9ccfeb0f1a8e184a899fe96d29edf1fd94", "sha1": "aebe84b45360debad94f692a4074c6aceb535fa0", "md5": "81e7d30d8ae478d83b9a8c152a0c148"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.15.4"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-core-2.15.4.jar", "url": "jackson-core-2.15.4.jar", "size": 548185, "sha512": "c71a7bd565a5974c1dffc4ba84e1b89b23c7b1df726b35dd5a0929a6acf2bac80ad41872e8474bacdd37044210e3e145a335f6ab6aba4f323a8750b1e997b212", "sha256": "8dc9210dd285db366f45f518dd1e6a9ccfeb0f1a8e184a899fe96d29edf1fd94", "sha1": "aebe84b45360debad94f692a4074c6aceb535fa0", "md5": "81e7d30d8ae478d83b9a8c152a0c148"}]}]}