{"formatVersion": "1.1", "component": {"group": "io.projectreactor", "module": "reactor-bom", "version": "2023.0.4", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "platform", "org.gradle.usage": "java-api"}, "dependencyConstraints": [{"group": "org.reactivestreams", "module": "reactive-streams", "version": {"requires": "1.0.4"}}, {"group": "io.projectreactor", "module": "reactor-core", "version": {"requires": "3.6.4"}}, {"group": "io.projectreactor", "module": "reactor-test", "version": {"requires": "3.6.4"}}, {"group": "io.projectreactor", "module": "reactor-tools", "version": {"requires": "3.6.4"}}, {"group": "io.projectreactor", "module": "reactor-core-micrometer", "version": {"requires": "1.1.4"}}, {"group": "io.projectreactor.addons", "module": "reactor-extra", "version": {"requires": "3.5.1"}}, {"group": "io.projectreactor.addons", "module": "reactor-adapter", "version": {"requires": "3.5.1"}}, {"group": "io.projectreactor.netty", "module": "reactor-netty", "version": {"requires": "1.1.17"}}, {"group": "io.projectreactor.netty", "module": "reactor-netty-core", "version": {"requires": "1.1.17"}}, {"group": "io.projectreactor.netty", "module": "reactor-netty-http", "version": {"requires": "1.1.17"}}, {"group": "io.projectreactor.netty", "module": "reactor-netty-http-brave", "version": {"requires": "1.1.17"}}, {"group": "io.projectreactor.addons", "module": "reactor-pool", "version": {"requires": "1.0.5"}}, {"group": "io.projectreactor.addons", "module": "reactor-pool-micrometer", "version": {"requires": "0.1.5"}}, {"group": "io.projectreactor.kotlin", "module": "reactor-kotlin-extensions", "version": {"requires": "1.2.2"}}, {"group": "io.projectreactor.kafka", "module": "reactor-kafka", "version": {"requires": "1.3.23"}}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "platform", "org.gradle.usage": "java-runtime"}, "dependencyConstraints": [{"group": "org.reactivestreams", "module": "reactive-streams", "version": {"requires": "1.0.4"}}, {"group": "io.projectreactor", "module": "reactor-core", "version": {"requires": "3.6.4"}}, {"group": "io.projectreactor", "module": "reactor-test", "version": {"requires": "3.6.4"}}, {"group": "io.projectreactor", "module": "reactor-tools", "version": {"requires": "3.6.4"}}, {"group": "io.projectreactor", "module": "reactor-core-micrometer", "version": {"requires": "1.1.4"}}, {"group": "io.projectreactor.addons", "module": "reactor-extra", "version": {"requires": "3.5.1"}}, {"group": "io.projectreactor.addons", "module": "reactor-adapter", "version": {"requires": "3.5.1"}}, {"group": "io.projectreactor.netty", "module": "reactor-netty", "version": {"requires": "1.1.17"}}, {"group": "io.projectreactor.netty", "module": "reactor-netty-core", "version": {"requires": "1.1.17"}}, {"group": "io.projectreactor.netty", "module": "reactor-netty-http", "version": {"requires": "1.1.17"}}, {"group": "io.projectreactor.netty", "module": "reactor-netty-http-brave", "version": {"requires": "1.1.17"}}, {"group": "io.projectreactor.addons", "module": "reactor-pool", "version": {"requires": "1.0.5"}}, {"group": "io.projectreactor.addons", "module": "reactor-pool-micrometer", "version": {"requires": "0.1.5"}}, {"group": "io.projectreactor.kotlin", "module": "reactor-kotlin-extensions", "version": {"requires": "1.2.2"}}, {"group": "io.projectreactor.kafka", "module": "reactor-kafka", "version": {"requires": "1.3.23"}}]}]}