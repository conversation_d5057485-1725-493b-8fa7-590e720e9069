<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.google.android</groupId>
	<artifactId>annotations</artifactId>
	<version>*******</version>
	<packaging>jar</packaging>

	<parent>
		<groupId>org.sonatype.oss</groupId>
		<artifactId>oss-parent</artifactId>
		<version>7</version>
	</parent>

	<name>Google Android Annotations Library</name>
	<description>A library jar that provides annotations for the Google Android Platform.</description>
	<url>http://source.android.com/</url>
	<inceptionYear>2008</inceptionYear>
	<licenses>
		<license>
			<name>Apache 2.0</name>
			<url>http://www.apache.org/licenses/LICENSE-2.0</url>
			<comments>
				While the EULA for the Android SDK restricts distribution of those binaries, the source code 
				is licensed under Apache 2.0 which allows compiling binaries from source and then distributing
				those versions. 
			</comments>
			<distribution>repo</distribution>
		</license>
	</licenses>
	<scm>
		<url>https://android.git.kernel.org/</url>
		<connection>git://android.git.kernel.org/platform/manifest.git</connection>
	</scm>
	<developers>
		<developer>
			<name>The Android Open Source Projects</name>
		</developer>
	</developers>

	<properties> 
		<release.name>jellybean-4.1</release.name>
		<platform>android-16</platform>
		<branch.tag>android-4.1.1_r4</branch.tag>
	</properties> 

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.3.2</version>
				<configuration>
					<source>1.5</source>
					<target>1.5</target>
				</configuration>
			</plugin>
		</plugins>

	</build>

</project>
