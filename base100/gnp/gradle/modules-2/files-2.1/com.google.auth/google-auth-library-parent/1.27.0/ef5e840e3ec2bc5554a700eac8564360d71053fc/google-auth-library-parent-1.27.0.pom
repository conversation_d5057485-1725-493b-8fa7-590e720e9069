<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.auth</groupId>
  <artifactId>google-auth-library-parent</artifactId>
  <version>1.27.0</version><!-- {x-version-update:google-auth-library-parent:current} -->
  <packaging>pom</packaging>
  <name>Google Auth Library for Java</name>
  <description>Client libraries providing authentication and
    authorization to enable calling Google APIs.
  </description>
  <url>https://github.com/googleapis/google-auth-library-java</url>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-shared-config</artifactId>
    <version>1.11.2</version>
  </parent>

  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <url>https://google.oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <site>
      <id>googleapis.dev</id>
      <name>Google self-hosted documentation site</name>
      <url>https://googleapis.dev/java/google-auth-library/${project.version}</url>
    </site>
  </distributionManagement>

  <licenses>
    <license>
      <name>BSD New license</name>
      <url>http://opensource.org/licenses/BSD-3-Clause</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <organization>
    <name>Google</name>
    <url>http://www.google.com/</url>
  </organization>


  <developers>
    <developer>
      <name>Jeff Ching</name>
      <email><EMAIL></email>
      <organization>Google</organization>
    </developer>
  </developers>

  <modules>
    <module>credentials</module>
    <module>oauth2_http</module>
    <module>appengine</module>
    <module>bom</module>
  </modules>

  <scm>
    <connection>scm:git:https://github.com/googleapis/google-auth-library-java.git</connection>
    <developerConnection>scm:git:https://github.com/googleapis/google-auth-library-java.git
    </developerConnection>
    <url>https://github.com/googleapis/google-auth-library-java</url>
  </scm>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.google.http.version>1.45.0</project.google.http.version>
    <project.junit.version>4.13.2</project.junit.version>
    <project.guava.version>33.3.0-android</project.guava.version>
    <project.appengine.version>2.0.30</project.appengine.version>
    <project.findbugs.version>3.0.2</project.findbugs.version>
    <deploy.autorelease>false</deploy.autorelease>
    <project.error-prone.version>2.32.0</project.error-prone.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.auth</groupId>
        <artifactId>google-auth-library-credentials</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.auth</groupId>
        <artifactId>google-auth-library-oauth2-http</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.auth</groupId>
        <artifactId>google-auth-library-cloudshell</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-bom</artifactId>
        <version>${project.google.http.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-api-1.0-sdk</artifactId>
        <version>${project.appengine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${project.guava.version}</version>
		<exclusions>
			<exclusion>
        		<groupId>com.google.j2objc</groupId>
        		<artifactId>j2objc-annotations</artifactId>
			</exclusion>
		</exclusions>
      </dependency>
      <dependency>
        <groupId>com.google.code.findbugs</groupId>
        <artifactId>jsr305</artifactId>
        <version>${project.findbugs.version}</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${project.junit.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine</artifactId>
        <version>${project.appengine.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.google.auth</groupId>
        <artifactId>google-auth-library-oauth2-http</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
        <classifier>testlib</classifier>
      </dependency>
      <dependency>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
          <version>${project.error-prone.version}</version>
          <scope>compile</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <!-- This is the parent, so only define pluginManagement, not plugins. -->
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.sonatype.plugins</groupId>
          <artifactId>nexus-staging-maven-plugin</artifactId>
          <version>1.7.0</version>
          <extensions>true</extensions>
          <configuration>
            <serverId>ossrh</serverId>
            <nexusUrl>https://google.oss.sonatype.org/</nexusUrl>
            <autoReleaseAfterClose>${deploy.autorelease}</autoReleaseAfterClose>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.3.1</version>
          <executions>
            <execution>
              <id>attach-sources</id>
              <goals>
                <goal>jar-no-fork</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>3.10.0</version>
          <configuration>
            <source>8</source>
            <failOnError>false</failOnError>
          </configuration>
          <executions>
            <execution>
              <id>attach-javadocs</id>
              <goals>
                <goal>jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.4.2</version>
          <executions>
            <execution>
              <goals>
                <goal>test-jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>3.5.0</version>
          <configuration>
            <reportNameSuffix>sponge_log</reportNameSuffix>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>3.8.0</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>2.8</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.13.0</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>3.5.0</version>
        <executions>
          <execution>
            <id>checkstyle</id>
            <phase>validate</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <headerLocation>java.header</headerLocation>
              <configLocation>license-checks.xml</configLocation>
              <consoleOutput>true</consoleOutput>
              <failOnViolation>true</failOnViolation>
              <violationSeverity>error</violationSeverity>
              <failsOnError>true</failsOnError>
              <includeTestSourceDirectory>true</includeTestSourceDirectory>
              <!-- Explicitly set the source directory to avoid running checkstyle on generated sources. -->
              <sourceDirectories>
                <sourceDirectory>src/main</sourceDirectory>
              </sourceDirectories>
              <testSourceDirectories>
                <testSourceDirectory>src/test</testSourceDirectory>
              </testSourceDirectories>
              <excludes>com.google.auth.oauth2.ExternalAccountCredentials</excludes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <version>3.20.0</version>
        <configuration>
          <skipDeploy>true</skipDeploy>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.8.12</version>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <configuration>
          <ignoredDifferencesFile>clirr-ignored-differences.xml</ignoredDifferencesFile>
          <logResults>true</logResults>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.3.1</version>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>3.10.0</version>
        <reportSets>
          <reportSet>
            <id>html</id>
            <reports>
              <report>aggregate</report>
              <report>javadoc</report>
            </reports>
          </reportSet>
        </reportSets>
        <configuration>
          <failOnError>false</failOnError>
          <doclint>none</doclint>
          <source>8</source>
          <outputDirectory>${project.build.directory}/javadoc</outputDirectory>
          <doctitle>Google Auth Library for Java ${project.version}</doctitle>
          <overview>${basedir}/overview.html</overview>
          <groups>
            <group>
              <title>google-auth-library-credentials</title>
              <packages>com.google.auth*</packages>
            </group>
            <group>
              <title>google-auth-library-oauth2-http</title>
              <packages>com.google.auth.http*:com.google.auth.oauth2*</packages>
            </group>
            <group>
              <title>google-auth-library-appengine</title>
              <packages>com.google.auth.appengine*</packages>
            </group>
          </groups>
          <links>
            <link>http://download.oracle.com/javase/7/docs/api/</link>
            <link>http://cloud.google.com/appengine/docs/java/javadoc</link>
          </links>
          <windowtitle>google-auth-library ${project.version}</windowtitle>
        </configuration>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>release-sign-artifacts</id>
      <activation>
        <property>
          <name>performRelease</name>
          <value>true</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>3.3.1</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.10.0</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>3.2.6</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
                <configuration>
                  <gpgArguments>
                    <arg>--pinentry-mode</arg>
                    <arg>loopback</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
