<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.google.auth</groupId>
    <artifactId>google-auth-library-parent</artifactId>
    <version>1.27.0</version><!-- {x-version-update:google-auth-library-parent:current} -->
    <relativePath>../pom.xml</relativePath>
  </parent>

  <artifactId>google-auth-library-oauth2-http</artifactId>
  <name>Google Auth Library for Java - OAuth2 HTTP</name>

  <distributionManagement>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://google.oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>

  <profiles>
    <profile>
      <id>junit47</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>${surefire.version}</version>
            <configuration>
              <!-- Excludes integration tests when unit tests are run. -->
              <excludes>
                <exclude>**/IT*.java</exclude>
                <exclude>**/functional/*.java</exclude>
              </excludes>
              <reportNameSuffix>sponge_log</reportNameSuffix>
            </configuration>
            <dependencies>
              <dependency>
                <groupId>org.apache.maven.surefire</groupId>
                <artifactId>surefire-junit47</artifactId>
                <version>${surefire.version}</version>
              </dependency>
            </dependencies>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>native-test</id>
      <build>
        <plugins>
          <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${surefire.version}</version>
            <configuration>
              <excludes combine.self="override"></excludes>
              <includes>
                <include>**/IT*.java</include>
                <include>**/functional/*.java</include>
            </includes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <build>
    <sourceDirectory>java</sourceDirectory>
    <resources>
      <resource>
        <directory>resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
    <testSourceDirectory>javatests</testSourceDirectory>
    <testResources>
      <testResource>
        <directory>testresources</directory>
      </testResource>
    </testResources>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>resources</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <configuration>
          <ignoredUnusedDeclaredDependencies>com.google.auto.value:auto-value
          </ignoredUnusedDeclaredDependencies>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifest>
              <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
            </manifest>
            <manifestEntries>
              <Automatic-Module-Name>com.google.auth.oauth2</Automatic-Module-Name>
            </manifestEntries>
          </archive>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
            <configuration>
              <skip>false</skip>
              <classifier>testlib</classifier>
              <includes>
                <include>**/Mock*</include>
                <include>**/*TestUtil*</include>
                <include>**/BaseSerializationTest*</include>
              </includes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
        <version>3.5.0</version>
        <configuration>
          <forkedProcessTimeoutInSeconds>1200</forkedProcessTimeoutInSeconds>
          <reportNameSuffix>sponge_log</reportNameSuffix>
          <includes>
            <include>**/IT*.java</include>
            <include>**/functional/*.java</include>
          </includes>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>integration-test</goal>
              <goal>verify</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${surefire.version}</version>
        <configuration>
          <!-- Excludes integration tests when unit tests are run. -->
          <excludes>
            <exclude>**/IT*.java</exclude>
            <exclude>**/functional/*.java</exclude>
          </excludes>
          <reportNameSuffix>sponge_log</reportNameSuffix>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.8.12</version>
        <executions>
          <execution>
            <id>jacoco-check</id>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <rules>
                <rule>
                  <element>BUNDLE</element>
                  <limits>
                    <limit>
                      <counter>LINE</counter>
                      <value>COVEREDRATIO</value>
                      <minimum>0.910</minimum>
                    </limit>
                  </limits>
                </rule>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <dependency>
      <groupId>com.google.auto.value</groupId>
      <artifactId>auto-value-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.auth</groupId>
      <artifactId>google-auth-library-credentials</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client-gson</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.errorprone</groupId>
      <artifactId>error_prone_annotations</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-core</artifactId>
      <version>1.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>4.11.0</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
