<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.google.auth</groupId>
    <artifactId>google-auth-library-parent</artifactId>
    <version>1.27.0</version><!-- {x-version-update:google-auth-library-parent:current} -->
    <relativePath>../pom.xml</relativePath>
  </parent>

  <artifactId>google-auth-library-credentials</artifactId>
  <name>Google Auth Library for Java - Credentials</name>

  <distributionManagement>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://google.oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>

  <build>
    <sourceDirectory>java</sourceDirectory>
    <testSourceDirectory>javatests</testSourceDirectory>
    <plugins>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>  
            <manifestEntries>
              <Automatic-Module-Name>com.google.auth</Automatic-Module-Name>
            </manifestEntries>
          </archive> 
        </configuration>
      </plugin>
    </plugins>
  </build>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

</project>
