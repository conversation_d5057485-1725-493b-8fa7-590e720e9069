<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.jayway.jsonpath</groupId>
  <artifactId>json-path</artifactId>
  <version>2.9.0</version>
  <name>json-path</name>
  <description>A library to query and verify JSON</description>
  <url>https://github.com/jayway/JsonPath</url>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>kalle.stenflo</id>
      <name>Kalle Stenflo</name>
      <email>kalle.stenflo (a) gmail.com</email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/jayway/JsonPath.git</connection>
    <developerConnection>scm:git:git://github.com/jayway/JsonPath.git</developerConnection>
    <url>scm:git:git://github.com/jayway/JsonPath.git</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>net.minidev</groupId>
      <artifactId>json-smart</artifactId>
      <version>2.5.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>2.0.11</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
</project>
