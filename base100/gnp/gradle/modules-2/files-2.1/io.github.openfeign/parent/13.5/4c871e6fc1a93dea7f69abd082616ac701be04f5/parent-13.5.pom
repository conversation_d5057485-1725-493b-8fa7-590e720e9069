<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright © 2012 The Feign Authors (<EMAIL>)

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>io.github.openfeign</groupId>
  <artifactId>parent</artifactId>
  <version>13.5</version>
  <packaging>pom</packaging>

  <name>Feign (Parent)</name>
  <description>Feign makes writing java http clients easier</description>
  <url>https://github.com/openfeign/feign</url>
  <inceptionYear>2012</inceptionYear>

  <organization>
    <name>OpenFeign</name>
    <url>https://github.com/openfeign</url>
  </organization>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>velo</id>
      <name>Marvin Herman Froeder</name>
      <email>velo br at gmail dot com</email>
      <url>about.me/velo</url>
    </developer>
    <developer>
      <id>kdavisk6</id>
      <name>Kevin Davis</name>
      <email><EMAIL></email>
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Adrian Cole</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Spencer Gibb</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Artem Labazin</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Tomasz Juchniewicz</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Guillaume Simard</name>
    </contributor>
  </contributors>

  <modules>
    <module>core</module>
    <module>gson</module>
    <module>httpclient</module>
    <module>hc5</module>
    <module>hystrix</module>
    <module>jackson</module>
    <module>jackson-jaxb</module>
    <module>jackson-jr</module>
    <module>jaxb</module>
    <module>jaxb-jakarta</module>
    <module>jaxrs</module>
    <module>jaxrs2</module>
    <module>jaxrs3</module>
    <module>jaxrs4</module>
    <module>java11</module>
    <module>jakarta</module>
    <module>json</module>
    <module>okhttp</module>
    <module>googlehttpclient</module>
    <module>ribbon</module>
    <module>sax</module>
    <module>slf4j</module>
    <module>spring</module>
    <module>spring4</module>
    <module>soap</module>
    <module>soap-jakarta</module>
    <module>reactive</module>
    <module>dropwizard-metrics4</module>
    <module>dropwizard-metrics5</module>
    <module>kotlin</module>
    <module>micrometer</module>
    <module>mock</module>
    <module>apt-test-generator</module>
    <module>annotation-error-decoder</module>
    <module>example-github</module>
    <module>example-github-with-coroutine</module>
    <module>example-wikipedia</module>
    <module>example-wikipedia-with-springboot</module>
    <module>benchmark</module>
    <module>moshi</module>
    <module>fastjson2</module>
    <module>feign-form</module>
    <module>feign-form-spring</module>
  </modules>

  <scm>
    <connection>scm:git:**************:OpenFeign/feign.git</connection>
    <developerConnection>scm:git:**************:OpenFeign/feign.git</developerConnection>
    <tag>HEAD</tag>
    <url>https://github.com/openfeign/feign</url>
  </scm>

  <issueManagement>
    <system>Github</system>
    <url>https://github.com/openfeign/feign/issues</url>
  </issueManagement>

  <distributionManagement>
    <repository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.build.resourceEncoding>UTF-8</project.build.resourceEncoding>

    <moditect.skip>false</moditect.skip>
    <!-- specifying jvm arguments -->
    <jvm.options>-Duser.language=en</jvm.options>

    <!-- default bytecode version for src/main -->
    <main.java.version>1.8</main.java.version>
    <latest.java.version>21</latest.java.version>

    <!-- default bytecode version for src/test -->
    <maven.compiler.source>${main.java.version}</maven.compiler.source>
    <maven.compiler.target>${main.java.version}</maven.compiler.target>

    <okhttp3.version>4.12.0</okhttp3.version>
    <guava.version>33.3.1-jre</guava.version>
    <googlehttpclient.version>1.45.0</googlehttpclient.version>
    <gson.version>2.11.0</gson.version>
    <moshi.version>1.15.1</moshi.version>
    <slf4j.version>2.0.16</slf4j.version>
    <json.version>20240303</json.version>

    <junit5.version>5.11.2</junit5.version>
    <jackson.version>2.18.0</jackson.version>
    <assertj.version>3.26.3</assertj.version>
    <mockito.version>5.14.1</mockito.version>
    <fastjson2.version>2.0.53</fastjson2.version>

    <git-code-format-maven-plugin.version>5.3</git-code-format-maven-plugin.version>
    <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
    <maven-install-plugin.version>3.1.3</maven-install-plugin.version>
    <maven-source-plugin.version>3.3.1</maven-source-plugin.version>
    <maven-javadoc-plugin.version>3.10.1</maven-javadoc-plugin.version>
    <license-maven-plugin.version>4.6</license-maven-plugin.version>
    <maven-jar-plugin.version>3.4.2</maven-jar-plugin.version>
    <maven-release-plugin.version>3.1.1</maven-release-plugin.version>
    <maven-bundle-plugin.version>5.1.9</maven-bundle-plugin.version>
    <centralsync-maven-plugin.version>0.1.1</centralsync-maven-plugin.version>
    <maven-surefire-plugin.version>3.5.1</maven-surefire-plugin.version>
    <bom-generator.version>0.200.0</bom-generator.version>
    <bom.template.file.path>file://${project.basedir}/src/config/bom.xml</bom.template.file.path>
    <maven-scm-plugin.version>2.1.0</maven-scm-plugin.version>
    <maven-versions-plugin.version>2.17.1</maven-versions-plugin.version>
    <maven-gpg-plugin.version>3.2.7</maven-gpg-plugin.version>
    <maven-deploy-plugin.version>3.1.3</maven-deploy-plugin.version>
    <docker-maven-plugin.version>1.2.2</docker-maven-plugin.version>
    <moditect-maven-plugin.version>1.2.2.Final</moditect-maven-plugin.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-core</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-core</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-gson</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-httpclient</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-hystrix</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jackson-jaxb</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jackson</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jaxb</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jaxrs</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jaxrs</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jaxrs</artifactId>
        <version>${project.version}</version>
        <classifier>jakarta</classifier>
        <exclusions>
          <exclusion>
            <groupId>javax.ws.rs</groupId>
            <artifactId>jsr311-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jaxrs2</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jaxrs2</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jaxrs2</artifactId>
        <version>${project.version}</version>
        <classifier>jakarta</classifier>
        <exclusions>
          <exclusion>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>${project.groupId}</groupId>
            <artifactId>feign-jaxrs</artifactId>
          </exclusion>
        </exclusions>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-jaxrs3</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-mock</artifactId>
        <version>${project.version}</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-okhttp</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-ribbon</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-sax</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-slf4j</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-kotlin</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-micrometer</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-form</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>feign-form-spring</artifactId>
        <version>${project.version}</version>
      </dependency>

      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>${junit5.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <dependency>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
        <version>${gson.version}</version>
      </dependency>

      <dependency>
        <groupId>com.squareup.moshi</groupId>
        <artifactId>moshi</artifactId>
        <version>${moshi.version}</version>
      </dependency>

      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>${assertj.version}</version>
      </dependency>

      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-bom</artifactId>
        <version>${okhttp3.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>

      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client</artifactId>
        <version>${googlehttpclient.version}</version>
      </dependency>

      <dependency>
        <groupId>org.json</groupId>
        <artifactId>json</artifactId>
        <version>${json.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-objects</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-annotation-support</artifactId>
        <version>${jackson.version}</version>
      </dependency>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-simple</artifactId>
        <version>${slf4j.version}</version>
      </dependency>

      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-nop</artifactId>
        <version>${slf4j.version}</version>
      </dependency>

      <dependency>
        <groupId>com.alibaba.fastjson2</groupId>
        <artifactId>fastjson2</artifactId>
        <version>${fastjson2.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <defaultGoal>clean install</defaultGoal>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven-compiler-plugin.version}</version>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${maven-jar-plugin.version}</version>
          <configuration>
            <archive>
              <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
            </archive>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
          <configuration>
            <redirectTestOutputToFile>true</redirectTestOutputToFile>
            <trimStackTrace>false</trimStackTrace>
            <argLine>${jvm.options}</argLine>
            <excludes>
              <exclude></exclude>
            </excludes>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
          <configuration>
            <redirectTestOutputToFile>true</redirectTestOutputToFile>
            <trimStackTrace>false</trimStackTrace>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.moditect</groupId>
        <artifactId>moditect-maven-plugin</artifactId>
        <version>${moditect-maven-plugin.version}</version>
        <configuration>
          <skip>${moditect.skip}</skip>
          <overwriteExistingFiles>true</overwriteExistingFiles>
          <module>
            <moduleInfo>
              <!-- module name will be derived from filename -->
              <!-- export everything -->
              <exports>*;</exports>
              <!-- declare services consumed by the artifact -->
              <addServiceUses>true</addServiceUses>
            </moduleInfo>
          </module>
          <jdepsExtraArgs>
            <arg>--multi-release=9</arg>
          </jdepsExtraArgs>
        </configuration>
        <executions>
          <execution>
            <id>add-module-infos</id>
            <goals>
              <goal>add-module-info</goal>
            </goals>
            <phase>package</phase>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <inherited>true</inherited>
        <configuration>
          <source>${main.java.version}</source>
          <target>${main.java.version}</target>
        </configuration>
        <executions>
          <!-- Ensure main source tree compiles to Java ${main.java.version} bytecode. -->
          <execution>
            <id>default-compile</id>
            <goals>
              <goal>compile</goal>
            </goals>
            <phase>compile</phase>
            <configuration>
              <source>${main.java.version}</source>
              <target>${main.java.version}</target>
            </configuration>
          </execution>
          <execution>
            <id>default-testCompile</id>
            <goals>
              <goal>testCompile</goal>
            </goals>
            <phase>test-compile</phase>
            <configuration>
              <fork>true</fork>
              <compilerArgs>
                <arg>-parameters</arg>
              </compilerArgs>
              <source>${latest.java.version}</source>
              <target>${latest.java.version}</target>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-install-plugin</artifactId>
        <version>${maven-install-plugin.version}</version>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>${maven-release-plugin.version}</version>
        <configuration>
          <useReleaseProfile>false</useReleaseProfile>
          <releaseProfiles>release</releaseProfiles>
          <autoVersionSubmodules>true</autoVersionSubmodules>
          <tagNameFormat>@{project.version}</tagNameFormat>
        </configuration>
      </plugin>

      <plugin>
        <groupId>io.zipkin.centralsync-maven-plugin</groupId>
        <artifactId>centralsync-maven-plugin</artifactId>
        <version>${centralsync-maven-plugin.version}</version>
        <configuration>
          <subject>openfeign</subject>
          <repo>maven</repo>
          <packageName>feign</packageName>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>${maven-bundle-plugin.version}</version>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <goals>
              <goal>manifest</goal>
            </goals>
            <phase>process-classes</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.mycila</groupId>
        <artifactId>license-maven-plugin</artifactId>
        <version>${license-maven-plugin.version}</version>
        <configuration>
          <properties>
            <owner>The Feign Authors</owner>
            <email><EMAIL></email>
          </properties>
          <licenseSets>
            <licenseSet>
              <header>com/mycila/maven/plugin/license/templates/APACHE-2.txt</header>

              <excludes>
                <exclude>.travis.yml</exclude>
                <exclude>.editorconfig</exclude>
                <exclude>.gitattributes</exclude>
                <exclude>.gitignore</exclude>
                <exclude>.mvn/**</exclude>
                <exclude>mvnw*</exclude>
                <exclude>etc/header.txt</exclude>
                <exclude>**/.idea/**</exclude>
                <exclude>**/target/**</exclude>
                <exclude>**/scripts/**</exclude>
                <exclude>**/src/config/**</exclude>
                <exclude>**/codequality/**</exclude>
                <exclude>LICENSE</exclude>
                <exclude>NOTICE</exclude>
                <exclude>OSSMETADATA</exclude>
                <exclude>**/*.md</exclude>
                <exclude>**/*.asciidoc</exclude>
                <exclude>**/*.iuml</exclude>
                <exclude>bnd.bnd</exclude>
                <exclude>travis/**</exclude>
                <exclude>src/test/resources/**</exclude>
                <exclude>src/main/resources/**</exclude>
                <exclude>.circleci/**</exclude>
              </excludes>
            </licenseSet>
          </licenseSets>
          <strictCheck>true</strictCheck>
          <mapping>
            <java>SLASHSTAR_STYLE</java>
          </mapping>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>com.mycila</groupId>
            <artifactId>license-maven-plugin-git</artifactId>
            <version>${license-maven-plugin.version}</version>
          </dependency>
        </dependencies>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
            <phase>validate</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.cosium.code</groupId>
        <artifactId>git-code-format-maven-plugin</artifactId>
        <version>${git-code-format-maven-plugin.version}</version>
        <dependencies>
          <!-- Enable https://github.com/google/google-java-format -->
          <dependency>
            <groupId>com.cosium.code</groupId>
            <artifactId>google-java-format</artifactId>
            <version>${git-code-format-maven-plugin.version}</version>
          </dependency>
        </dependencies>
        <executions>
          <!-- On commit, format the modified files -->
          <execution>
            <id>install-formatter-hook</id>
            <goals>
              <goal>install-hooks</goal>
            </goals>
            <inherited>false</inherited>
          </execution>
          <!-- On Maven verify phase, fail if any file (including unmodified) is
            badly formatted -->
          <execution>
            <id>validate-code-format</id>
            <goals>
              <goal>validate-code-format</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>de.qaware.maven</groupId>
        <artifactId>go-offline-maven-plugin</artifactId>
        <version>1.2.8</version>
        <configuration>
          <dynamicDependencies>
            <DynamicDependency>
              <groupId>org.apache.maven.surefire</groupId>
              <artifactId>surefire-junit4</artifactId>
              <version>${maven-surefire-plugin.version}</version>
              <repositoryType>PLUGIN</repositoryType>
            </DynamicDependency>
            <DynamicDependency>
              <groupId>org.codehaus.mojo.signature</groupId>
              <artifactId>java18</artifactId>
              <version>1.0</version>
              <type>signature</type>
              <repositoryType>MAIN</repositoryType>
            </DynamicDependency>
          </dynamicDependencies>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.ekryd.sortpom</groupId>
        <artifactId>sortpom-maven-plugin</artifactId>
        <version>4.0.0</version>
        <configuration>
          <keepBlankLines>true</keepBlankLines>
          <lineSeparator>\n</lineSeparator>
          <predefinedSortOrder>recommended_2008_06</predefinedSortOrder>
          <createBackupFile>false</createBackupFile>
        </configuration>
        <executions>
          <execution>
            <id>format</id>
            <goals>
              <goal>sort</goal>
            </goals>
            <phase>verify</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>io.sundr</groupId>
        <artifactId>sundr-maven-plugin</artifactId>
        <version>${bom-generator.version}</version>
        <inherited>false</inherited>
        <configuration>
          <bomTemplateUrl>${bom.template.file.path}</bomTemplateUrl>
          <boms>
            <bom>
              <artifactId>feign-bom</artifactId>
              <name>Feign (Bill Of Materials)</name>

              <properties>
                <license.skip>true</license.skip>
              </properties>

              <modules>
                <includes>
                  <include>io.github.openfeign:*</include>
                </includes>
                <excludes>
                  <exclude>*:feign-example-*</exclude>
                  <exclude>*:feign-benchmark</exclude>
                </excludes>
              </modules>
            </bom>
          </boms>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>generate-bom</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>versions-maven-plugin</artifactId>
        <version>${maven-versions-plugin.version}</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>${maven-deploy-plugin.version}</version>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-scm-plugin</artifactId>
        <version>${maven-scm-plugin.version}</version>
        <configuration>
          <tag>${project.version}</tag>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <version>1.7.0</version>
        <extensions>true</extensions>
        <configuration>
          <serverId>ossrh</serverId>
          <nexusUrl>https://oss.sonatype.org/</nexusUrl>
          <autoReleaseAfterClose>true</autoReleaseAfterClose>
          <stagingProgressTimeoutMinutes>15</stagingProgressTimeoutMinutes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.5.0</version>
        <executions>
          <execution>
            <id>enforce-no-repositories</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireNoRepositories>
                  <message>Feign should only depend on artifacts readily available on maven central</message>
                </requireNoRepositories>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>quickbuild</id>
      <properties>
        <skipTests>true</skipTests>
        <enforcer.skip>true</enforcer.skip>
        <animal.sniffer.skip>true</animal.sniffer.skip>
        <japicmp.skip>true</japicmp.skip>
        <maven.javadoc.skip>true</maven.javadoc.skip>
        <maven.javadoc.skip>true</maven.javadoc.skip>
        <formatter.skip>true</formatter.skip>
        <license.skip>true</license.skip>
      </properties>
    </profile>

    <profile>
      <id>windows</id>
      <activation>
        <os>
          <family>Windows</family>
        </os>
      </activation>
      <properties>
        <bom.template.file.path>file:///${project.basedir}/src/config/bom.xml</bom.template.file.path>
      </properties>
    </profile>

    <profile>
      <id>release</id>
      <build>
        <plugins>
          <!-- Creates source jar -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>${maven-source-plugin.version}</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>

          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>${maven-javadoc-plugin.version}</version>
            <configuration>
              <failOnError>false</failOnError>
            </configuration>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
                <phase>package</phase>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>${maven-gpg-plugin.version}</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <goals>
                  <goal>sign</goal>
                </goals>
                <phase>verify</phase>
                <configuration>
                  <gpgArguments>
                    <arg>--pinentry-mode</arg>
                    <arg>loopback</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>openrewrite</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.openrewrite.maven</groupId>
            <artifactId>rewrite-maven-plugin</artifactId>
            <version>5.41.0</version>

            <dependencies>
              <dependency>
                <groupId>org.openrewrite.recipe</groupId>
                <artifactId>rewrite-testing-frameworks</artifactId>
                <version>2.19.0</version>
              </dependency>
              <dependency>
                <groupId>org.openrewrite.recipe</groupId>
                <artifactId>rewrite-migrate-java</artifactId>
                <version>2.26.1</version>
              </dependency>
            </dependencies>

            <executions>
              <execution>
                <id>tests</id>
                <goals>
                  <goal>runNoFork</goal>
                </goals>
                <phase>verify</phase>
                <configuration>
                  <exportDatatables>true</exportDatatables>
                  <activeRecipes>
                    <recipe>org.openrewrite.java.testing.hamcrest.MigrateHamcrestToAssertJ</recipe>
                    <recipe>org.openrewrite.java.testing.junit5.AssertToAssertions</recipe>
                    <recipe>org.openrewrite.java.testing.assertj.JUnitToAssertj</recipe>
                    <recipe>org.openrewrite.java.testing.assertj.Assertj</recipe>
                    <recipe>org.openrewrite.java.migrate.UpgradeToJava21</recipe>
                  </activeRecipes>
                  <exclusions>
                    <exclusion>**/src/main/java/**</exclusion>
                    <exclusion>pom.xml</exclusion>
                  </exclusions>
                </configuration>
              </execution>
              <execution>
                <id>sources</id>
                <goals>
                  <goal>runNoFork</goal>
                </goals>
                <phase>verify</phase>
                <configuration>
                  <exportDatatables>true</exportDatatables>
                  <activeRecipes>
                    <recipe>org.openrewrite.java.migrate.UpgradeToJava8</recipe>
                  </activeRecipes>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>toolchain</id>
      <activation>
        <property>
          <name>!toolchain.skip</name>
        </property>
      </activation>

      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-toolchains-plugin</artifactId>
            <version>3.2.0</version>
            <executions>
              <execution>
                <goals>
                  <goal>toolchain</goal>
                </goals>
                <configuration>
                  <toolchains>
                    <jdk>
                      <version>${main.java.version}</version>
                    </jdk>
                  </toolchains>
                </configuration>
              </execution>
              <execution>
                <id>test</id>
                <goals>
                  <goal>toolchain</goal>
                </goals>
                <phase>generate-test-sources</phase>
                <configuration>
                  <toolchains>
                    <jdk>
                      <version>${latest.java.version}</version>
                    </jdk>
                  </toolchains>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <jdkToolchain>
                <version>${latest.java.version}</version>
              </jdkToolchain>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-failsafe-plugin</artifactId>
            <configuration>
              <jdkToolchain>
                <version>${latest.java.version}</version>
              </jdkToolchain>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
