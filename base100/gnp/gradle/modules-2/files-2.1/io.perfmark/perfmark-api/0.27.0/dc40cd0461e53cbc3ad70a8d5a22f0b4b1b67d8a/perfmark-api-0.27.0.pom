<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.perfmark</groupId>
  <artifactId>perfmark-api</artifactId>
  <version>0.27.0</version>
  <name>perfmark:perfmark-api</name>
  <description>PerfMark API</description>
  <url>https://github.com/perfmark/perfmark</url>
  <licenses>
    <license>
      <name>Apache 2.0</name>
      <url>https://opensource.org/licenses/Apache-2.0</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>carl-mastrangelo</id>
      <name>Carl Mastrangelo</name>
      <email><EMAIL></email>
      <url>https://www.perfmark.io/</url>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/perfmark/perfmark.git</connection>
    <developerConnection>scm:**************:perfmark/perfmark.git</developerConnection>
    <url>https://github.com/perfmark/perfmark</url>
  </scm>
</project>
