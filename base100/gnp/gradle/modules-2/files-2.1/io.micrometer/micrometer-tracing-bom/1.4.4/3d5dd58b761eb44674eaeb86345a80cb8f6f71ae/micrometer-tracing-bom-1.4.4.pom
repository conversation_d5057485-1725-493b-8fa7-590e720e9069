<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.micrometer</groupId>
  <artifactId>micrometer-tracing-bom</artifactId>
  <version>1.4.4</version>
  <packaging>pom</packaging>
  <name>micrometer-tracing-bom</name>
  <description>Micrometer Tracing BOM (Bill of Materials) for managing Micrometer Tracing artifact versions</description>
  <url>https://github.com/micrometer-metrics/tracing</url>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>shakuzen</id>
      <name><PERSON></name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>jonatan-ivanov</id>
      <name>Jonatan Ivanov</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>marcingrzejszczak</id>
      <name>Marcin Grzejszczak</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <url>**************:micrometer-metrics/tracing.git</url>
  </scm>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>docs</artifactId>
        <version>1.4.4</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-tracing</artifactId>
        <version>1.4.4</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-tracing-bridge-brave</artifactId>
        <version>1.4.4</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-tracing-bridge-otel</artifactId>
        <version>1.4.4</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-tracing-integration-test</artifactId>
        <version>1.4.4</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-tracing-reporter-wavefront</artifactId>
        <version>1.4.4</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-tracing-test</artifactId>
        <version>1.4.4</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-bom</artifactId>
        <version>1.14.5</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <properties>
    <nebula_Manifest_Version>1.0</nebula_Manifest_Version>
    <nebula_Implementation_Title>io.micrometer#micrometer-tracing-bom;1.4.4</nebula_Implementation_Title>
    <nebula_Implementation_Version>1.4.4</nebula_Implementation_Version>
    <nebula_Built_Status>release</nebula_Built_Status>
    <nebula_Built_By>circleci</nebula_Built_By>
    <nebula_Built_OS>Linux</nebula_Built_OS>
    <nebula_Build_Timezone>Etc/UTC</nebula_Build_Timezone>
    <nebula_Build_Date_UTC>2025-03-10T11:45:45.658484558Z</nebula_Build_Date_UTC>
    <nebula_Build_Date>2025-03-10_11:45:45</nebula_Build_Date>
    <nebula_Gradle_Version>8.12.1</nebula_Gradle_Version>
    <nebula_Module_Source>/micrometer-tracing-bom</nebula_Module_Source>
    <nebula_Module_Origin>**************:micrometer-metrics/tracing.git</nebula_Module_Origin>
    <nebula_Change>00bd40f</nebula_Change>
    <nebula_Full_Change>00bd40fba395b0bc5a51e01d772f4b287dc290f7</nebula_Full_Change>
    <nebula_Branch>HEAD</nebula_Branch>
    <nebula_Build_Host>5e9ef446152b</nebula_Build_Host>
    <nebula_Build_Job>deploy</nebula_Build_Job>
    <nebula_Build_Number>9831</nebula_Build_Number>
    <nebula_Build_Id>9831</nebula_Build_Id>
    <nebula_Build_Url>https://circleci.com/gh/micrometer-metrics/tracing/9831</nebula_Build_Url>
    <nebula_Created_By>20.0.1+9 (Eclipse Adoptium)</nebula_Created_By>
    <nebula_Module_Owner><EMAIL>,<EMAIL>,<EMAIL></nebula_Module_Owner>
    <nebula_Module_Email><EMAIL>,<EMAIL>,<EMAIL></nebula_Module_Email>
  </properties>
</project>
