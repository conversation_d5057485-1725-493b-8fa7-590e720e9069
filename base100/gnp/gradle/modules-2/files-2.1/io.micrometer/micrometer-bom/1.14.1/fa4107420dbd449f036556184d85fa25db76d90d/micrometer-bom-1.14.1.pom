<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.micrometer</groupId>
  <artifactId>micrometer-bom</artifactId>
  <version>1.14.1</version>
  <packaging>pom</packaging>
  <name>micrometer-bom</name>
  <description>Micrometer BOM (Bill of Materials) for managing Micrometer artifact versions</description>
  <url>https://github.com/micrometer-metrics/micrometer</url>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>shakuzen</id>
      <name><PERSON></name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <url>**************:micrometer-metrics/micrometer.git</url>
  </scm>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-commons</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-core</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-jakarta9</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-java11</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-java21</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-jetty11</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-jetty12</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-observation</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-observation-test</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-appoptics</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-atlas</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-azure-monitor</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-cloudwatch2</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-datadog</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-dynatrace</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-elastic</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-ganglia</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-graphite</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-health</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-humio</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-influx</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-jmx</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-kairos</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-new-relic</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-opentsdb</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-otlp</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-prometheus</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-prometheus-simpleclient</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-signalfx</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-stackdriver</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-statsd</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-wavefront</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-test</artifactId>
        <version>1.14.1</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>context-propagation</artifactId>
        <version>1.1.2</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <properties>
    <nebula_Manifest_Version>1.0</nebula_Manifest_Version>
    <nebula_Implementation_Title>io.micrometer#micrometer-bom;1.14.1</nebula_Implementation_Title>
    <nebula_Implementation_Version>1.14.1</nebula_Implementation_Version>
    <nebula_Built_Status>release</nebula_Built_Status>
    <nebula_Built_By>circleci</nebula_Built_By>
    <nebula_Built_OS>Linux</nebula_Built_OS>
    <nebula_Build_Timezone>Etc/UTC</nebula_Build_Timezone>
    <nebula_Build_Date_UTC>2024-11-15T09:15:53.795639782Z</nebula_Build_Date_UTC>
    <nebula_Build_Date>2024-11-15_09:15:53</nebula_Build_Date>
    <nebula_Gradle_Version>8.11</nebula_Gradle_Version>
    <nebula_Module_Source>/micrometer-bom</nebula_Module_Source>
    <nebula_Module_Origin>**************:micrometer-metrics/micrometer.git</nebula_Module_Origin>
    <nebula_Change>2f8b914</nebula_Change>
    <nebula_Full_Change>2f8b914b53382e66001ebb7b1586fc09f304c98b</nebula_Full_Change>
    <nebula_Branch>HEAD</nebula_Branch>
    <nebula_Build_Host>7a1249d95f1e</nebula_Build_Host>
    <nebula_Build_Job>deploy</nebula_Build_Job>
    <nebula_Build_Number>39234</nebula_Build_Number>
    <nebula_Build_Id>39234</nebula_Build_Id>
    <nebula_Build_Url>https://circleci.com/gh/micrometer-metrics/micrometer/39234</nebula_Build_Url>
    <nebula_Created_By>21.0.5+11-LTS (Eclipse Adoptium)</nebula_Created_By>
    <nebula_Module_Owner><EMAIL></nebula_Module_Owner>
    <nebula_Module_Email><EMAIL></nebula_Module_Email>
  </properties>
</project>
