<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.micrometer</groupId>
  <artifactId>micrometer-commons</artifactId>
  <version>1.12.4</version>
  <name>micrometer-commons</name>
  <description>Module containing common code</description>
  <url>https://github.com/micrometer-metrics/micrometer</url>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>shakuzen</id>
      <name><PERSON></name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <url>**************:micrometer-metrics/micrometer.git</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.2</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>1.2.13</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.aspectj</groupId>
      <artifactId>aspectjweaver</artifactId>
      <version>1.9.21.1</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
  </dependencies>
  <properties>
    <nebula_Manifest_Version>1.0</nebula_Manifest_Version>
    <nebula_Implementation_Title>io.micrometer#micrometer-commons;1.12.4</nebula_Implementation_Title>
    <nebula_Implementation_Version>1.12.4</nebula_Implementation_Version>
    <nebula_Built_Status>release</nebula_Built_Status>
    <nebula_Built_By>circleci</nebula_Built_By>
    <nebula_Built_OS>Linux</nebula_Built_OS>
    <nebula_Build_Timezone>Etc/UTC</nebula_Build_Timezone>
    <nebula_Build_Date_UTC>2024-03-11T15:21:27.414328518Z</nebula_Build_Date_UTC>
    <nebula_Build_Date>2024-03-11_15:21:27</nebula_Build_Date>
    <nebula_Gradle_Version>8.6</nebula_Gradle_Version>
    <nebula_Module_Source>/micrometer-commons</nebula_Module_Source>
    <nebula_Module_Origin>**************:micrometer-metrics/micrometer.git</nebula_Module_Origin>
    <nebula_Change>52075c6</nebula_Change>
    <nebula_Full_Change>52075c682e092f87cfc39d9df0ff61a3b4c93ab8</nebula_Full_Change>
    <nebula_Branch>HEAD</nebula_Branch>
    <nebula_Build_Host>81a4eaf02372</nebula_Build_Host>
    <nebula_Build_Job>deploy</nebula_Build_Job>
    <nebula_Build_Number>31228</nebula_Build_Number>
    <nebula_Build_Id>31228</nebula_Build_Id>
    <nebula_Build_Url>https://circleci.com/gh/micrometer-metrics/micrometer/31228</nebula_Build_Url>
    <nebula_Created_By>21.0.2+13-LTS (Eclipse Adoptium)</nebula_Created_By>
    <nebula_Module_Owner><EMAIL></nebula_Module_Owner>
    <nebula_Module_Email><EMAIL></nebula_Module_Email>
    <nebula_X_Compile_Target_JDK>1.8</nebula_X_Compile_Target_JDK>
    <nebula_X_Compile_Source_JDK>1.8</nebula_X_Compile_Source_JDK>
    <nebula_Build_Java_Version>21</nebula_Build_Java_Version>
  </properties>
</project>
