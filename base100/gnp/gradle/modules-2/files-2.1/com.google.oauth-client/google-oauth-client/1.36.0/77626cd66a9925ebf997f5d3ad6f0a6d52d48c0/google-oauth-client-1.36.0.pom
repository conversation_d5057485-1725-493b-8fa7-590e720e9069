<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.google.oauth-client</groupId>
    <artifactId>google-oauth-client-parent</artifactId>
    <version>1.36.0</version><!-- {x-version-update:google-oauth-client:current} -->
    <relativePath>../pom.xml</relativePath>
  </parent>
  <artifactId>google-oauth-client</artifactId>
  <name>Google OAuth Client Library for Java</name>
  <description>
    Google OAuth Client Library for Java. Functionality that works on all supported Java platforms,
    including Java 7 (or higher) desktop (SE) and web (EE), Android, and Google App Engine.
  </description>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <links>
            <link>http://download.oracle.com/javase/7/docs/api/</link>
            <link>https://googleapis.dev/java/google-http-client/${project.http.version}/</link>
          </links>
          <doctitle>${project.name} ${project.version}</doctitle>
          <windowtitle>${project.artifactId} ${project.version}</windowtitle>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifest>
              <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
            </manifest>
            <manifestEntries>
              <Automatic-Module-Name>com.google.api.client.auth</Automatic-Module-Name>
            </manifestEntries>
            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <executions>
          <execution>
            <id>source-jar</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>5.1.8</version>
        <configuration>
          <instructions>
            <Bundle-SymbolicName>com.google.oauth-client</Bundle-SymbolicName>
            <Bundle-RequiredExecutionEnvironment>JavaSE-1.7</Bundle-RequiredExecutionEnvironment>
          </instructions>
        </configuration>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client-gson</artifactId>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
  </dependencies>
</project>
