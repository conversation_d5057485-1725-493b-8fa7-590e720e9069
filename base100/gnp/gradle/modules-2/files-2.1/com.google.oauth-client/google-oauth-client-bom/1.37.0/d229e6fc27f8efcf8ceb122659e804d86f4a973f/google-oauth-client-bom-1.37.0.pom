<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.oauth-client</groupId>
  <artifactId>google-oauth-client-bom</artifactId>
  <version>1.37.0</version><!-- {x-version-update:google-oauth-client:current} -->
  <packaging>pom</packaging>

  <name>Google OAuth Client Library for Java BOM</name>
  <url>https://github.com/googleapis/google-oauth-java-client/tree/master/google-oauth-client-bom</url>
  <description>
    BOM for Google OAuth Client Library for Java
  </description>

  <organization>
    <name>Google LLC</name>
  </organization>

  <developers>
    <developer>
      <id>chingor13</id>
      <name><PERSON></name>
      <email><EMAIL></email>
      <organization>Google LLC</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>

  <scm>
    <connection>scm:git:https://github.com/googleapis/google-http-oauth-client.git</connection>
    <developerConnection>scm:git:**************:googleapis/google-oauth-java-client.git</developerConnection>
    <url>https://github.com/googleapis/google-oauth-java-client</url>
  </scm>

  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <url>https://google.oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://google.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <site>
      <id>github-pages-site</id>
      <name>Deployment through GitHub's site deployment plugin</name>
      <url>site/google-cloud-bom</url>
    </site>
  </distributionManagement>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client</artifactId>
        <version>1.37.0</version><!-- {x-version-update:google-oauth-client:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client-appengine</artifactId>
        <version>1.37.0</version><!-- {x-version-update:google-oauth-client:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client-java6</artifactId>
        <version>1.37.0</version><!-- {x-version-update:google-oauth-client:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client-jetty</artifactId>
        <version>1.37.0</version><!-- {x-version-update:google-oauth-client:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client-servlet</artifactId>
        <version>1.37.0</version><!-- {x-version-update:google-oauth-client:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
  <build>
    <plugins>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
        <version>1.6.13</version>
        <extensions>true</extensions>
        <configuration>
          <serverId>sonatype-nexus-staging</serverId>
          <nexusUrl>https://google.oss.sonatype.org/</nexusUrl>
          <autoReleaseAfterClose>false</autoReleaseAfterClose>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>3.4.1</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-site-plugin</artifactId>
        <version>3.12.1</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.coveo</groupId>
        <artifactId>fmt-maven-plugin</artifactId>
        <version>2.13</version>
        <configuration>
          <style>google</style>
          <verbose>true</verbose>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>com.google.googlejavaformat</groupId>
            <artifactId>google-java-format</artifactId>
            <version>1.15.0</version>
          </dependency>
        </dependencies>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>release-sign-artifacts</id>
      <activation>
        <property>
          <name>performRelease</name>
          <value>true</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>3.0.1</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
                <configuration>
                  <gpgArguments>
                    <arg>--pinentry-mode</arg>
                    <arg>loopback</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
