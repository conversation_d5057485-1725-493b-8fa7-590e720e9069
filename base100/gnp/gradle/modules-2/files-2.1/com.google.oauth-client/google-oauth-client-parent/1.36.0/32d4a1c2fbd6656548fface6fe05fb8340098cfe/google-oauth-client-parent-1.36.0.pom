<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.google.oauth-client</groupId>
  <artifactId>google-oauth-client-parent</artifactId>
  <version>1.36.0</version><!-- {x-version-update:google-oauth-client:current} -->
  <packaging>pom</packaging>
  <name>Parent for the Google OAuth Client Library for Java</name>
  <description>Google OAuth Client Library for Java</description>

  <url>https://github.com/googleapis/google-oauth-java-client</url>

  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/googleapis/google-oauth-java-client/issues</url>
  </issueManagement>

  <inceptionYear>2011</inceptionYear>
  <scm>
    <connection>scm:git:https://github.com/googleapis/google-oauth-java-client.git</connection>
    <developerConnection>scm:git:**************:googleapis/google-oauth-java-client.git</developerConnection>
    <url>https://github.com/googleapis/google-oauth-java-client</url>
  </scm>

  <developers>
    <developer>
      <id>chingor</id>
      <name>Jeff Ching</name>
      <email><EMAIL></email>
    </developer>
  </developers>

  <organization>
    <name>Google</name>
    <url>https://www.google.com/</url>
  </organization>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <url>https://google.oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://google.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>
  <!--
    If you add a new module, make sure to also add it in the following places:
    * below in the dependencyManagement and maven-javadoc-plugin sections
    * google-oauth-client-assembly/classpath-include
    * google-oauth-client-assembly/pom.xml
    * google-oauth-client-assembly/readme.html
    * google-oauth-client-assembly/dependencies/<name>-dependencies.html
        (use mvn project-info-reports:dependencies and copy from
        google-oauth-client-<name>/target/site/dependencies.html)
    * google-oauth-client-assembly/android-properties/*.properties
  -->
  <modules>
    <module>google-oauth-client</module>
    <module>google-oauth-client-appengine</module>
    <module>google-oauth-client-bom</module>
    <module>google-oauth-client-servlet</module>
    <module>google-oauth-client-java6</module>
    <module>google-oauth-client-jetty</module>
    <module>samples/dailymotion-cmdline-sample</module>
    <module>samples/keycloak-pkce-cmdline-sample</module>

    <!-- For deployment reasons, a deployable artifact must be the last one. -->
    <module>google-oauth-client-assembly</module>
  </modules>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.13.2</version>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-api-1.0-sdk</artifactId>
        <version>${project.appengine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-testing</artifactId>
        <version>${project.appengine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-api-stubs</artifactId>
        <version>${project.appengine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${project.guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.findbugs</groupId>
        <artifactId>jsr305</artifactId>
        <version>${project.jsr305.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.jdo</groupId>
        <artifactId>jdo2-api</artifactId>
        <version>${project.jdo2-api.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.servlet</groupId>
        <artifactId>jakarta.servlet-api</artifactId>
        <version>${project.jakarta-servlet-api.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>servlet-api</artifactId>
        <version>${project.servlet-api.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <scope>provided</scope>
        <version>1.15</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-bom</artifactId>
        <version>${project.http.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client-appengine</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client-servlet</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client-java6</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client-jetty</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.datanucleus</groupId>
        <artifactId>datanucleus-core</artifactId>
        <version>${project.datanucleus-core.version}</version>
      </dependency>
      <dependency>
        <groupId>org.datanucleus</groupId>
        <artifactId>datanucleus-api-jdo</artifactId>
        <version>${project.datanucleus-api-jdo.version}</version>
      </dependency>
      <dependency>
        <groupId>org.datanucleus</groupId>
        <artifactId>datanucleus-rdbms</artifactId>
        <version>${project.datanucleus-rdbms.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <!-- This is the parent, so only define pluginManagement, not plugins. -->
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.sonatype.plugins</groupId>
          <artifactId>nexus-staging-maven-plugin</artifactId>
          <version>1.6.13</version>
          <extensions>true</extensions>
          <configuration>
            <serverId>ossrh</serverId>
            <nexusUrl>https://google.oss.sonatype.org/</nexusUrl>
            <autoReleaseAfterClose>${deploy.autorelease}</autoReleaseAfterClose>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>3.4.2</version>
        </plugin>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>2.5.1</version>
          <configuration>
            <source>1.7</source>
            <target>1.7</target>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.2.1</version>
          <executions>
            <execution>
              <id>attach-sources</id>
              <goals>
                <goal>jar-no-fork</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>3.6.3</version>
          <executions>
            <execution>
              <id>attach-javadocs</id>
              <goals>
                <goal>jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.3.0</version>
          <executions>
            <execution>
              <goals>
                <goal>test-jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>3.2.3</version>
          <configuration>
            <argLine>-Xmx1024m</argLine>
            <reportNameSuffix>sponge_log</reportNameSuffix>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>3.0.0</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>findbugs-maven-plugin</artifactId>
          <version>3.0.5</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>2.8</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.22</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>3.5.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.12.1</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.1.0</version><executions>
        <execution>
          <id>enforce-maven</id>
          <goals>
            <goal>enforce</goal>
          </goals>
          <configuration>
            <rules>
              <requireMavenVersion>
                <version>[3.5.4,4.0.0)</version>
              </requireMavenVersion>
            </rules>
          </configuration>
        </execution>
      </executions>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <doclint>none</doclint>
          <source>7</source>
        </configuration>
        <executions>
          <execution>
            <id>aggregate</id>
            <goals>
              <goal>aggregate</goal>
            </goals>
            <phase>site</phase>
            <configuration>
              <links>
                <link>https://docs.oracle.com/javase/7/docs/api/</link>
                <link>https://cloud.google.com/appengine/docs/java/javadoc</link>
                <link>https://googleapis.dev/java/google-http-client/${project.http.version}/</link>
              </links>
              <doctitle>Google OAuth Client Library for Java ${project.version}</doctitle>
              <excludePackageNames>com.google.api.services</excludePackageNames>
              <groups>
                <group>
                  <title>google-oauth-client</title>
                  <packages>com.google.api.client.auth*</packages>
                </group>
                <group>
                  <title>google-oauth-client-appengine</title>
                  <packages>com.google.api.client.extensions.appengine.*</packages>
                </group>
                <group>
                  <title>google-oauth-client-servlet</title>
                  <packages>com.google.api.client.extensions.servlet.*:com.google.api.client.extensions.auth.*:com.google.api.client.extensions.jdo.*</packages>
                </group>
                <group>
                  <title>google-oauth-client-java6</title>
                  <packages>com.google.api.client.extensions.java6.*</packages>
                </group>
                <group>
                  <title>google-oauth-client-jetty</title>
                  <packages>com.google.api.client.extensions.jetty.*</packages>
                </group>
              </groups>
              <windowtitle>google-oauth-java-client ${project.version}</windowtitle>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <configuration>
          <configLocation>checkstyle.xml</configLocation>
          <consoleOutput>true</consoleOutput>
          <suppressionsLocation>checkstyle-suppressions.xml</suppressionsLocation>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <configuration>
          <excludeFilterFile>${basedir}/../findbugs-exclude.xml</excludeFilterFile>
          <plugins>
            <plugin>
              <groupId>com.google.http-client</groupId>
              <artifactId>google-http-client-findbugs</artifactId>
              <version>${project.http.version}</version>
            </plugin>
          </plugins>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <configuration>
          <comparisonVersion>1.19.0</comparisonVersion>
          <ignoredDifferencesFile>${basedir}/../clirr-ignored-differences.xml</ignoredDifferencesFile>
          <logResults>true</logResults>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>java17</artifactId>
            <version>1.0</version>
          </signature>
          <ignores>
            <ignore>com.sun.net.httpserver.*</ignore>
          </ignores>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <!-- Build the dependencies report at package time (needed for the assembly artifact). -->
      <plugin>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>dependencies</goal>
            </goals>
            <phase>package</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>com.coveo</groupId>
        <artifactId>fmt-maven-plugin</artifactId>
        <version>2.9</version>
        <configuration>
          <style>google</style>
          <verbose>true</verbose>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>com.google.googlejavaformat</groupId>
            <artifactId>google-java-format</artifactId>
            <version>1.7</version>
          </dependency>
        </dependencies>
      </plugin>
    </plugins>
  </build>
  <properties>
    <!--
      NOTE: if you make a change to these versions, you MUST make the same change to:
      - google-oauth-client-assembly/android-properties (make the filenames match the version here)
      - google-http-java-client/pom.xml (versions must match)
      - google-http-java-client/google-http-client-assembly/android-properties (make the filenames match the version here)
      - google-api-java-client/pom.xml (versions must match)
      - google-api-java-client/google-api-client-assembly/android-properties (make the filenames match the version here)
      - Internally, update the default features.json file
    -->
    <project.appengine.version>2.0.6</project.appengine.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.http.version>1.43.3</project.http.version>
    <!-- jakarta-servlet-api 5.0.0 is the last version that works with Java 8 -->
    <project.jakarta-servlet-api.version>5.0.0</project.jakarta-servlet-api.version>
    <project.jsr305.version>3.0.2</project.jsr305.version>
    <project.guava.version>31.1-android</project.guava.version>
    <project.xpp3.version>1.1.4c</project.xpp3.version>
    <project.commons-logging.version>1.1.1</project.commons-logging.version>
    <project.jdo2-api.version>2.3-20090302111651</project.jdo2-api.version>
    <project.transaction-api.version>1.1</project.transaction-api.version>
    <project.datanucleus-core.version>3.2.15</project.datanucleus-core.version>
    <project.datanucleus-api-jdo.version>3.2.8</project.datanucleus-api-jdo.version>
    <project.datanucleus-rdbms.version>6.0.0-release</project.datanucleus-rdbms.version>
    <project.datanucleus-maven-plugin.version>5.2.1</project.datanucleus-maven-plugin.version>
    <project.servlet-api.version>2.5</project.servlet-api.version>
    <deploy.autorelease>false</deploy.autorelease>
  </properties>

  <profiles>
    <profile>
      <id>release-sign-artifacts</id>
      <activation>
        <property>
          <name>performRelease</name>
          <value>true</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>3.0.1</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
                <configuration>
                  <gpgArguments>
                    <arg>--pinentry-mode</arg>
                    <arg>loopback</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>docFX</id>
      <activation>
        <property>
          <!-- Activate with -P docFX -->
          <name>docFX</name>
        </property>
      </activation>
      <properties>
        <!-- default config values -->
        <outputpath>${project.build.directory}/docfx-yml</outputpath>
        <projectname>${project.artifactId}</projectname>
        <source>17</source>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.6.3</version>
            <configuration>
              <doclet>com.microsoft.doclet.DocFxDoclet</doclet>
              <useStandardDocletOptions>false</useStandardDocletOptions>
               <!-- custom config with -Dproperty=value -->
              <additionalOptions>
                -outputpath ${outputpath} 
                -projectname ${projectname}
              </additionalOptions>
              <excludePackageNames>com.google.api.services</excludePackageNames>
              <groups>
                <group>
                  <title>google-oauth-client</title>
                  <packages>com.google.api.client.auth*</packages>
                </group>
                <group>
                  <title>google-oauth-client-appengine</title>
                  <packages>com.google.api.client.extensions.appengine.*</packages>
                </group>
                <group>
                  <title>google-oauth-client-servlet</title>
                  <packages>com.google.api.client.extensions.servlet.*:com.google.api.client.extensions.auth.*:com.google.api.client.extensions.jdo.*</packages>
                </group>
                <group>
                  <title>google-oauth-client-java6</title>
                  <packages>com.google.api.client.extensions.java6.*</packages>
                </group>
                <group>
                  <title>google-oauth-client-jetty</title>
                  <packages>com.google.api.client.extensions.jetty.*</packages>
                </group>
              </groups>
              <doclint>none</doclint>
              <show>protected</show>
              <nohelp>true</nohelp>
              <source>${source}</source>
            </configuration>
          </plugin>  
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
