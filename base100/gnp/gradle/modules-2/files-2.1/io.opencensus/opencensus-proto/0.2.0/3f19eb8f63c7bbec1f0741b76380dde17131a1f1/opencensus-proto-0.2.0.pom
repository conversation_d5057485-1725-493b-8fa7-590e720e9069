<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.opencensus</groupId>
  <artifactId>opencensus-proto</artifactId>
  <version>0.2.0</version>
  <name>OpenCensus</name>
  <description>Opencensus Proto</description>
  <url>https://github.com/census-instrumentation/opencensus-proto</url>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>io.opencensus</id>
      <name>OpenCensus Contributors</name>
      <email><EMAIL></email>
      <url>opencensus.io</url>
      <organization>OpenCensus Authors</organization>
      <organizationUrl>https://www.opencensus.io</organizationUrl>
    </developer>
  </developers>
  <scm>
    <connection>scm:svn:https://github.com/census-instrumentation/opencensus-proto</connection>
    <developerConnection>scm:git:**************/census-instrumentation/opencensus-proto</developerConnection>
    <url>https://github.com/census-instrumentation/opencensus-proto</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>3.5.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-protobuf</artifactId>
      <version>1.14.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-stub</artifactId>
      <version>1.14.0</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
