<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud.opentelemetry</groupId>
  <artifactId>shared-resourcemapping</artifactId>
  <version>0.32.0</version>
  <name>OpenTelemetry Operations Java</name>
  <description>Resource helper utilites for GCP within OpenTelemetry</description>
  <url>https://github.com/GoogleCloudPlatform/opentelemetry-operations-java</url>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>com.google.cloud.opentelemetry</id>
      <name>OpenTelemetry Operations Contributors</name>
      <email><EMAIL></email>
      <organization>Google Inc</organization>
      <organizationUrl>https://cloud.google.com/products/operations</organizationUrl>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/GoogleCloudPlatform/opentelemetry-operations-java</connection>
    <developerConnection>scm:git:https://github.com/GoogleCloudPlatform/opentelemetry-operations-java</developerConnection>
    <url>https://github.com/GoogleCloudPlatform/opentelemetry-operations-java</url>
  </scm>
  <dependencies/>
</project>
