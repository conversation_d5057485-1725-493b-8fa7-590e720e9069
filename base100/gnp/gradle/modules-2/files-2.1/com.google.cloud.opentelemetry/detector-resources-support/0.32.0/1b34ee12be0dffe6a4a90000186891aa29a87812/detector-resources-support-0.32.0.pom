<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to <PERSON>rad<PERSON> or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud.opentelemetry</groupId>
  <artifactId>detector-resources-support</artifactId>
  <version>0.32.0</version>
  <name>OpenTelemetry Operations Java</name>
  <description>Support library for Google Cloud Resource Detector</description>
  <url>https://github.com/GoogleCloudPlatform/opentelemetry-operations-java</url>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>com.google.cloud.opentelemetry</id>
      <name>OpenTelemetry Operations Contributors</name>
      <email><EMAIL></email>
      <organization>Google Inc</organization>
      <organizationUrl>https://cloud.google.com/products/operations</organizationUrl>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/GoogleCloudPlatform/opentelemetry-operations-java</connection>
    <developerConnection>scm:git:https://github.com/GoogleCloudPlatform/opentelemetry-operations-java</developerConnection>
    <url>https://github.com/GoogleCloudPlatform/opentelemetry-operations-java</url>
  </scm>
</project>
