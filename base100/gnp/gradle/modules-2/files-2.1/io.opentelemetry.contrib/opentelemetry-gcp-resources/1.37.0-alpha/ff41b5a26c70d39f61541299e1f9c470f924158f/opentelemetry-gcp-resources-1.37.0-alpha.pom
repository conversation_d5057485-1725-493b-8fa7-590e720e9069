<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to <PERSON>radle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.opentelemetry.contrib</groupId>
  <artifactId>opentelemetry-gcp-resources</artifactId>
  <version>1.37.0-alpha</version>
  <name>OpenTelemetry Java Contrib</name>
  <description>OpenTelemetry GCP Resources Support</description>
  <url>https://github.com/open-telemetry/opentelemetry-java-contrib</url>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>opentelemetry</id>
      <name>OpenTelemetry</name>
      <url>https://github.com/open-telemetry/opentelemetry-java-contrib/discussions</url>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:**************:open-telemetry/opentelemetry-java-contrib.git</connection>
    <developerConnection>scm:git:**************:open-telemetry/opentelemetry-java-contrib.git</developerConnection>
    <url>**************:open-telemetry/opentelemetry-java-contrib.git</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>io.opentelemetry</groupId>
      <artifactId>opentelemetry-api</artifactId>
      <version>1.40.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry</groupId>
      <artifactId>opentelemetry-sdk</artifactId>
      <version>1.40.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.cloud.opentelemetry</groupId>
      <artifactId>detector-resources-support</artifactId>
      <version>0.31.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry.semconv</groupId>
      <artifactId>opentelemetry-semconv</artifactId>
      <version>1.25.0-alpha</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>2.17.2</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
</project>
