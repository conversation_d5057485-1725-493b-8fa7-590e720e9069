<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <packaging>pom</packaging>
    <modelVersion>4.0.0</modelVersion>

    <groupId>io.prometheus</groupId>
    <artifactId>parent</artifactId>
    <version>0.16.0</version>

    <name>Prometheus Java Suite</name>
    <url>http://github.com/prometheus/client_java</url>
    <description>
        The Prometheus Java Suite: Client Metrics, Exposition, and Examples
    </description>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <connection>scm:git:**************:prometheus/client_java.git</connection>
        <developerConnection>scm:git:**************:prometheus/client_java.git</developerConnection>
        <url>**************:prometheus/client_java.git</url>
        <tag>parent-0.16.0</tag>
    </scm>

    <developers>
        <developer>
            <id>mtp</id>
            <name>Matt T. Proud</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <modules>
        <module>simpleclient</module>
        <module>simpleclient_common</module>
        <module>simpleclient_caffeine</module>
        <module>simpleclient_dropwizard</module>
        <module>simpleclient_graphite_bridge</module>
        <module>simpleclient_hibernate</module>
        <module>simpleclient_guava</module>
        <module>simpleclient_hotspot</module>
        <module>simpleclient_httpserver</module>
        <module>simpleclient_log4j</module>
        <module>simpleclient_log4j2</module>
        <module>simpleclient_logback</module>
        <module>simpleclient_pushgateway</module>
        <module>simpleclient_servlet</module>
        <module>simpleclient_servlet_common</module>
        <module>simpleclient_servlet_jakarta</module>
        <module>simpleclient_spring_web</module>
        <module>simpleclient_spring_boot</module>
        <module>simpleclient_jetty</module>
        <module>simpleclient_jetty_jdk8</module>
        <module>simpleclient_tracer</module>
        <module>simpleclient_vertx</module>
        <module>simpleclient_vertx4</module>
        <module>simpleclient_bom</module>
        <module>benchmarks</module>
        <module>integration_tests</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <distributionManagement>
        <snapshotRepository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        </snapshotRepository>
        <repository>
            <id>ossrh</id>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        </repository>
    </distributionManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- Add plugin versions for reproducible builds. Versions for default bindings are taken from -->
                <!-- https://github.com/apache/maven/tree/maven-3.8.1/maven-core/src/main/resources/META-INF/plexus -->
                <plugin>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>2.6</version>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.12.4</version>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.4</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>2.7</version>
                </plugin>
                <plugin>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>2.5</version>
                </plugin>
                <plugin>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>3.3</version>
                </plugin>
                <!-- Plugins with groupId org.apache.maven.plugins that are not part of default bindings. -->
                <plugin>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.2.4</version>
                </plugin>
                <plugin>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>2.22.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-release-plugin</artifactId>
                    <version>2.5.3</version>
                </plugin>
                <plugin>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.1.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.2.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>1.4.1</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-plugin-versions</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requirePluginVersions>
                                    <unCheckedPluginList>org.springframework.boot:spring-boot-maven-plugin
                                    </unCheckedPluginList>
                                </requirePluginVersions>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <configuration>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                    <useReleaseProfile>false</useReleaseProfile>
                    <releaseProfiles>release</releaseProfiles>
                    <goals>deploy</goals>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>2.4.0</version>
                <extensions>true</extensions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <docencoding>UTF-8</docencoding>
                    <linksource>true</linksource>
                    <source>8</source>
                    <javadocExecutable>${java.home}/bin/javadoc</javadocExecutable>
                </configuration>
                <executions>
                    <execution>
                        <id>generate-javadoc-site-report</id>
                        <phase>site</phase>
                        <goals>
                            <goal>aggregate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.6</source>
                    <target>1.6</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.10.0</version>
                <configuration>
                    <rulesUri>file://${project.basedir}/version-rules.xml</rulesUri>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <!-- maven-site-plugin 3.3 requires maven-project-info-reports-plugin < 3.0 -->
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>2.9</version>
            </plugin>
            <plugin>
                <artifactId>maven-javadoc-plugin</artifactId>
                <reportSets>
                    <reportSet>
                        <id>aggregate</id>
                        <inherited>false</inherited>
                        <reports>
                            <report>aggregate</report>
                        </reports>
                    </reportSet>
                    <reportSet>
                        <id>default</id>
                        <reports>
                            <report>javadoc</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>

    <profiles>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
