<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <packaging>pom</packaging>
    <modelVersion>4.0.0</modelVersion>

    <groupId>io.prometheus</groupId>
    <artifactId>client_java</artifactId>
    <version>1.3.3</version>

    <name>Prometheus Metrics Library</name>
    <url>http://github.com/prometheus/client_java</url>
    <description>
        The Prometheus Java Metrics Library
    </description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <automatic.module.name>--module-name-need-to-be-overriden--</automatic.module.name>
        <junit-jupiter.version>5.11.3</junit-jupiter.version>
        <otel.instrumentation.version>2.9.0-alpha</otel.instrumentation.version>
        <java.version>8</java.version>
        <jacoco.line-coverage>0.70</jacoco.line-coverage>
        <coverage.skip>false</coverage.skip>
        <warnings>-Werror</warnings>
    </properties>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <connection>scm:git:**************:prometheus/client_java.git</connection>
        <developerConnection>scm:git:**************:prometheus/client_java.git</developerConnection>
        <url>**************:prometheus/client_java.git</url>
        <tag>HEAD</tag>
    </scm>

    <developers>
        <developer>
            <id>fstab</id>
            <name>Fabian Stäber</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>zeitlinger</id>
            <name>Gregor Zeitlinger</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>dhoard</id>
            <name>Doug Hoard</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <modules>
        <module>prometheus-metrics-bom</module>
        <module>prometheus-metrics-core</module>
        <module>prometheus-metrics-config</module>
        <module>prometheus-metrics-model</module>
        <module>prometheus-metrics-tracer</module>
        <module>prometheus-metrics-exposition-formats</module>
        <module>prometheus-metrics-exporter-common</module>
        <module>prometheus-metrics-exporter-servlet-jakarta</module>
        <module>prometheus-metrics-exporter-servlet-javax</module>
        <module>prometheus-metrics-exporter-httpserver</module>
        <module>prometheus-metrics-exporter-opentelemetry</module>
        <module>prometheus-metrics-exporter-pushgateway</module>
        <module>prometheus-metrics-instrumentation-caffeine</module>
        <module>prometheus-metrics-instrumentation-jvm</module>
        <module>prometheus-metrics-instrumentation-dropwizard5</module>
        <module>prometheus-metrics-instrumentation-guava</module>
        <module>prometheus-metrics-simpleclient-bridge</module>
        <module>otel-agent-resources</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>33.3.1-jre</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit-jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <version>${junit-jupiter.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>5.14.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>3.26.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
            <version>2.0.16</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit-pioneer</groupId>
            <artifactId>junit-pioneer</artifactId>
            <version>2.3.0</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <!-- Add plugin versions for reproducible builds. Versions for default bindings are taken from -->
                <!-- https://github.com/apache/maven/tree/maven-3.8.1/maven-core/src/main/resources/META-INF/plexus -->
                <plugin>
                    <artifactId>maven-install-plugin</artifactId>
                    <version>3.1.3</version>
                </plugin>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.3.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.13.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.5.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.4.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>3.1.3</version>
                </plugin>
                <plugin>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>3.4.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>3.21.0</version>
                </plugin>
                <!-- Plugins with groupId org.apache.maven.plugins that are not part of default bindings. -->
                <plugin>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.6.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>3.5.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.11.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>3.2.7</version>
                </plugin>
                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.3.1</version>
                </plugin>
                <plugin>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.5.0</version>
                </plugin>
                <plugin>
                    <groupId>com.diffplug.spotless</groupId>
                    <artifactId>spotless-maven-plugin</artifactId>
                    <version>2.43.0</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>com.diffplug.spotless</groupId>
                <artifactId>spotless-maven-plugin</artifactId>
                <configuration>
                    <java>
                        <googleJavaFormat/>
                    </java>
                </configuration>
                <executions>
                    <execution>
                        <phase>verify</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.12</version>
                <configuration>
                    <skip>${coverage.skip}</skip>
                    <excludes>
                        <exclude>**/generated/**</exclude>
                        <exclude>**/*BlockingRejectedExecutionHandler*</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <rule>
                                    <element>CLASS</element>
                                    <limits>
                                        <limit>
                                            <counter>LINE</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>${jacoco.line-coverage}</minimum>
                                        </limit>
                                    </limits>
                                </rule>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-plugin-versions</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requirePluginVersions>
                                    <unCheckedPluginList>org.springframework.boot:spring-boot-maven-plugin
                                    </unCheckedPluginList>
                                </requirePluginVersions>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>5.1.9</version>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <Automatic-Module-Name>${automatic.module.name}</Automatic-Module-Name>
                    </instructions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <release>${java.version}</release>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <testRelease>17</testRelease>
                    <testSource>17</testSource>
                    <testTarget>17</testTarget>
                    <showWarnings>true</showWarnings>
                    <compilerArgs>
                        <arg>-Xlint:all,-serial,-processing,-options</arg>
                        <arg>${warnings}</arg>
                        <arg>-XDcompilePolicy=simple</arg>
                        <arg>
                            -Xplugin:ErrorProne
                            -Xep:AlmostJavadoc:OFF
                            -Xep:MissingSummary:OFF
                            -Xep:LongDoubleConversion:OFF
                            -Xep:StringSplitter:OFF
                            -XepExcludedPaths:.*/generated/.*
                        </arg>
                    </compilerArgs>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>com.google.errorprone</groupId>
                            <artifactId>error_prone_core</artifactId>
                            <version>2.35.1</version>
                        </path>
                        <!-- Other annotation processors go here.

                        If 'annotationProcessorPaths' is set, processors will no longer be
                        discovered on the regular -classpath; see also 'Using Error Prone
                        together with other annotation processors' below. -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.17.1</version>
                <configuration>
                    <rulesUri>file://${project.basedir}/version-rules.xml</rulesUri>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <!-- maven-site-plugin 3.3 requires maven-project-info-reports-plugin < 3.0 -->
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>3.8.0</version>
            </plugin>
            <plugin>
                <artifactId>maven-javadoc-plugin</artifactId>
                <reportSets>
                    <reportSet>
                        <id>aggregate</id>
                        <inherited>false</inherited>
                        <reports>
                            <report>aggregate</report>
                        </reports>
                    </reportSet>
                    <reportSet>
                        <id>default</id>
                        <reports>
                            <report>javadoc</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>

    <profiles>
        <profile>
            <id>default</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <modules>
                <module>examples</module>
                <module>benchmarks</module>
                <module>integration-tests</module>
            </modules>
        </profile>
        <profile>
            <id>javadoc</id>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <configuration>
                            <encoding>UTF-8</encoding>
                            <docencoding>UTF-8</docencoding>
                            <linksource>true</linksource>
                            <noqualifier>all</noqualifier>
                            <show>public</show>
                            <excludePackageNames>io.prometheus.metrics.expositionformats.generated.*
                            </excludePackageNames>
                            <source>8</source>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                                <configuration>
                                    <gpgArguments>
                                        <arg>--pinentry-mode</arg>
                                        <arg>loopback</arg>
                                    </gpgArguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.sonatype.central</groupId>
                        <artifactId>central-publishing-maven-plugin</artifactId>
                        <version>0.6.0</version>
                        <extensions>true</extensions>
                        <configuration>
                            <publishingServerId>ossrh</publishingServerId>
                            <autoPublish>true</autoPublish>
                            <waitUntil>published</waitUntil>
                        </configuration>
                    </plugin>
                </plugins>
            </build>

            <distributionManagement>
                <snapshotRepository>
                    <id>ossrh</id>
                    <url>https://oss.sonatype.org/content/repositories/snapshots</url>
                </snapshotRepository>
                <repository>
                    <id>ossrh</id>
                    <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
                </repository>
            </distributionManagement>
        </profile>
    </profiles>
</project>
