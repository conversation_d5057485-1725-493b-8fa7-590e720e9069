<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>io.prometheus</groupId>
        <artifactId>client_java</artifactId>
        <version>1.3.6</version>
    </parent>

    <artifactId>prometheus-metrics-bom</artifactId>
    <packaging>pom</packaging>

    <name>Prometheus Metrics BOM</name>
    <description>
        Bill of Materials for the Prometheus Metrics library
    </description>

    <properties>
        <prometheus.metrics.shaded.dependencies.version>1.3.1</prometheus.metrics.shaded.dependencies.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-config</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-exporter-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-exporter-httpserver</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-exporter-opentelemetry</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-exporter-pushgateway</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-exporter-servlet-jakarta</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-exporter-servlet-javax</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-exposition-formats</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-instrumentation-dropwizard</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-instrumentation-dropwizard5</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-instrumentation-caffeine</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-instrumentation-guava</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-instrumentation-jvm</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-simpleclient-bridge</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-tracer</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-tracer-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-tracer-initializer</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-tracer-otel</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>prometheus-metrics-tracer-otel-agent</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
