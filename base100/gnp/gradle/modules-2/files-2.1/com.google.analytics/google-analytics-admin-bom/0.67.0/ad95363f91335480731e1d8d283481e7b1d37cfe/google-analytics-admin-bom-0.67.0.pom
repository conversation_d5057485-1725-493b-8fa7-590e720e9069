<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.analytics</groupId>
  <artifactId>google-analytics-admin-bom</artifactId>
  <version>0.67.0</version><!-- {x-version-update:google-analytics-admin:current} -->
  <packaging>pom</packaging>

  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-pom-parent</artifactId>
    <version>1.51.0</version><!-- {x-version-update:google-cloud-java:current} -->
    <relativePath>../../google-cloud-pom-parent/pom.xml</relativePath>
  </parent>

  <name>Google Analytics Admin BOM</name>
  <description>
    BOM for Analytics Admin
  </description>

  <properties>
    <maven.antrun.skip>true</maven.antrun.skip>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.google.analytics</groupId>
        <artifactId>google-analytics-admin</artifactId>
        <version>0.67.0</version><!-- {x-version-update:google-analytics-admin:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-analytics-admin-v1alpha</artifactId>
        <version>0.67.0</version><!-- {x-version-update:grpc-google-analytics-admin-v1alpha:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>grpc-google-analytics-admin-v1beta</artifactId>
        <version>0.67.0</version><!-- {x-version-update:grpc-google-analytics-admin-v1beta:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-analytics-admin-v1alpha</artifactId>
        <version>0.67.0</version><!-- {x-version-update:proto-google-analytics-admin-v1alpha:current} -->
      </dependency>
      <dependency>
        <groupId>com.google.api.grpc</groupId>
        <artifactId>proto-google-analytics-admin-v1beta</artifactId>
        <version>0.67.0</version><!-- {x-version-update:proto-google-analytics-admin-v1beta:current} -->
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
