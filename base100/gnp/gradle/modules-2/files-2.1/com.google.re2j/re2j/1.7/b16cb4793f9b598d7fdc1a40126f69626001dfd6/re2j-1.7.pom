<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.re2j</groupId>
  <artifactId>re2j</artifactId>
  <version>1.7</version>
  <name>RE2/J</name>
  <description>Linear time regular expressions for Java</description>
  <url>http://github.com/google/re2j</url>
  <licenses>
    <license>
      <name>Go License</name>
      <url>https://golang.org/LICENSE</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <scm>
    <url>https://github.com/google/re2j.git</url>
  </scm>
  <developers>
    <developer>
      <id>dev</id>
      <name>The RE2/J Contributors</name>
      <email><EMAIL></email>
    </developer>
  </developers>
</project>
