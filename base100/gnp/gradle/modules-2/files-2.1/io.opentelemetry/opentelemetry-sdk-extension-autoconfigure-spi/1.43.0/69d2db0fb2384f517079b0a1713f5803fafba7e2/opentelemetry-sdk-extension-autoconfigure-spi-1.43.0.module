{"formatVersion": "1.1", "component": {"group": "io.opentelemetry", "module": "opentelemetry-sdk-extension-autoconfigure-spi", "version": "1.43.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-sdk", "version": {"requires": "1.43.0"}}], "files": [{"name": "opentelemetry-sdk-extension-autoconfigure-spi-1.43.0.jar", "url": "opentelemetry-sdk-extension-autoconfigure-spi-1.43.0.jar", "size": 20568, "sha512": "554894d1052fe5a0f12594fccdf31c1a011dcae86cd2e0c773b4234b1d379948478e60c13a6add442f85ef1c3f4a3414613ff73a123d601325065153faf93473", "sha256": "ae0b08fbe8b3146ff2a8e14cc48af418d6f8f808527d00406b0778748cd07f24", "sha1": "0ef96761d2234ab0973aea432d74ddb6cf4589ca", "md5": "9f405972c54c01730c177461171f9046"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-sdk", "version": {"requires": "1.43.0"}}], "files": [{"name": "opentelemetry-sdk-extension-autoconfigure-spi-1.43.0.jar", "url": "opentelemetry-sdk-extension-autoconfigure-spi-1.43.0.jar", "size": 20568, "sha512": "554894d1052fe5a0f12594fccdf31c1a011dcae86cd2e0c773b4234b1d379948478e60c13a6add442f85ef1c3f4a3414613ff73a123d601325065153faf93473", "sha256": "ae0b08fbe8b3146ff2a8e14cc48af418d6f8f808527d00406b0778748cd07f24", "sha1": "0ef96761d2234ab0973aea432d74ddb6cf4589ca", "md5": "9f405972c54c01730c177461171f9046"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-extension-autoconfigure-spi-1.43.0-javadoc.jar", "url": "opentelemetry-sdk-extension-autoconfigure-spi-1.43.0-javadoc.jar", "size": 131731, "sha512": "4e454256ec80ea0146aff374763e0bf8865568a8213cecf6ae35310c797a7032b08d83fa8b949e3f5a9a28eb1a970625e69f0776d6a9a37601519c05d9d8969e", "sha256": "6255e66a8f19bc798365a3db3f690a8109dfd26fee82d7d53de3c94cde54d06f", "sha1": "e2ead661a63224dde1d4cff839c3d59193f42c97", "md5": "4a477ff8ce523235d1976dd2804706d0"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-extension-autoconfigure-spi-1.43.0-sources.jar", "url": "opentelemetry-sdk-extension-autoconfigure-spi-1.43.0-sources.jar", "size": 22471, "sha512": "706ded6cb23a16f50e8d9434294879d38ee2e76e606329b467a9919d76c909edc5ad2850cd8acc518fa566a6374cfb29f89d1fa48c5601ae3db12158f41a7e73", "sha256": "c92792a6f691b68f513ae3b9d4a1bc97299881b08399f3fb1900e947e5434dca", "sha1": "ac7901d4fe6d6254ef26aa619679fce33c604c0a", "md5": "d287945f020973c1cd8b36e4869d8cdc"}]}]}