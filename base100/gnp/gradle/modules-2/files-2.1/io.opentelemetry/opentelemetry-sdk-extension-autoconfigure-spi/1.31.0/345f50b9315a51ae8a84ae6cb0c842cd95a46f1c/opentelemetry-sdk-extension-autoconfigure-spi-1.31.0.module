{"formatVersion": "1.1", "component": {"group": "io.opentelemetry", "module": "opentelemetry-sdk-extension-autoconfigure-spi", "version": "1.31.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-sdk", "version": {"requires": "1.31.0"}}], "files": [{"name": "opentelemetry-sdk-extension-autoconfigure-spi-1.31.0.jar", "url": "opentelemetry-sdk-extension-autoconfigure-spi-1.31.0.jar", "size": 16910, "sha512": "e419c9863f011f0fd56782f1ea61de483a0fc3d7d5f3feef0afb138b0f6081a9d046eaec6ccfc99c83d5c9eddfc9cef993ca4a127306245ae5e983113fc1af4c", "sha256": "e512ca9b23005bf9b8a147487bbbaaf87a8957e642a3330984214ae0dab550fc", "sha1": "80acc40893fd00b56eee2acd145dbbd560173265", "md5": "ae24dae654d97175c72b752f8c652779"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-sdk", "version": {"requires": "1.31.0"}}], "files": [{"name": "opentelemetry-sdk-extension-autoconfigure-spi-1.31.0.jar", "url": "opentelemetry-sdk-extension-autoconfigure-spi-1.31.0.jar", "size": 16910, "sha512": "e419c9863f011f0fd56782f1ea61de483a0fc3d7d5f3feef0afb138b0f6081a9d046eaec6ccfc99c83d5c9eddfc9cef993ca4a127306245ae5e983113fc1af4c", "sha256": "e512ca9b23005bf9b8a147487bbbaaf87a8957e642a3330984214ae0dab550fc", "sha1": "80acc40893fd00b56eee2acd145dbbd560173265", "md5": "ae24dae654d97175c72b752f8c652779"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-extension-autoconfigure-spi-1.31.0-javadoc.jar", "url": "opentelemetry-sdk-extension-autoconfigure-spi-1.31.0-javadoc.jar", "size": 131064, "sha512": "4689836c11a961a0d434a18c5f82bede8b5f6d64a37daa5d79b266138a10577a7314d2bdd8b5d3fb788ce052ea51572ce74a09a9278a35576b3179864f5bf3ca", "sha256": "12df23d219652bf1e6b36e8c260111c8c7cd1d4601aec31548c66694108dcc23", "sha1": "9ebcac614e043c8cfd7ced935a2764c1ac48353a", "md5": "abc4ecc43e4309446bd0ce9aba7c1660"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-extension-autoconfigure-spi-1.31.0-sources.jar", "url": "opentelemetry-sdk-extension-autoconfigure-spi-1.31.0-sources.jar", "size": 17949, "sha512": "c5be73078d09b0ccba2a5edaedb1757a946d0dd1cf9f2d99e3a8fd63c87c34d4116f80d8c1b7bcdeaab3a651b122f3fcab70c8e6c77e89a010a853db2cf4a654", "sha256": "c7b38859d8467553810852295a6c354ace6739e09e49d5acdc1dc58e35207882", "sha1": "dc847d70d75343322a5ae35c6aedbb115287cca1", "md5": "783ebc9a8c811275183a460c1b327896"}]}]}