{"formatVersion": "1.1", "component": {"group": "io.opentelemetry", "module": "opentelemetry-sdk-common", "version": "1.43.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.43.0"}}], "files": [{"name": "opentelemetry-sdk-common-1.43.0.jar", "url": "opentelemetry-sdk-common-1.43.0.jar", "size": 54519, "sha512": "ff8285d83eef4d1bef97035f525769cfbedda9923bc54c391781ec9139b6367095724b06f69189899e22d1a5d0041a73fc64b781e29a7893f6287b010c9e677e", "sha256": "8d0cc91322e85ea6f71dbf5d35f829808958fd0427e0c43aa3fdd2e94e56a48b", "sha1": "8f2bb94d9d9fe070de698b973c1f1f247a166dcf", "md5": "f3dfce95d526794a3f22d9407fe60af8"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.43.0"}}], "files": [{"name": "opentelemetry-sdk-common-1.43.0.jar", "url": "opentelemetry-sdk-common-1.43.0.jar", "size": 54519, "sha512": "ff8285d83eef4d1bef97035f525769cfbedda9923bc54c391781ec9139b6367095724b06f69189899e22d1a5d0041a73fc64b781e29a7893f6287b010c9e677e", "sha256": "8d0cc91322e85ea6f71dbf5d35f829808958fd0427e0c43aa3fdd2e94e56a48b", "sha1": "8f2bb94d9d9fe070de698b973c1f1f247a166dcf", "md5": "f3dfce95d526794a3f22d9407fe60af8"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-common-1.43.0-javadoc.jar", "url": "opentelemetry-sdk-common-1.43.0-javadoc.jar", "size": 131462, "sha512": "ecc470f3c80e48363992c9fd0e918cbe3a7d118843731ca53dcee860e3759959dffe56bee69a5b8924f868b00c4d5878fbf4d85e9db62e215ed981ba9f93ca0f", "sha256": "05f0b0668b40ec312d8c34f27f3c869621debfe76c0da3112c77dba49fa52c4f", "sha1": "de58b0d857601f9427415742111244d8564b63d9", "md5": "ece39ee9466850bfec14531b71e15185"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-common-1.43.0-sources.jar", "url": "opentelemetry-sdk-common-1.43.0-sources.jar", "size": 37593, "sha512": "d424347c16f3c75172b7cb7e3080b2897d3532074c00d81d72e66d6c995b15380fbb01a00890f623f94b7f2aa4effa8b8182cdeab281f2971ee321b6b7c36c25", "sha256": "e240713669cc161a6898c9c405fdf0903f69ff35abde95827d1f6660df58c0cc", "sha1": "1d4934615bd9ed46d0c1208724fb9d3607682b6c", "md5": "ff4fe7094077a7932fc72748e490a637"}]}]}