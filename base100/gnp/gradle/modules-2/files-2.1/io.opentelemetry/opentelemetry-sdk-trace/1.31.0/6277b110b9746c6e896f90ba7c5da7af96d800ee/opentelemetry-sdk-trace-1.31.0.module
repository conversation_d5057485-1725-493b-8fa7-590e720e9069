{"formatVersion": "1.1", "component": {"group": "io.opentelemetry", "module": "opentelemetry-sdk-trace", "version": "1.31.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.31.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-common", "version": {"requires": "1.31.0"}}], "files": [{"name": "opentelemetry-sdk-trace-1.31.0.jar", "url": "opentelemetry-sdk-trace-1.31.0.jar", "size": 117197, "sha512": "9c3a67e0f502c35770c1561b9e862edac463926f6c505b50a17aca6d4cc852352685b521ec52d89f5a1dbeca5bf32679a0be3529a9c5bf150f80100a1bb9c325", "sha256": "e78062c470400d6d5e0566fd24e1e048c874fa27a555c0d2b36ad0294af85cab", "sha1": "a3941197cfb8ae9eb9e482073480c0c3918b746c", "md5": "395890d991931c195bacd0d97ad0b713"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.31.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-common", "version": {"requires": "1.31.0"}}], "files": [{"name": "opentelemetry-sdk-trace-1.31.0.jar", "url": "opentelemetry-sdk-trace-1.31.0.jar", "size": 117197, "sha512": "9c3a67e0f502c35770c1561b9e862edac463926f6c505b50a17aca6d4cc852352685b521ec52d89f5a1dbeca5bf32679a0be3529a9c5bf150f80100a1bb9c325", "sha256": "e78062c470400d6d5e0566fd24e1e048c874fa27a555c0d2b36ad0294af85cab", "sha1": "a3941197cfb8ae9eb9e482073480c0c3918b746c", "md5": "395890d991931c195bacd0d97ad0b713"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-trace-1.31.0-javadoc.jar", "url": "opentelemetry-sdk-trace-1.31.0-javadoc.jar", "size": 176092, "sha512": "4ee4feb7c2bad9df7fd4f3ece3fa85a2b2481726cea365ee1405b28aafe9d21c40c2f227d02c73cafbb2385bda3815c87576ca2657c0dcbdd592163bb1601ec7", "sha256": "6d8600e09edd09da043cddbaae791f8173f58e947a34ec5e9c43f37a0709517b", "sha1": "b3049e04c9117b8b271e68a8686608e766bc97e8", "md5": "dfb15653b3079d73d33a5efa2c37e1fe"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-trace-1.31.0-sources.jar", "url": "opentelemetry-sdk-trace-1.31.0-sources.jar", "size": 55088, "sha512": "3966f0cbcf27dafd8e435a406281d78009c2d6feeb2a4cc67f6e8ff4206a724c05ab130682822f30a32008d9e2c5101fecb46e0be681ea27a21f4f42dbc312c3", "sha256": "f1759c9c10747b126e5746403f934e6ef695440b91dccfa040a2b4098de02602", "sha1": "c7aaef9bfe15690b48aa8c6014b5da31cb8e23f0", "md5": "e0dfdf7eec5874d267d3f1f52898aa17"}]}]}