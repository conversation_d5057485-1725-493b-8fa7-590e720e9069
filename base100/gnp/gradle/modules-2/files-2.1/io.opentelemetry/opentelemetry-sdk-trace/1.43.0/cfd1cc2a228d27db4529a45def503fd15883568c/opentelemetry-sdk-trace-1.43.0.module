{"formatVersion": "1.1", "component": {"group": "io.opentelemetry", "module": "opentelemetry-sdk-trace", "version": "1.43.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.43.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-common", "version": {"requires": "1.43.0"}}], "files": [{"name": "opentelemetry-sdk-trace-1.43.0.jar", "url": "opentelemetry-sdk-trace-1.43.0.jar", "size": 130073, "sha512": "091efd3ba8d4cb87928856514d0467fb0ff96caaae97fd3197404c4bfebff6170fae34594d372a8d41e74dff6b60c088fe71732822e91ca4f9b71c960ecb3dbf", "sha256": "7e28072cf33eaa7d5e5f3ab2f8b2e51e54d24e0fe3dae3482769b0f6ff0dfcfc", "sha1": "69619c6433ebf0fe5196f7c3fe18bd543279b451", "md5": "0065f9c7a5d78a32ad88134c1d5d1628"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api-incubator", "version": {"requires": "1.43.0-alpha"}}, {"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.43.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-common", "version": {"requires": "1.43.0"}}], "files": [{"name": "opentelemetry-sdk-trace-1.43.0.jar", "url": "opentelemetry-sdk-trace-1.43.0.jar", "size": 130073, "sha512": "091efd3ba8d4cb87928856514d0467fb0ff96caaae97fd3197404c4bfebff6170fae34594d372a8d41e74dff6b60c088fe71732822e91ca4f9b71c960ecb3dbf", "sha256": "7e28072cf33eaa7d5e5f3ab2f8b2e51e54d24e0fe3dae3482769b0f6ff0dfcfc", "sha1": "69619c6433ebf0fe5196f7c3fe18bd543279b451", "md5": "0065f9c7a5d78a32ad88134c1d5d1628"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-trace-1.43.0-javadoc.jar", "url": "opentelemetry-sdk-trace-1.43.0-javadoc.jar", "size": 179419, "sha512": "6cf223ea6f3aa27f00baecd82ac799eda4afdaed026462f2531f741fa14d4f6691426d278e7829a853bf8269219dd1956c42c2f197d12b3dd2599bb609389b6c", "sha256": "d1e38c7aacfec903be8c55b20382f5d191597a0ba11aa64afd0a16049f6626ac", "sha1": "30b3a9aceea3e6cea7567c620e780b480ccbb4e4", "md5": "6ea82dd86238ec7479279f08a5e85008"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-trace-1.43.0-sources.jar", "url": "opentelemetry-sdk-trace-1.43.0-sources.jar", "size": 60889, "sha512": "1d622de28cf69d3ecddda9de22c49e5d3b4d76cce3b5c8b1684d85d21cf53d0057e5d6d006848bbd2597bf2b2c175b3dbd628fe344a5dcb1b3ae16234a7fa1ed", "sha256": "b75d547249b04ad5163c17253018159f3f40a99d6d89719a82d11eb3d64588f1", "sha1": "deeb2c9dc2f9f1770d3a1ff6de0f1b2b3f0ae29a", "md5": "efe2ba800fb182bc65e2b018647fa150"}]}]}