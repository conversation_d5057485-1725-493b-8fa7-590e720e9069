{"formatVersion": "1.1", "component": {"group": "io.opentelemetry", "module": "opentelemetry-api", "version": "1.43.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-context", "version": {"requires": "1.43.0"}}], "files": [{"name": "opentelemetry-api-1.43.0.jar", "url": "opentelemetry-api-1.43.0.jar", "size": 157855, "sha512": "1745b0460ca3d69101b389defbde908b48b826d453a6c7c267ee937b332d49d1949f01da6950f0a694ad329f6f6fe928817451c110520c7ac901bc5f80003b54", "sha256": "4d08915aa1590bd64b3fced39bae06489e3f597c4bf7fa12f413e13c5a98be39", "sha1": "ce6889e709baac891833dc1c718702661650b3b3", "md5": "7ddd9cf428685da262bd31586ca4d07b"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-context", "version": {"requires": "1.43.0"}}], "files": [{"name": "opentelemetry-api-1.43.0.jar", "url": "opentelemetry-api-1.43.0.jar", "size": 157855, "sha512": "1745b0460ca3d69101b389defbde908b48b826d453a6c7c267ee937b332d49d1949f01da6950f0a694ad329f6f6fe928817451c110520c7ac901bc5f80003b54", "sha256": "4d08915aa1590bd64b3fced39bae06489e3f597c4bf7fa12f413e13c5a98be39", "sha1": "ce6889e709baac891833dc1c718702661650b3b3", "md5": "7ddd9cf428685da262bd31586ca4d07b"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-api-1.43.0-javadoc.jar", "url": "opentelemetry-api-1.43.0-javadoc.jar", "size": 308260, "sha512": "e7a6672937390ac8c2a6553c5947257594a8757f0e9a794830c2cd87277b0aedff12b66659e2b36a6076d6bfe014849af3efe4acd9b7e2a54097fb9ffd313822", "sha256": "1c8b7ad7ab1bd6014b2c4935794e5c409c77a3a5c8c405433c9c2247e056d175", "sha1": "8234fe2a05f61657c3532dcd71e7062e374f3c3c", "md5": "22e87a6729a35091af7a8633c6ee8b59"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-api-1.43.0-sources.jar", "url": "opentelemetry-api-1.43.0-sources.jar", "size": 120741, "sha512": "2841f6e8a472bc9b0548c893d194581f159a4b29b0e7bb2b9abad4cbee1fc1bc57476c7349700db664ac55742a4497cb69bbff13bbd4420e16678f2874f10422", "sha256": "e8d0fbee6a7a19408e776bda9caecb2a4adeedf64cc196cfefd977837eb27300", "sha1": "c615cdee488fd8a80064eb40a46df4100885dc48", "md5": "8fe2253b5bbecc8eb8ee8622be7aaccc"}]}]}