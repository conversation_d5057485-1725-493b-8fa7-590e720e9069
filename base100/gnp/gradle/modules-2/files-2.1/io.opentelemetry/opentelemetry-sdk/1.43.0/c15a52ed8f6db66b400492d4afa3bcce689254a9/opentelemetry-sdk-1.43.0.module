{"formatVersion": "1.1", "component": {"group": "io.opentelemetry", "module": "opentelemetry-sdk", "version": "1.43.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.43.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-common", "version": {"requires": "1.43.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-trace", "version": {"requires": "1.43.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-metrics", "version": {"requires": "1.43.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-logs", "version": {"requires": "1.43.0"}}], "files": [{"name": "opentelemetry-sdk-1.43.0.jar", "url": "opentelemetry-sdk-1.43.0.jar", "size": 6770, "sha512": "0e25f0f1e4d2d616d8a3ef323ad4a9a45c1d71486fc0c18e30136b6d3d8047fcc86a41cfe41e778a88753c975ca496966dac0b7fa9b838c47526c853cd05036e", "sha256": "99fd821997190e9fa1a6f6820b8cedd8e38297ab727b3b98db4d86ab518c0155", "sha1": "5e4e695beb5400c1866dcf800f08ca8307405b3b", "md5": "7ea5863e06619abdd30054a57e150ca0"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.43.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-common", "version": {"requires": "1.43.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-trace", "version": {"requires": "1.43.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-metrics", "version": {"requires": "1.43.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-logs", "version": {"requires": "1.43.0"}}], "files": [{"name": "opentelemetry-sdk-1.43.0.jar", "url": "opentelemetry-sdk-1.43.0.jar", "size": 6770, "sha512": "0e25f0f1e4d2d616d8a3ef323ad4a9a45c1d71486fc0c18e30136b6d3d8047fcc86a41cfe41e778a88753c975ca496966dac0b7fa9b838c47526c853cd05036e", "sha256": "99fd821997190e9fa1a6f6820b8cedd8e38297ab727b3b98db4d86ab518c0155", "sha1": "5e4e695beb5400c1866dcf800f08ca8307405b3b", "md5": "7ea5863e06619abdd30054a57e150ca0"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-1.43.0-javadoc.jar", "url": "opentelemetry-sdk-1.43.0-javadoc.jar", "size": 86118, "sha512": "8093dd3ce15ddda5d0c3e777b90ec7643c91eded6e7558ef567d245e622804578879c681cf487052fd756b69472a6535b2b1dd997affac384290e001034462e5", "sha256": "41c450d18165217a64df637466aec61ab1cba8a2bf67513f9a4cfdec86a9b852", "sha1": "fdfa1a2aeb1bfa7f3320a45ed85d96e8b368190d", "md5": "828bcdb0d11a06ae1d717b41c697849a"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-1.43.0-sources.jar", "url": "opentelemetry-sdk-1.43.0-sources.jar", "size": 3895, "sha512": "fb77e827534161866a48c8509b4412797803cd04829ff72ae2b36fa0e2455fe41d34c9c7473ce487387800b57c055b6e42bdee0bbef9e6d10da1e8a255763e12", "sha256": "d798a21f2338674cd9dc7249d67963f7328b9a0a702c91f65c9d20b4b1eff664", "sha1": "3b69595a6e38a7765c75850790d4ed596a28c72a", "md5": "c9b3454a0e7f32c5d4ad7dc8a4984d5f"}]}]}