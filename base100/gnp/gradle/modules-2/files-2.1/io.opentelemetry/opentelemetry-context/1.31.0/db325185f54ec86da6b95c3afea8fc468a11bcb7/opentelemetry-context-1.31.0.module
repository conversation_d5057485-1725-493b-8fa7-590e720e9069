{"formatVersion": "1.1", "component": {"group": "io.opentelemetry", "module": "opentelemetry-context", "version": "1.31.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "opentelemetry-context-1.31.0.jar", "url": "opentelemetry-context-1.31.0.jar", "size": 47185, "sha512": "87b67879176b0942ce29a3db9514b599289a8f0ebacca5846a954a1232135982a35717d16605593d7855adaae206b36e1376d9b495c1711cdc8a6430c9f61d88", "sha256": "664896a5c34bcda20c95c8f45198a95e8f97a1cd5e5c2923978f42dddada787d", "sha1": "b8004737f7a970124e36ac71fde8eb88423e8cee", "md5": "17ba793f58aa9331f1e3b2a4ad1d11d1"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-context-1.31.0.jar", "url": "opentelemetry-context-1.31.0.jar", "size": 47185, "sha512": "87b67879176b0942ce29a3db9514b599289a8f0ebacca5846a954a1232135982a35717d16605593d7855adaae206b36e1376d9b495c1711cdc8a6430c9f61d88", "sha256": "664896a5c34bcda20c95c8f45198a95e8f97a1cd5e5c2923978f42dddada787d", "sha1": "b8004737f7a970124e36ac71fde8eb88423e8cee", "md5": "17ba793f58aa9331f1e3b2a4ad1d11d1"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-context-1.31.0-javadoc.jar", "url": "opentelemetry-context-1.31.0-javadoc.jar", "size": 120390, "sha512": "ab75331658c6066af32cce84cbc86dd02f7a710737e94e6d42ea8ef94bf220990eb36b236c4d2b7c352a7c7376f28a5fb5f53ebed178ddcf24dea4cf694f7633", "sha256": "9c560e7747b0522495f1d3e37ba103fae08cfc5ff5fac84f86aee02bef183006", "sha1": "139ac1800578cef756c8d7b23b55933f7cad2184", "md5": "d73c09b1b9b4f887d5660ed0b7f04309"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-context-1.31.0-sources.jar", "url": "opentelemetry-context-1.31.0-sources.jar", "size": 37251, "sha512": "b10ee788df09434a6ae59b17f33e767ffa33513ac24fe7ccdc44a67fcc74c85d36ab5d6e270114b14a6c3cd561b16395b0cc5903e75cc44708cd87421dab5310", "sha256": "de37eb55d2d3dd57ac909a4d64d4114a22d4c1e2da615a4b364e3003a114fd86", "sha1": "8032510853ed3ddac591fb60a3542f8fe5ed254a", "md5": "e9dbf9efe76d459c9909f4a4ae14695e"}]}]}