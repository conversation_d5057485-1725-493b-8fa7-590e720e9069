{"formatVersion": "1.1", "component": {"group": "io.opentelemetry", "module": "opentelemetry-context", "version": "1.43.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "opentelemetry-context-1.43.0.jar", "url": "opentelemetry-context-1.43.0.jar", "size": 48315, "sha512": "9beea23c844fc96f2f9fc65ad97aec2da31c5c3e8d1bdf792aa9929c29b6ebd4d6da5a61814c09f4c584a15ad5515b38d721f77c4cd19cd6f5b490b4020247a9", "sha256": "83d54bed8a7aa74a8648d43974c743f470c9ceab3dea57f26a938667a2dc0160", "sha1": "473c7a67d2d587f7faefe7b6d9c00743e82819ee", "md5": "127395085d1eadd7994273973b0af9a1"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-context-1.43.0.jar", "url": "opentelemetry-context-1.43.0.jar", "size": 48315, "sha512": "9beea23c844fc96f2f9fc65ad97aec2da31c5c3e8d1bdf792aa9929c29b6ebd4d6da5a61814c09f4c584a15ad5515b38d721f77c4cd19cd6f5b490b4020247a9", "sha256": "83d54bed8a7aa74a8648d43974c743f470c9ceab3dea57f26a938667a2dc0160", "sha1": "473c7a67d2d587f7faefe7b6d9c00743e82819ee", "md5": "127395085d1eadd7994273973b0af9a1"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-context-1.43.0-javadoc.jar", "url": "opentelemetry-context-1.43.0-javadoc.jar", "size": 120744, "sha512": "565472a1f1a7f0fae2570500e9b5eddf6e756db95eaa94069c4e5c01bc12f02718b510fd87c7ae58a183079f835b6e2e5a31aa2a6a4bfce14c69a61032083db8", "sha256": "2e522242c29fc898bcb5d818012863753bd9c056bc2bb64f10693c6bd992fe90", "sha1": "7646f7b3323ea144b6e31b8122b8a5362f2a79b4", "md5": "9df623bf561c716f360bf1f7885a09d0"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-context-1.43.0-sources.jar", "url": "opentelemetry-context-1.43.0-sources.jar", "size": 38166, "sha512": "556758a6718a839433fe2d185b012a902770fdea031a07447b26dbb92d5b06931c36a2b642214f90c63b1325efdd98e7cb16753ea4860d28c784df7fe1fb7862", "sha256": "6cbaf1ff211bd61fd5ac2dd5762e18db56913035c43c963c3863eeff2bb25888", "sha1": "b274add5c147164ecb2ee60efb8ec6e5e6c6a67e", "md5": "cafb34097313802027d5121b95646ecd"}]}]}