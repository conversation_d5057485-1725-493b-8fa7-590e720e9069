{"formatVersion": "1.1", "component": {"group": "io.opentelemetry", "module": "opentelemetry-sdk-logs", "version": "1.31.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.31.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-common", "version": {"requires": "1.31.0"}}], "files": [{"name": "opentelemetry-sdk-logs-1.31.0.jar", "url": "opentelemetry-sdk-logs-1.31.0.jar", "size": 47839, "sha512": "fc0643963f40065cf0b191030efded420def61fff387150234352605383fa08856fc3ba08833864c6c603b50b6a73085befe288e1835192996fedbf489d6ec05", "sha256": "dab93a074f5a8eae03a7c4984aa90ba0d3f2cc8b015e061b585d820c3d0151d2", "sha1": "a63a203d3dc6f8875f8c26b9e3b522dc9a3f6280", "md5": "418e9f799c99be3fb591b3cf739d0ed8"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api-events", "version": {"requires": "1.31.0-alpha"}}, {"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.31.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-common", "version": {"requires": "1.31.0"}}], "files": [{"name": "opentelemetry-sdk-logs-1.31.0.jar", "url": "opentelemetry-sdk-logs-1.31.0.jar", "size": 47839, "sha512": "fc0643963f40065cf0b191030efded420def61fff387150234352605383fa08856fc3ba08833864c6c603b50b6a73085befe288e1835192996fedbf489d6ec05", "sha256": "dab93a074f5a8eae03a7c4984aa90ba0d3f2cc8b015e061b585d820c3d0151d2", "sha1": "a63a203d3dc6f8875f8c26b9e3b522dc9a3f6280", "md5": "418e9f799c99be3fb591b3cf739d0ed8"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-logs-1.31.0-javadoc.jar", "url": "opentelemetry-sdk-logs-1.31.0-javadoc.jar", "size": 133901, "sha512": "9ad341d08d2975ecdf8e86ca4e3fd6fb802974de185c4c91f9071d2a9b2609bd1e91e016f92abf27cf0b03a3b102497953fda9950743df826748aad9d8137538", "sha256": "2c88de50eea6b53193235d5f5d8b0d12ca7df02a391ec6d3e808c4ebeeec6b45", "sha1": "4bf9e10c15b9c3e1962384d399b01dcaa24e92da", "md5": "302a8d87b67841856aee3b893b398eb7"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-logs-1.31.0-sources.jar", "url": "opentelemetry-sdk-logs-1.31.0-sources.jar", "size": 27767, "sha512": "ff6fb266e41f7e3c8cd057a61405e41b2b985e7783e2dab78b59497889e63bf143f44175832a8b7a6db453c46160691f894479a039d85afc007550ae8228f41c", "sha256": "004041f63d33dcb6eb9c579c78d7ca4f99a3e4371ab42262f81da53029c34fcf", "sha1": "bac2778497f682c41a2cf02bfbdfb1a6f97e4618", "md5": "6c10fd91e0cdfdca870bc8ca2d19dd56"}]}]}