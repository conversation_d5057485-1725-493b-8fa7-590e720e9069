{"formatVersion": "1.1", "component": {"group": "io.opentelemetry", "module": "opentelemetry-sdk-logs", "version": "1.43.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.43.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-common", "version": {"requires": "1.43.0"}}], "files": [{"name": "opentelemetry-sdk-logs-1.43.0.jar", "url": "opentelemetry-sdk-logs-1.43.0.jar", "size": 54133, "sha512": "2a49333b2ef58c59926a4bd965717373662e0cd46da2dcd5335fced6a1150ef8526cfeb5d997faa6344072b8df6fc47a37e85fd71ff7757d8a547e552f17b9c8", "sha256": "5465297bac529a32be7c3e37bc854f1f683df967551cc092397deaa2596f4462", "sha1": "e613c8ecf1efc1a275c7fe744773035df954c291", "md5": "9e2f353b119f5ce3efe70efa1de0feae"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api-incubator", "version": {"requires": "1.43.0-alpha"}}, {"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.43.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-common", "version": {"requires": "1.43.0"}}], "files": [{"name": "opentelemetry-sdk-logs-1.43.0.jar", "url": "opentelemetry-sdk-logs-1.43.0.jar", "size": 54133, "sha512": "2a49333b2ef58c59926a4bd965717373662e0cd46da2dcd5335fced6a1150ef8526cfeb5d997faa6344072b8df6fc47a37e85fd71ff7757d8a547e552f17b9c8", "sha256": "5465297bac529a32be7c3e37bc854f1f683df967551cc092397deaa2596f4462", "sha1": "e613c8ecf1efc1a275c7fe744773035df954c291", "md5": "9e2f353b119f5ce3efe70efa1de0feae"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-logs-1.43.0-javadoc.jar", "url": "opentelemetry-sdk-logs-1.43.0-javadoc.jar", "size": 136753, "sha512": "24e6b1d8b48e4c388b5bf81a73a5cf48aded11d068b619f60cb7c32ec923508ff0f3c8302e9772ee42693669e0051ada3bbdeef194be07631e686098338586bb", "sha256": "f3dfcc1ea53428d4ba87d113137d0feb7795cd25f32dafb9eb308f79a850a78e", "sha1": "dafa1cd808d577bec5745b15d38d66742f7246cc", "md5": "b9f14228a4fca781516ae55d6c5b5c72"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-logs-1.43.0-sources.jar", "url": "opentelemetry-sdk-logs-1.43.0-sources.jar", "size": 31847, "sha512": "8c2ff89582b578080da76c7b1bbf5955ef676db59958b4c51ad8c9cdce63d7544087817bd01d33d42e25bfea577a1a84d13315488787f65fb4340d46ff553258", "sha256": "234fb21e31806e054c53044cf97d82786390b3634cc94fd42b4fe5c12d86a553", "sha1": "a2627d29777e439c7f44bbe4c5610b517e8c5454", "md5": "54040a067dba21e8182aab6a05a53217"}]}]}