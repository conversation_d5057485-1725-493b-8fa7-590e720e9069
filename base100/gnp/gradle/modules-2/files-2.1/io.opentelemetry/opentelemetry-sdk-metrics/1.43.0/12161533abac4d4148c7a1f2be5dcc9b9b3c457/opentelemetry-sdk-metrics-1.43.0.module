{"formatVersion": "1.1", "component": {"group": "io.opentelemetry", "module": "opentelemetry-sdk-metrics", "version": "1.43.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10.2"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.43.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-common", "version": {"requires": "1.43.0"}}], "files": [{"name": "opentelemetry-sdk-metrics-1.43.0.jar", "url": "opentelemetry-sdk-metrics-1.43.0.jar", "size": 315064, "sha512": "f3a844f95fe60f09da95d48b7385c4b5a92d7f5e14273127698c27505464fc8a420c7d269d579ec81efcb4bd6156b62115e96a8ffef7726004b1abe53e4dcefb", "sha256": "b3cd4906157c559005d6890441d367730c337ad4d860adeee7ceda1106cfc245", "sha1": "ca508a02ef7bb96678d054de264fcc0bf41e729c", "md5": "ca7af717fac76561a06f881c9e1c2d71"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api-incubator", "version": {"requires": "1.43.0-alpha"}}, {"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.43.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-common", "version": {"requires": "1.43.0"}}], "files": [{"name": "opentelemetry-sdk-metrics-1.43.0.jar", "url": "opentelemetry-sdk-metrics-1.43.0.jar", "size": 315064, "sha512": "f3a844f95fe60f09da95d48b7385c4b5a92d7f5e14273127698c27505464fc8a420c7d269d579ec81efcb4bd6156b62115e96a8ffef7726004b1abe53e4dcefb", "sha256": "b3cd4906157c559005d6890441d367730c337ad4d860adeee7ceda1106cfc245", "sha1": "ca508a02ef7bb96678d054de264fcc0bf41e729c", "md5": "ca7af717fac76561a06f881c9e1c2d71"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-metrics-1.43.0-javadoc.jar", "url": "opentelemetry-sdk-metrics-1.43.0-javadoc.jar", "size": 205714, "sha512": "8a21c40ba74c9156920caf7a97e8dcdc196999a184cd177a8ea7eb4a24ff83becc02852f64b112f7490d6a2fb2404b9d2abe3424fbf482d1021a394ec3cecd5a", "sha256": "b41d34b9a7759c74b1f7ae157ed97a105c1929f201259822f975baeb2703b63a", "sha1": "c70d9e168bea5fbbd79fe92dee72f31aeeff315d", "md5": "464205c2b85aee98787fa3163348fb69"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-sdk-metrics-1.43.0-sources.jar", "url": "opentelemetry-sdk-metrics-1.43.0-sources.jar", "size": 175648, "sha512": "9bde4c36f6fab7ef9e585fd2ae3539eb0b8d51373fcfc1a0957b849bcbbef8f4032be8f8018f183831d6a1e6c12ded85ebab92626b9338c2c6d851bb32572c36", "sha256": "d7523f6dbeb161fd4f0dabf1c6e414ab3eac153479acb95547155f5b16f8e557", "sha1": "c51b302d36d063b78b04115b9f5b31c6f64732f2", "md5": "8ec97fc0cb0703e62fe6b7bf7ec49c3f"}]}]}