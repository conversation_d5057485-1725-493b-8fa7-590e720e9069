<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.ibm.db2.jcc</groupId>
  <artifactId>db2jcc</artifactId>
  <version>db2jcc4</version>
  <name>IBM Data Server Driver for JDBC and SQLJ</name>
  <description>IBM Data Server Driver for JDBC and SQLJ is a pure-Java driver (Type 4) that supports the JDBC 4 specification. You can use this JDBC driver for Java applications that access the Db2® LUW database server.</description>
  <licenses>
    <license>
      <name>International Program License Agreement (IPLA)</name>
      <url>http://www14.software.ibm.com/cgi-bin/weblap/lap.pl?li_formnum=L-XHUG-B3WNQS&amp;title=IBM%20Data%20Server%20Driver%20for%20JDBC%20and%20SQLJ%20v4.25%20(********)</url>
      <distribution>repo</distribution>
      <comments>You can use this JDBC driver for Java applications that access the Db2 LUW database server</comments>
    </license>
  </licenses>
</project>