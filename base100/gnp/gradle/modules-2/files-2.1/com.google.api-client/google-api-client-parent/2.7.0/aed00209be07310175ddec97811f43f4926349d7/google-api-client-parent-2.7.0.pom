<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.api-client</groupId>
  <artifactId>google-api-client-parent</artifactId>
  <version>2.7.0</version><!-- {x-version-update:google-api-client:current} -->
  <packaging>pom</packaging>
  <name>Parent for the Google API Client Library for Java</name>
  <description>The Google APIs Client Library for Java is a Java client library
    for accessing any HTTP-based API on the web</description>
  <url>https://github.com/googleapis/google-api-java-client</url>

  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/googleapis/google-api-java-client/issues</url>
  </issueManagement>

  <inceptionYear>2010</inceptionYear>

  <developers>
    <developer>
      <id>chingor</id>
      <name>Jeff Ching</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>neenushaji</id>
      <name>Neenu Shaji</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>

  <scm>
    <connection>scm:git:https://github.com/googleapis/google-api-java-client.git</connection>
    <developerConnection>scm:git:ssh:**************:googleapis/google-api-java-client.git</developerConnection>
    <url>https://github.com/googleapis/google-api-java-client</url>
  </scm>

  <organization>
    <name>Google</name>
    <url>http://www.google.com/</url>
  </organization>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <!--
    If you add a new module, make sure to also add it in the following places:
    * below in the dependencyManagement and maven-javadoc-plugin sections
    * google-api-client-assembly/classpath-include
    * google-api-client-assembly/pom.xml
    * google-api-client-assembly/readme.html
    * google-api-client-assembly/dependencies/<name>-dependencies.html
        (use mvn project-info-reports:dependencies and copy from
        google-api-client-<name>/target/site/dependencies.html)
    * google-api-client-assembly/android-properties/*.properties
  -->
  <modules>
    <module>google-api-client</module>
    <module>google-api-client-servlet</module>
    <module>google-api-client-android</module>
    <module>google-api-client-appengine</module>
    <module>google-api-client-assembly</module>
    <module>google-api-client-bom</module>
    <module>google-api-client-gson</module>
    <module>google-api-client-jackson2</module>
    <module>google-api-client-protobuf</module>
    <module>google-api-client-xml</module>
    <module>google-api-client-apache-v5</module>
  </modules>

  <pluginRepositories>
    <pluginRepository>
      <releases>
        <updatePolicy>never</updatePolicy>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>central</id>
      <name>Central Repository</name>
      <url>https://repo.maven.apache.org/maven2</url>
    </pluginRepository>
  </pluginRepositories>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.13.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>4.11.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-api-1.0-sdk</artifactId>
        <version>${project.appengine.version}</version>
      </dependency>
      <dependency>
        <groupId>xpp3</groupId>
        <artifactId>xpp3</artifactId>
        <version>${project.xpp3.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${project.guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.findbugs</groupId>
        <artifactId>jsr305</artifactId>
        <version>${project.jsr305.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.servlet</groupId>
        <artifactId>jakarta.servlet-api</artifactId>
        <version>${project.jakarta-servlet-api.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.jdo</groupId>
        <artifactId>jdo2-api</artifactId>
        <version>${project.jdo2-api.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>servlet-api</artifactId>
        <version>${project.servlet-api.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore</artifactId>
        <version>${project.httpcore.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>${project.httpclient.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-bom</artifactId>
        <version>${project.http.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.oauth-client</groupId>
        <artifactId>google-oauth-client-bom</artifactId>
        <version>${project.oauth.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.auth</groupId>
        <artifactId>google-auth-library-bom</artifactId>
        <version>${project.auth.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client-appengine</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client-servlet</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client-android</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client-gson</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client-jackson2</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client-protobuf</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.api-client</groupId>
        <artifactId>google-api-client-xml</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>${project.commons-codec.version}</version>
      </dependency>

      <!-- See README.md about installing this jar -->
      <dependency>
        <groupId>com.google.android.google-play-services</groupId>
        <artifactId>google-play-services</artifactId>
        <version>1</version>
      </dependency>

      <dependency>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-java</artifactId>
        <version>${project.protobuf-java.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
        <version>${gson.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5</artifactId>
        <version>${project.httpclient5.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5</artifactId>
        <version>${project.httpcore5.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-apache-v5</artifactId>
        <version>${project.http.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <!-- This is the parent, so only define pluginManagement, not plugins. -->
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.sonatype.plugins</groupId>
          <artifactId>nexus-staging-maven-plugin</artifactId>
          <version>1.6.13</version>
          <extensions>true</extensions>
          <configuration>
            <serverId>ossrh</serverId>
            <nexusUrl>https://google.oss.sonatype.org/</nexusUrl>
            <autoReleaseAfterClose>${deploy.autorelease}</autoReleaseAfterClose>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>3.7.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.13.0</version>
          <configuration>
            <source>1.7</source>
            <target>1.7</target>
          </configuration>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.2.1</version>
          <executions>
            <execution>
              <id>attach-sources</id>
              <goals>
                <goal>jar-no-fork</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>3.6.0</version>
          <executions>
            <execution>
              <id>attach-javadocs</id>
              <goals>
                <goal>jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.4.1</version>
          <executions>
            <execution>
              <goals>
                <goal>test-jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>3.2.5</version>
          <configuration>
            <argLine>-Xmx1024m</argLine>
            <reportNameSuffix>sponge_log</reportNameSuffix>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>3.3.1</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>findbugs-maven-plugin</artifactId>
          <version>3.0.5</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>2.8</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.23</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>3.4.5</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.12.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>3.3.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>3.6.1</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.4.1</version><executions>
        <execution>
          <id>enforce-maven</id>
          <goals>
            <goal>enforce</goal>
          </goals>
          <configuration>
            <rules>
              <requireMavenVersion>
                <version>[3.5.4,4.0.0)</version>
              </requireMavenVersion>
            </rules>
          </configuration>
        </execution>
      </executions>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <doclint>none</doclint>
          <source>7</source>
        </configuration>
        <executions>
          <execution>
            <id>aggregate</id>
            <goals>
              <goal>aggregate</goal>
            </goals>
            <phase>site</phase>
            <configuration>
              <links>
                <link>http://download.oracle.com/javase/7/docs/api/</link>
                <link>http://cloud.google.com/appengine/docs/java/javadoc</link>
                <link>https://googleapis.dev/java/google-http-client/${project.http.version}/</link>
                <link>https://googleapis.dev/java/google-oauth-client/${project.oauth.version}/</link>
              </links>
              <doctitle>Google API Client Library for Java ${project.version}</doctitle>
              <overview>${basedir}/overview.html</overview>
              <groups>
                <group>
                  <title>google-api-client</title>
                  <packages>com.google.api.client.googleapis*</packages>
                </group>
                <group>
                  <title>google-api-client-android</title>
                  <packages>com.google.api.client.googleapis.extensions.android.*</packages>
                </group>
                <group>
                  <title>google-api-client-appengine</title>
                  <packages>com.google.api.client.googleapis.extensions.appengine*</packages>
                </group>
                <group>
                  <title>google-api-client-gson</title>
                  <packages>com.google.api.client.googleapis.notifications.json.gson.*</packages>
                </group>
                <group>
                  <title>google-api-client-jackson2</title>
                  <packages>com.google.api.client.googleapis.notifications.json.jackson2.*</packages>
                </group>
                <group>
                  <title>google-api-client-protobuf</title>
                  <packages>com.google.api.client.googleapis.services.protobuf*:com.google.api.client.googleapis.testing.services.protobuf*</packages>
                </group>
                <group>
                  <title>google-api-client-servlet</title>
                  <packages>com.google.api.client.googleapis.extensions.servlet*</packages>
                </group>
                <group>
                  <title>google-api-client-xml</title>
                  <packages>com.google.api.client.googleapis.xml.*</packages>
                </group>
              </groups>
              <windowtitle>google-api-java-client ${project.version}</windowtitle>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <configuration>
          <excludeFilterFile>${basedir}/../findbugs-exclude.xml</excludeFilterFile>
          <plugins>
            <plugin>
              <groupId>com.google.http-client</groupId>
              <artifactId>google-http-client-findbugs</artifactId>
              <version>${project.http.version}</version>
            </plugin>
          </plugins>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <configuration>
          <comparisonVersion>1.27.0</comparisonVersion>
          <ignoredDifferencesFile>${basedir}/../clirr-ignored-differences.xml</ignoredDifferencesFile>
          <logResults>true</logResults>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <configuration>
          <signature>
            <groupId>org.codehaus.mojo.signature</groupId>
            <artifactId>java17</artifactId>
            <version>1.0</version>
          </signature>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>com.coveo</groupId>
        <artifactId>fmt-maven-plugin</artifactId>
        <version>2.13</version>
        <configuration>
          <style>google</style>
          <verbose>true</verbose>
        </configuration>
        <dependencies>
          <dependency>
            <groupId>com.google.googlejavaformat</groupId>
            <artifactId>google-java-format</artifactId>
            <version>1.13.0</version>
          </dependency>
        </dependencies>
      </plugin>
    </plugins>
  </build>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.http.version>1.45.0</project.http.version>
    <project.httpcore.version>4.4.16</project.httpcore.version>
    <project.httpclient.version>4.5.14</project.httpclient.version>
    <project.httpcore5.version>5.2.4</project.httpcore5.version>
    <project.httpclient5.version>5.3.1</project.httpclient5.version>
    <project.commons-codec.version>1.17.0</project.commons-codec.version>
    <project.oauth.version>1.36.0</project.oauth.version>
    <project.auth.version>1.23.0</project.auth.version>
    <project.jsr305.version>3.0.2</project.jsr305.version>
    <project.gson.version>2.8.6</project.gson.version>
    <project.protobuf-java.version>3.25.3</project.protobuf-java.version>
    <project.guava.version>33.1.0-jre</project.guava.version>
    <project.appengine.version>2.0.27</project.appengine.version>
    <project.xpp3.version>1.1.4c</project.xpp3.version>
    <project.jdo2-api.version>2.3-20090302111651</project.jdo2-api.version>
    <project.datanucleus-core.version>3.2.2</project.datanucleus-core.version>
    <project.datanucleus-api-jdo.version>3.2.1</project.datanucleus-api-jdo.version>
    <project.datanucleus-maven-plugin.version>4.0.3</project.datanucleus-maven-plugin.version>
    <project.servlet-api.version>2.5</project.servlet-api.version>
    <!-- jakarta-servlet-api 5.0.0 is the last version that works with Java 8 -->
    <project.jakarta-servlet-api.version>5.0.0</project.jakarta-servlet-api.version>
    <deploy.autorelease>false</deploy.autorelease>
    <gson.version>2.11.0</gson.version>
  </properties>

  <profiles>
    <profile>
      <id>release-sign-artifacts</id>
      <activation>
        <property>
          <name>performRelease</name>
          <value>true</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>3.2.4</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
                <configuration>
                  <gpgArguments>
                    <arg>--pinentry-mode</arg>
                    <arg>loopback</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!-- set project.root-directory property based on where we are -->
    <profile>
      <id>root-directory</id>
      <activation>
        <file>
          <exists>checkstyle-suppressions.xml</exists>
        </file>
      </activation>
      <properties>
        <project.root-directory>.</project.root-directory>
      </properties>
    </profile>

    <profile>
      <!-- Only run checkstyle plugin on Java 8+ (checkstyle artifact only supports Java 8+) -->
      <id>checkstyle-tests</id>
      <activation>
        <jdk>[1.8,)</jdk>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <dependencies>
              <dependency>
                <groupId>com.puppycrawl.tools</groupId>
                <artifactId>checkstyle</artifactId>
                <version>9.3</version>
              </dependency>
            </dependencies>
            <configuration>
              <configLocation>checkstyle.xml</configLocation>
              <consoleOutput>true</consoleOutput>
              <suppressionsLocation>checkstyle-suppressions.xml</suppressionsLocation>
            </configuration>
            <executions>
              <execution>
                <goals>
                  <goal>check</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <!-- cloud RAD generation -->
      <id>docFX</id>
      <activation>
        <property>
          <!-- activate with -P docFX -->
          <name>docFX</name>
        </property>
      </activation>
      <properties>
        <!-- default config values -->
        <docletName>java-docfx-doclet-1.9.0</docletName>
        <outputpath>${project.build.directory}/docfx-yml</outputpath>
        <projectname>${project.artifactId}</projectname>
        <source>17</source>
      </properties>
      <build>
        <plugins>
          <plugin>
            <artifactId>exec-maven-plugin</artifactId>
            <groupId>org.codehaus.mojo</groupId>
            <executions>
              <execution>
                <id>install-artifact</id>
                <phase>generate-sources</phase>
                <goals>
                  <goal>exec</goal>
                </goals>
                <configuration>
                  <executable>bash</executable>
                  <arguments>
                    <argument>-c</argument>
                    <argument><![CDATA[
                     if [ ! -d "play-services" ]; then
                       mkdir play-services &&
                       cd play-services   &&
                       curl --output play-services-basement-8.3.0.aar https://dl.google.com/dl/android/maven2/com/google/android/gms/play-services-basement/8.3.0/play-services-basement-8.3.0.aar &&
                       unzip play-services-basement-8.3.0.aar &&
                       mvn install:install-file -Dfile=classes.jar -DgroupId=com.google.android.google-play-services -DartifactId=google-play-services -Dversion=1 -Dpackaging=jar
                     fi
                    ]]></argument>
                  </arguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.6.3</version>
            <configuration>
              <doclet>com.microsoft.doclet.DocFxDoclet</doclet>
              <useStandardDocletOptions>false</useStandardDocletOptions>
              <!-- custom config with -Dproperty=value -->
              <additionalOptions>
                -outputpath ${outputpath}
                -projectname ${projectname}
              </additionalOptions>
              <doclint>none</doclint>
              <show>protected</show>
              <nohelp>true</nohelp>
              <source>${source}</source>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>envVarTest</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <!-- Unless overriden in the submodules, this profile run no tests in a submodule -->
              <excludes>
                <exclude>**/*.java</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
