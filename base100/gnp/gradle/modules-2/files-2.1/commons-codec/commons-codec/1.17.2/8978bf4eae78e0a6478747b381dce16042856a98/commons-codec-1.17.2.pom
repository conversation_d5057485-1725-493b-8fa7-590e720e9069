<?xml version="1.0" encoding="UTF-8"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<!--
 To produce reports, use the reporting profile, for example: mvn -Preporting clean site
 You may need to use the -U option to update your environment if you get an error.
 -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>78</version>
  </parent>
  <groupId>commons-codec</groupId>
  <artifactId>commons-codec</artifactId>
  <version>1.17.2</version>
  <name>Apache Commons Codec</name>
  <inceptionYear>2002</inceptionYear>
  <description>
     The Apache Commons Codec component contains encoders and decoders for
     formats such as Base16, Base32, Base64, digest, and Hexadecimal. In addition to these
     widely used encoders and decoders, the codec package also maintains a
     collection of phonetic encoding utilities.
  </description>
  <url>https://commons.apache.org/proper/commons-codec/</url>
  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/CODEC</url>
  </issueManagement>
  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/commons-codec</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/commons-codec</developerConnection>
    <url>https://github.com/apache/commons-codec</url>
    <tag>HEAD</tag>
  </scm>
  <distributionManagement>
    <site>
      <id>apache.website</id>
      <name>Apache Commons Site</name>
      <url>scm:svn:https://svn.apache.org/repos/infra/websites/production/commons/content/proper/commons-codec/</url>
    </site>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.17.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.18.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <properties>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <commons.componentid>codec</commons.componentid>
    <commons.module.name>org.apache.commons.codec</commons.module.name>
    <commons.jira.id>CODEC</commons.jira.id>
    <commons.jira.pid>12310464</commons.jira.pid>
    <!-- Ensure copies work OK (can be removed later when this is in parent POM) -->
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <commons.encoding>UTF-8</commons.encoding>
    <checkstyle.header.file>${basedir}/src/conf/checkstyle-header.txt</checkstyle.header.file>
    <checkstyle.config.file>${basedir}/src/conf/checkstyle.xml</checkstyle.config.file>
    <!-- Commons Release Plugin -->
    <commons.release.version>1.17.2</commons.release.version>
    <commons.bc.version>1.17.1</commons.bc.version>
    <commons.release.next>1.17.3</commons.release.next>
    <commons.rc.version>RC1</commons.rc.version>
    <commons.release.isDistModule>true</commons.release.isDistModule>
    <commons.distSvnStagingUrl>scm:svn:https://dist.apache.org/repos/dist/dev/commons/${commons.componentid}</commons.distSvnStagingUrl>
    <project.build.outputTimestamp>2024-12-28T14:13:52Z</project.build.outputTimestamp>
    <commons.jacoco.version>0.8.12</commons.jacoco.version>
    <!-- Temp for SUREFIRE-2253 -->
    <commons.surefire.version>3.2.5</commons.surefire.version>
    <!-- JaCoCo: Don't make code coverage worse than: -->
    <jacoco.skip>false</jacoco.skip>
    <commons.jacoco.haltOnFailure>true</commons.jacoco.haltOnFailure>
    <commons.jacoco.classRatio>0.92</commons.jacoco.classRatio>
    <commons.jacoco.instructionRatio>0.91</commons.jacoco.instructionRatio>
    <commons.jacoco.methodRatio>0.87</commons.jacoco.methodRatio>
    <commons.jacoco.branchRatio>0.86</commons.jacoco.branchRatio>
    <commons.jacoco.lineRatio>0.89</commons.jacoco.lineRatio>
    <commons.jacoco.complexityRatio>0.84</commons.jacoco.complexityRatio>
  </properties>
  <build>
    <defaultGoal>clean verify apache-rat:check japicmp:cmp checkstyle:check javadoc:javadoc</defaultGoal>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-publish-plugin</artifactId>
          <version>${commons.scm-publish.version}</version>
          <configuration>
            <ignorePathsToDelete>
              <ignorePathToDelete>archive**</ignorePathToDelete>
            </ignorePathsToDelete>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <configuration>
            <configLocation>${checkstyle.config.file}</configLocation>
            <enableRulesSummary>false</enableRulesSummary>
            <includeTestSourceDirectory>true</includeTestSourceDirectory>
            <resourceExcludes>NOTICE.txt,LICENSE.txt,**/pom.properties,**/sha512.properties</resourceExcludes>
          </configuration>
        </plugin>        
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>src/test/resources/org/apache/commons/codec/bla.tar</exclude>
            <exclude>src/test/resources/org/apache/commons/codec/bla.tar.xz</exclude>
            <exclude>src/test/resources/org/apache/commons/codec/empty.bin</exclude>
            <exclude>src/test/resources/org/apache/commons/codec/small.bin</exclude>
          </excludes>
        </configuration>
      </plugin>
      <!-- Add Java 9 Automatic-Module-Name -->
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive combine.children="append">
            <!-- Temporary fix, remove this after this has implemented in parent pom -->
            <manifestEntries>
              <Automatic-Module-Name>${commons.module.name}</Automatic-Module-Name>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>**/*AbstractTest.java</exclude>
            <exclude>**/*PerformanceTest.java</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptors>
            <descriptor>src/assembly/bin.xml</descriptor>
            <descriptor>src/assembly/src.xml</descriptor>
          </descriptors>
          <tarLongFileMode>gnu</tarLongFileMode>
        </configuration>
      </plugin>
      <!-- Allow use of mvn checkstyle:checkstyle. Must agree with reporting section below. -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
      </plugin>
      <!-- Specify source for JDK 11+ javadoc tool to ignore the 'unnamed' module.
           See: https://bugs.openjdk.java.net/browse/JDK-8212233.
           To be removed with parent 49. -->
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <source>${maven.compiler.source}</source>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
        <version>1.24</version>
        <configuration>
          <ignores>
            <!-- JDK9 breaks compatibility by returning ByteBuffer not Buffer -->
            <ignore>java.nio.ByteBuffer</ignore>
          </ignores>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>src/test/resources/org/apache/commons/codec/bla.tar</exclude>
            <exclude>src/test/resources/org/apache/commons/codec/bla.tar.xz</exclude>
            <exclude>src/test/resources/org/apache/commons/codec/empty.bin</exclude>
            <exclude>src/test/resources/org/apache/commons/codec/small.bin</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <!-- We need to specify reportSets because 2.9.1 creates two reports -->
        <reportSets>
          <reportSet>
            <reports>
              <report>checkstyle</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>${commons.pmd.version}</version>
        <configuration>
          <targetJdk>${maven.compiler.target}</targetJdk>
          <linkXref>true</linkXref>
          <rulesets>
            <ruleset>${basedir}/src/conf/pmd.xml</ruleset>
          </rulesets>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.spotbugs</groupId>
        <artifactId>spotbugs-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>taglist-maven-plugin</artifactId>
        <configuration>
          <tags>
            <tag>TODO</tag>
            <tag>NOPMD</tag>
            <tag>NOTE</tag>
          </tags>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>javancss-maven-plugin</artifactId>
        <version>2.1</version>
      </plugin>
    </plugins>
  </reporting>
  <profiles>
    <profile>
	  <!-- Java 8 only -->
      <id>java-8</id>
      <activation>
		<jdk>8</jdk>
	  </activation>
      <properties>
        <!-- JaCoCo: Don't make code coverage worse than: -->
        <commons.jacoco.haltOnFailure>true</commons.jacoco.haltOnFailure>
        <commons.jacoco.classRatio>0.96</commons.jacoco.classRatio>
        <commons.jacoco.instructionRatio>0.97</commons.jacoco.instructionRatio>
        <commons.jacoco.methodRatio>0.88</commons.jacoco.methodRatio>
        <commons.jacoco.branchRatio>0.92</commons.jacoco.branchRatio>
        <commons.jacoco.complexityRatio>0.88</commons.jacoco.complexityRatio>
        <commons.jacoco.lineRatio>0.94</commons.jacoco.lineRatio>    
      </properties>
    </profile>
    <profile>
	  <!-- Java 9 and up -->
      <id>java-9-up</id>
      <activation>
		<jdk>[9,)</jdk>
	  </activation>
      <properties>
        <!-- JaCoCo: Don't make code coverage worse than: -->
        <commons.jacoco.haltOnFailure>true</commons.jacoco.haltOnFailure>
        <commons.jacoco.classRatio>0.96</commons.jacoco.classRatio>
        <commons.jacoco.instructionRatio>0.97</commons.jacoco.instructionRatio>
        <commons.jacoco.methodRatio>0.93</commons.jacoco.methodRatio>
        <commons.jacoco.branchRatio>0.92</commons.jacoco.branchRatio>
        <commons.jacoco.complexityRatio>0.90</commons.jacoco.complexityRatio>
        <commons.jacoco.lineRatio>0.95</commons.jacoco.lineRatio>    
      </properties>
    </profile>
  </profiles>
  <developers>
    <developer>
      <name>Henri Yandell</name>
      <id>bayard</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Tim OBrien</name>
      <id>tobrien</id>
      <email><EMAIL></email>
      <timezone>-6</timezone>
    </developer>
    <developer>
      <name>Scott Sanders</name>
      <id>sanders</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Rodney Waldhoff</name>
      <id>rwaldhoff</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Daniel Rall</name>
      <id>dlr</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Jon S. Stevens</name>
      <id>jon</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>ggregory</id>
      <name>Gary Gregory</name>
      <email>ggregory at apache.org</email>
      <url>https://www.garygregory.com</url>
      <organization>The Apache Software Foundation</organization>
      <organizationUrl>https://www.apache.org/</organizationUrl>      
      <roles>
        <role>PMC Member</role>
      </roles>
      <timezone>America/New_York</timezone>
      <properties>
        <picUrl>https://people.apache.org/~ggregory/img/garydgregory80.png</picUrl>
      </properties>
    </developer>
    <developer>
      <name>David Graham</name>
      <id>dgraham</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Julius Davies</name>
      <id>julius</id>
      <email><EMAIL></email>
      <organizationUrl>http://juliusdavies.ca/</organizationUrl>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <name>Thomas Neidhart</name>
      <id>tn</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Rob Tompkins</name>
      <id>chtompki</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Matt Sicker</name>
      <id>mattsicker</id>
      <email><EMAIL></email>
      <url>https://musigma.blog/</url>
    </developer>
  </developers>
  <contributors>
    <contributor>
      <name>Christopher O'Brien</name>
      <email><EMAIL></email>
      <roles>
        <role>hex</role>
        <role>md5</role>
        <role>architecture</role>
      </roles>
    </contributor>
    <contributor>
      <name>Martin Redington</name>
      <roles>
        <role>Representing xml-rpc</role>
      </roles>
    </contributor>
    <contributor>
      <name>Jeffery Dever</name>
      <roles>
        <role>Representing http-client</role>
      </roles>
    </contributor>
    <contributor>
      <name>Steve Zimmermann</name>
      <email><EMAIL></email>
      <roles>
        <role>Documentation</role>
      </roles>
    </contributor>
    <contributor>
      <name>Benjamin Walstrum</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Oleg Kalnichevski</name>
      <email><EMAIL></email>
      <roles>
        <role>Representing http-client</role>
      </roles>
    </contributor>
    <contributor>
      <name>Dave Dribin</name>
      <email><EMAIL></email>
      <roles>
        <role>DigestUtil</role>
      </roles>
    </contributor>
    <contributor>
      <name>Alex Karasulu</name>
      <email>aok123 at bellsouth.net</email>
      <roles>
        <role>Submitted Binary class and test</role>
      </roles>
    </contributor>
    <contributor>
      <name>Matthew Inger</name>
      <email>mattinger at yahoo.com</email>
      <roles>
        <role>Submitted DIFFERENCE algorithm for Soundex and RefinedSoundex</role>
      </roles>
    </contributor>
    <contributor>
      <name>Jochen Wiedmann</name>
      <email><EMAIL></email>
      <roles>
        <role>Base64 code [CODEC-69]</role>
      </roles>
    </contributor>
    <contributor>
      <name>Sebastian Bazley</name>
      <email><EMAIL></email>
      <roles>
        <role>Streaming Base64</role>
      </roles>
    </contributor>
    <contributor>
      <name>Matthew Pocock</name>
      <email><EMAIL></email>
      <roles>
        <role>Beider-Morse phonetic matching</role>
      </roles>
    </contributor>
    <contributor>
      <name>Colm Rice</name>
      <email>colm_rice at hotmail dot com</email>
      <roles>
        <role>Submitted Match Rating Approach (MRA) phonetic encoder and tests [CODEC-161]</role>
      </roles>
    </contributor>
    <contributor>
      <name>Adam Retter</name>
      <organization>Evolved Binary</organization>
      <roles>
        <role>Base16 Input and Output Streams</role>
      </roles>
    </contributor>
  </contributors>
</project>
