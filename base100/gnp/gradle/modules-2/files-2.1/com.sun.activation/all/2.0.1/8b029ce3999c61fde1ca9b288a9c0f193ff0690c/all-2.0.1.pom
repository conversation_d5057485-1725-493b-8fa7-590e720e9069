<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 1997, 2021 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
			    http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.sun.activation</groupId>
    <artifactId>all</artifactId>
    <packaging>pom</packaging>
    <version>2.0.1</version>
    <name>Jakarta Activation distribution</name>
    <description>${project.name}</description>
    <url>https://github.com/eclipse-ee4j/jaf</url>

    <scm>
        <connection>scm:git:ssh://**************/eclipse/jaf.git</connection>
        <developerConnection>scm:git:ssh://**************/eclipse/jaf.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/jaf</url>
        <tag>HEAD</tag>
    </scm>

    <issueManagement>
        <system>github</system>
        <url>https://github.com/eclipse-ee4j/jaf/issues/</url>
    </issueManagement>

    <licenses>
        <license>
            <name>EDL 1.0</name>
            <url>http://www.eclipse.org/org/documents/edl-v10.php</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <properties>
        <activation.spec.version>2.0</activation.spec.version>
        <!-- defaults that are overridden in activation module -->
        <activation.extensionName>jakarta.activation</activation.extensionName>
        <activation.specificationTitle>${project.groupId}.${project.artifactId}</activation.specificationTitle>
        <activation.implementationTitle>${project.groupId}.${project.artifactId}</activation.implementationTitle>
        <activation.recompile.skip>false</activation.recompile.skip>
        <!-- for the osgiversion-maven-plugin -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <javadoc.title>Jakarta Activation API documentation</javadoc.title>
        <copyright.exclude>copyright-exclude</copyright.exclude>
        <copyright.ignoreyear>false</copyright.ignoreyear>
        <copyright.scmonly>true</copyright.scmonly>
        <copyright.update>false</copyright.update>
        <spotbugs.skip>true</spotbugs.skip>
        <spotbugs.threshold>High</spotbugs.threshold>
        <!--Maven plugins version-->
        <hk2.plugin.version>3.0.1</hk2.plugin.version>
        <spotbugs.plugin.version>4.2.0</spotbugs.plugin.version>
    </properties>

    <developers>
        <developer>
            <id>shannon</id>
            <name>Bill Shannon</name>
            <email><EMAIL></email>
            <organization>Oracle</organization>
            <roles>
                <role>lead</role>
            </roles>
        </developer>
    </developers>

    <!-- following to enable use of "mvn site:stage" -->
    <distributionManagement>
        <site>
            <id>oracle.com</id>
            <url>file:/tmp</url> <!-- not used -->
        </site>
    </distributionManagement>

    <modules>
        <module>activation</module>
        <module>activationapi</module>
    </modules>

    <profiles>
        <!--
            This profile contains modules that should only be built
            but not installed or deployed.
        -->
        <profile>
            <id>build-only</id>
            <modules>
                <module>demo</module>
            </modules>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!--
            This profile is used for deploying a Jakarta Activation
            final release.

            Activating this profile manually for deployment causes
            the above profile to be deactivated, which works around
            an apparent bug in maven that prevents me from manually
            deactivating a profile.  This profile purposely has none
            of the modules I don't want to be deployed.
        -->
        <profile>
            <id>oss-release</id>
        </profile>
    </profiles>

    <build>
        <defaultGoal>install</defaultGoal>
        <plugins>
            <!--
            Make sure we're using the correct version of maven.
            -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>enforce-version</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>[3.6.3,)</version>
                                </requireMavenVersion>
                                <requireJavaVersion>
                                    <version>[11,)</version>
                                </requireJavaVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--
            This plugin is responsible for packaging artifacts
            as OSGi bundles.  Please refer to
            http://felix.apache.org/site/apache-felix-maven-bundle-plugin-bnd.html
            for more information about how to use this plugin.
            -->
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <configuration>
                    <niceManifest>true</niceManifest>
                    <instructions>
                        <Export-Package>
                            ${activation.packages.export}
                        </Export-Package>
                        <Import-Package>
                            ${activation.packages.import}
                        </Import-Package>
                        <DynamicImport-Package>
                            *
                        </DynamicImport-Package>
                    </instructions>
                </configuration>
                <!--
                    Since we don't change the packaging type to bundle, we
                    need to configure the plugin to execute the manifest goal
                    during the process-classes phase of the build life cycle.
                -->
                <executions>
                    <execution>
                        <id>osgi-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--
            Since we don't want a qualifier like b05 or SNAPSHOT to
            appear in the OSGi package version attribute, we use
            the following plugin to populate a project property
            with an OSGi version that is equivalent to the maven
            version without the qualifier.
            -->
            <plugin>
                <groupId>org.glassfish.hk2</groupId>
                <artifactId>osgiversion-maven-plugin</artifactId>
                <version>${hk2.plugin.version}</version>
                <configuration>
                    <dropVersionComponent>qualifier</dropVersionComponent>
                    <versionPropertyName>activation.osgiversion</versionPropertyName>
                </configuration>
                <executions>
                    <execution>
                        <id>compute-osgi-version</id>
                        <goals>
                            <goal>compute-osgi-version</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--
            Use the JDK 9+ compiler but with -source 1.8 for all
            but the module-info.java file.
            -->
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <skipMain>${activation.recompile.skip}</skipMain>
                    <release>9</release>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                        <arg>-Xlint:-rawtypes</arg>
                        <arg>-Xlint:-unchecked</arg>
                        <arg>-Xlint:-finally</arg>
                    </compilerArgs>
                    <showWarnings>true</showWarnings>
                </configuration>
                <executions>
                    <execution>
                        <id>base-compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <release>8</release>
                            <excludes>
                                <exclude>module-info.java</exclude>
                            </excludes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <!-- need at least this version to make excludes work -->
                <configuration>
                    <finalName>${project.artifactId}</finalName>
                    <archive>
                        <!--
                            Configure the maven-jar-plugin to pick up
                            META-INF/MANIFEST.MF that's generated by
                            the maven-bundle-plugin.
                        -->
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                        <manifestEntries>
                            <Extension-Name>${activation.extensionName}</Extension-Name>
                            <Specification-Title>${activation.specificationTitle}</Specification-Title>
                            <Specification-Version>${activation.spec.version}</Specification-Version>
                            <Specification-Vendor>${project.organization.name}</Specification-Vendor>
                            <Implementation-Title>${activation.implementationTitle}</Implementation-Title>
                            <Implementation-Version>${project.version}</Implementation-Version>
                            <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
                            <Implementation-Vendor-Id>com.sun</Implementation-Vendor-Id>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
            <!--
            Tell the source plugin about the sources that may have
            been downloaded by the maven-dependency-plugin.
            -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>currentyear-property</id>
                        <goals>
                            <goal>timestamp-property</goal>
                        </goals>
                        <phase>validate</phase>
                        <configuration>
                            <name>current.year</name>
                            <locale>en,US</locale>
                            <pattern>yyyy</pattern>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source> <!-- for dependencies -->
                                    ${project.build.directory}/generated-sources/sources
                                </source>
                            </sources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add-resource</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${main.basedir}</directory>
                                    <targetPath>META-INF</targetPath>
                                    <includes>
                                        <include>LICENSE.md</include>
                                        <include>NOTICE.md</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--
            Directory plugin to find parent root directory absolute path.
            Sets ${main.basedir}, used above with build-helper-maven-plugin
            to include LICENSE.md and NOTICE.md in jar files.
            -->
            <plugin>
                <groupId>org.commonjava.maven.plugins</groupId>
                <artifactId>directory-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>directories</id>
                        <goals>
                            <goal>highest-basedir</goal>
                        </goals>
                        <phase>initialize</phase>
                        <configuration>
                            <property>main.basedir</property>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!--
            Configure the source plugin here so that it will know
            about the sources that may have been downloaded by the
            maven-dependency-plugin and configured by the
            build-helper-maven-plugin.
            -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <author>false</author>
                    <description>${javadoc.title}</description>
                    <doctitle>${javadoc.title}</doctitle>
                    <windowtitle>${javadoc.title}</windowtitle>
                    <splitindex>true</splitindex>
                    <use>true</use>
                    <notimestamp>true</notimestamp>
                    <serialwarn>true</serialwarn>
                    <quiet>true</quiet>
                    <header><![CDATA[<br>Jakarta Activation API v${project.version}]]></header>
                    <bottom>
                        <![CDATA[
Comments to : <a href="mailto:<EMAIL>"><EMAIL></a>.<br>
Copyright &#169; 2019, ${current.year} Eclipse Foundation. All rights reserved.<br>
Use is subject to <a href="{@docRoot}/doc-files/speclicense.html" target="_top">license terms</a>.]]>
                    </bottom>
                    <groups>
                        <group>
                            <title>Jakarta Activation API Packages</title>
                            <packages>jakarta.*</packages>
                        </group>
                    </groups>
                    <!-- force the doc-files directory to be copied -->
                    <docfilessubdirs>true</docfilessubdirs>
                    <javadocDirectory>${main.basedir}/activation/src/main/javadoc</javadocDirectory>
                    <detectJavaApiLink>false</detectJavaApiLink>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.glassfish.copyright</groupId>
                <artifactId>glassfish-copyright-maven-plugin</artifactId>
                <configuration>
                    <excludeFile>${copyright.exclude}</excludeFile>
                    <!-- skip files not under SCM-->
                    <scmOnly>${copyright.scmonly}</scmOnly>
                    <!-- for use with repair -->
                    <update>${copyright.update}</update>
                    <!-- check that year is correct -->
                    <ignoreYear>${copyright.ignoreyear}</ignoreYear>
                </configuration>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.commonjava.maven.plugins</groupId>
                    <artifactId>directory-maven-plugin</artifactId>
                    <version>0.3.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
                <plugin>
                    <!--
                    By default, disable the SpotBugs plugin for all modules.
                    It's enabled in the modules where we actually want to
                    run it.
                    -->
                    <groupId>com.github.spotbugs</groupId>
                    <artifactId>spotbugs-maven-plugin</artifactId>
                    <version>${spotbugs.plugin.version}</version>
                    <configuration>
                        <skip>${spotbugs.skip}</skip>
                        <threshold>${spotbugs.threshold}</threshold>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M3</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>5.1.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-project-info-reports-plugin</artifactId>
                    <version>3.1.1</version>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>2.4</version>
                    <configuration>
                        <scm>git</scm>
                        <scmOnly>true</scmOnly>
                        <excludeFile>
                            copyright-exclude
                        </excludeFile>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-site-plugin</artifactId>
                    <version>3.8.2</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <dependencyManagement>
        <dependencies>
            <!--Our artifacts-->
            <dependency>
                <groupId>com.sun.activation</groupId>
                <artifactId>jakarta.activation</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <reporting>
        <plugins>
            <!--
            Configure SpotBugs to run with "mvn site" and
            generate html output that can be viewed directly.
            -->
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <version>${spotbugs.plugin.version}</version>
                <configuration>
                    <skip>${spotbugs.skip}</skip>
                    <threshold>${spotbugs.threshold}</threshold>
                </configuration>
            </plugin>
        </plugins>
    </reporting>
</project>
