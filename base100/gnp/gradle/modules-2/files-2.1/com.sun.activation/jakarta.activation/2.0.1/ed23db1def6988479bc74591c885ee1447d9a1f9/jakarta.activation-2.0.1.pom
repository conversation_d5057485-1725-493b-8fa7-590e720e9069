<?xml version="1.0" encoding="UTF-8"?>
<!--

    Copyright (c) 1997, 2021 Oracle and/or its affiliates. All rights reserved.

    This program and the accompanying materials are made available under the
    terms of the Eclipse Distribution License v. 1.0, which is available at
    http://www.eclipse.org/org/documents/edl-v10.php.

    SPDX-License-Identifier: BSD-3-Clause

-->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
			    http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.sun.activation</groupId>
        <artifactId>all</artifactId>
        <version>2.0.1</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.sun.activation</groupId>
    <artifactId>jakarta.activation</artifactId>
    <name>Jakarta Activation</name>

    <properties>
        <activation.specificationTitle>Jakarta Activation Specification</activation.specificationTitle>
        <activation.implementationTitle>jakarta.activation</activation.implementationTitle>
        <activation.packages.export>
            jakarta.activation.*; version=${activation.spec.version},
            com.sun.activation.*; version=${activation.osgiversion}
        </activation.packages.export>
        <activation.packages.import>*</activation.packages.import>
        <spotbugs.skip>false</spotbugs.skip>
    </properties>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <release>11</release>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
