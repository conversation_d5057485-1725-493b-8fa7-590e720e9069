<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <artifactId>gax-httpjson</artifactId>
  <version>2.54.1</version> <!-- {x-version-update:gax:current} -->
  <packaging>jar</packaging>
  <name>GAX (Google Api eXtensions) for Java (HTTP JSON)</name>
  <description>Google Api eXtensions for Java (HTTP JSON)</description>

  <parent>
    <groupId>com.google.api</groupId>
    <artifactId>gax-parent</artifactId>
    <version>2.54.1</version><!-- {x-version-update:gax:current} -->
  </parent>

  <properties>
    <!-- Skip breaking changes checking against previous version while gax-httpj<PERSON> is not GA -->
    <clirr.skip>true</clirr.skip>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>gax</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>api-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>proto-google-common-protos</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.auth</groupId>
      <artifactId>google-auth-library-credentials</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
    </dependency>
    <dependency>
      <groupId>org.threeten</groupId>
      <artifactId>threetenbp</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.auth</groupId>
      <artifactId>google-auth-library-oauth2-http</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.http-client</groupId>
      <artifactId>google-http-client-gson</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java-util</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.google.auto.value</groupId>
      <artifactId>auto-value-annotations</artifactId>
      <scope>provided</scope>
    </dependency>

    <!-- test dependencies -->
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>gax</artifactId>
      <type>test-jar</type>
      <scope>test</scope>
      <classifier>testlib</classifier>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
            <configuration>
              <skip>false</skip>
              <classifier>testlib</classifier>
              <includes>
                <include>com/google/api/gax/httpjson/testing/**</include>
              </includes>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
