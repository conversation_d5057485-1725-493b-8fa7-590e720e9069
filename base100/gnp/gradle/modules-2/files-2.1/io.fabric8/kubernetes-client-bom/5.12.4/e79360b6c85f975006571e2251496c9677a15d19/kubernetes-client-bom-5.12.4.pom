<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">


    <modelVersion>4.0.0</modelVersion>

    <groupId>io.fabric8</groupId>
    <artifactId>kubernetes-client-bom</artifactId>
    <version>5.12.4</version>
    <name>Fabric8 :: Kubernetes :: Bom</name>
    <packaging>pom</packaging>
    <description>Generated Bom</description>
    
        <url>http://fabric8.io/</url>    
        <licenses>
            <license>
                <name>Apache License, Version 2.0</name>
                <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
                <distribution>repo</distribution>
            </license>
        </licenses>
    
    
        <scm>
            <connection>scm:git:**************:fabric8io/kubernetes-client.git</connection>
            <developerConnection>scm:git:**************:fabric8io/kubernetes-client.git</developerConnection>
            <url>http://github.com/fabric8io/kubernetes-client/</url>
            <tag>5.12.4</tag>
        </scm>
    
            <developers>
            <developer>
                <id>geeks</id>
                <name>Fabric8 Development Team</name>
                <organization>fabric8</organization>
                <organizationUrl>http://fabric8.io/</organizationUrl>
            </developer>
        </developers>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-common</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>model-annotator</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-core</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-rbac</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-admissionregistration</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-apps</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-autoscaling</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-apiextensions</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-batch</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-certificates</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-coordination</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-discovery</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-events</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-extensions</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-networking</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-metrics</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-policy</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-scheduling</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-storageclass</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-jsonschema2pojo</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-flowcontrol</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-node</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-clusterautoscaling</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-hive</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-installer</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-operator</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-operatorhub</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-machine</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-monitoring</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-console</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-machineconfig</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-tuned</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-whereabouts</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-storageversionmigrator</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-miscellaneous</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-client</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-server-mock</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-client</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>knative-model</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>knative-client</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>knative-mock</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>knative-examples</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>knative-tests</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>tekton-model-v1alpha1</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>tekton-model-v1beta1</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>tekton-model-triggers</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>tekton-client</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>tekton-mock</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>tekton-examples</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>tekton-tests</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>servicecatalog-model</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>servicecatalog-client</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>servicecatalog-server-mock</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>service-catalog-examples</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>servicecatalog-tests</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volumesnapshot-model</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volumesnapshot-client</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volumesnapshot-server-mock</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volumesnapshot-examples</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volumesnapshot-tests</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>chaosmesh-model</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>chaosmesh-client</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>chaosmesh-server-mock</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>chaosmesh-examples</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>chaosmesh-tests</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>camel-k-model-v1</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>camel-k-model-v1alpha1</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>camel-k-client</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>camel-k-mock</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>camel-k-tests</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>certmanager-model-v1alpha2</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>certmanager-model-v1alpha3</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>certmanager-model-v1beta1</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>certmanager-model-v1</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>certmanager-client</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>certmanager-server-mock</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>certmanager-examples</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>certmanager-tests</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>verticalpodautoscaler-model-v1</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>verticalpodautoscaler-client</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>verticalpodautoscaler-server-mock</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>verticalpodautoscaler-examples</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>verticalpodautoscaler-tests</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volcano-model-v1beta1</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volcano-client</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volcano-examples</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volcano-server-mock</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volcano-tests</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>istio-model-v1alpha3</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>istio-model-v1beta1</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>istio-client</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>istio-server-mock</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>istio-examples</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>istio-tests</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-apps-model</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-agent-model</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-cluster-model</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-discovery-model</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-observability-model</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-operator-model</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-placementruleapps-model</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-policy-model</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-search-model</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-client</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-server-mock</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-tests</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openclustermanagement-examples</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-server-mock</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-examples</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8.kubernetes</groupId>
                <artifactId>kubernetes-karaf</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8.kubernetes</groupId>
                <artifactId>kubernetes-karaf-itests</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>crd-generator-api</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>crd-generator-apt</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-test</artifactId>
                <version>5.12.4</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-openshift-uberjar</artifactId>
                <version>5.12.4</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    
        <build>
            <pluginManagement>
                <plugins>
                </plugins>
            </pluginManagement>
        </build>
    
</project>
