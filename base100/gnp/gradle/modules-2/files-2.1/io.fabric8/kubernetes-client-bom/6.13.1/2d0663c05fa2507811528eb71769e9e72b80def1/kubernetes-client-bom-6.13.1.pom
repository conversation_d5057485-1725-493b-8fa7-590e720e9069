<?xml version='1.0' encoding='UTF-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>io.fabric8</groupId>
    <artifactId>kubernetes-client-bom</artifactId>
    <version>6.13.1</version>
    <name>Fabric8 :: Kubernetes :: Bom</name>
    <packaging>pom</packaging>
    <description>Generated Bom</description>
    
    <url>https://github.com/fabric8io/kubernetes-client</url>    
    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    
    
    <scm>
        <connection>scm:git:**************:fabric8io/kubernetes-client.git</connection>
        <developerConnection>scm:git:**************:fabric8io/kubernetes-client.git</developerConnection>
        <url>https://github.com/fabric8io/kubernetes-client/</url>
        <tag>6.13.1</tag>
    </scm>
        
    <distributionManagement>
      
      <repository>
        <id>ossrh</id>
        <name>Sonatype OSS Repository</name>
        <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
        <layout>default</layout>
      </repository>
            
      <snapshotRepository>
        <id>ossrh</id>
        <name>Sonatype OSS Repository (Snapshots)</name>
        <url>https://oss.sonatype.org/content/repositories/snapshots</url>
        <layout>default</layout>
      </snapshotRepository>
      
    </distributionManagement>
        
    <developers>
        <developer>
            <id>geeks</id>
            <name>Fabric8 Development Team</name>
            <organization>fabric8</organization>
            <organizationUrl>https://github.com/fabric8io/kubernetes-client</organizationUrl>
        </developer>
    </developers>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-common</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-core</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-rbac</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-admissionregistration</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-apps</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-autoscaling</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-apiextensions</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-batch</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-certificates</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-coordination</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-discovery</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-events</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-extensions</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-networking</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-metrics</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-policy</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-scheduling</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-storageclass</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-config</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-jsonschema2pojo</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-gatewayapi</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-flowcontrol</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-node</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-resource</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-model-kustomize</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-clusterautoscaling</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-hive</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-machine</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-installer</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-operator</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-operatorhub</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-monitoring</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-console</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-machineconfig</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-tuned</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-whereabouts</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-storageversionmigrator</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-model-miscellaneous</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>mockwebserver</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-client-api</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-httpclient-okhttp</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-client</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-junit-jupiter</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-junit-jupiter-autodetected</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>servicecatalog-model</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>servicecatalog-client</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-server-mock</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-client-api</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-client</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>knative-model</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>knative-client</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>knative-examples</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>knative-tests</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>tekton-model-v1</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>tekton-model-v1alpha1</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>tekton-model-v1beta1</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>tekton-model-triggers-v1alpha1</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>tekton-model-triggers-v1beta1</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>tekton-client</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>tekton-examples</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>tekton-tests</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>service-catalog-examples</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>servicecatalog-tests</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volumesnapshot-model</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volumesnapshot-client</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volumesnapshot-examples</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volumesnapshot-tests</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>chaosmesh-model</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>chaosmesh-client</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>chaosmesh-examples</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>chaosmesh-tests</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>certmanager-model-v1alpha2</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>certmanager-model-v1alpha3</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>certmanager-model-v1beta1</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>certmanager-model-v1</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>certmanager-client</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>certmanager-examples</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>certmanager-tests</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>verticalpodautoscaler-model-v1</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>verticalpodautoscaler-client</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>verticalpodautoscaler-examples</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>verticalpodautoscaler-tests</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volcano-model-v1beta1</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volcano-client</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volcano-examples</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>volcano-tests</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>istio-model-v1alpha3</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>istio-model-v1beta1</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>istio-client</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>istio-examples</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>istio-tests</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-apps-model</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-agent-model</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-cluster-model</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-discovery-model</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-observability-model</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-operator-model</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-placementruleapps-model</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-policy-model</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-search-model</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-client</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-cluster-management-tests</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openclustermanagement-examples</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-virtual-networking-model-v1</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-virtual-networking-client</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>open-virtual-networking-tests</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>openshift-server-mock</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-examples</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8.kubernetes</groupId>
                <artifactId>kubernetes-karaf</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8.kubernetes</groupId>
                <artifactId>kubernetes-karaf-itests</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>generator-annotations</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>crd-generator-api</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>crd-generator-apt</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-test</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-openshift-uberjar</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>crd-generator-api-v2</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>crd-generator-test</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>crd-generator-test-apt</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>java-generator-core</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>java-generator-cli</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8.java-generator</groupId>
                <artifactId>io.fabric8.java-generator.gradle.plugin</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>java-generator-maven-plugin</artifactId>
                <version>6.13.1</version>
                <type>maven-plugin</type>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>java-generator-integration-tests</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>java-generator-benchmark</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-httpclient-vertx</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-client-init-bc-fips</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-log4j</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-httpclient-jdk</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-httpclient-jetty</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-httpclient-tests</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kube-api-test</artifactId>
                <version>6.13.1</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kube-api-test-client-inject</artifactId>
                <version>6.13.1</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
