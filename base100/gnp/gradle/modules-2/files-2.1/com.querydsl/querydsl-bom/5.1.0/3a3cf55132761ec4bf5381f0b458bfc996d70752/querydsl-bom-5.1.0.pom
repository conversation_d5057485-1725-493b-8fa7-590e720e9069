<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.querydsl</groupId>
  <artifactId>querydsl-bom</artifactId>
  <version>5.1.0</version>
  <packaging>pom</packaging>
  <name>Querydsl - Bill of materials</name>
  <description>Bill of materials</description>
  <url>http://www.querydsl.com</url>
  <inceptionYear>2007</inceptionYear>
  <organization>
    <name>Querydsl</name>
    <url>http://www.querydsl.com</url>
  </organization>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>timowest</id>
      <name>Timo Westkämper</name>
      <organization>Mysema Ltd</organization>
      <roles>
        <role>Project Manager</role>
        <role>Architect</role>
      </roles>
    </developer>
    <developer>
      <id>ssaarela</id>
      <name>Samppa Saarela</name>
      <organization>Mysema Ltd</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>ponzao</id>
      <name>Vesa Marttila</name>
      <organization>Mysema Ltd</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>mangolas</id>
      <name>Lassi Immonen</name>
      <organization>Mysema Ltd</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>Shredder121</id>
      <name>Ruben Dijkstra</name>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>johnktims</id>
      <name>John Tims</name>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>robertandrewbain</id>
      <name>Robert Bain</name>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>jwgmeligmeyling</id>
      <name>Jan-Willem Gmelig Meyling</name>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:**************:querydsl/querydsl.git/querydsl-bom</connection>
    <developerConnection>scm:git:**************:querydsl/querydsl.git/querydsl-bom</developerConnection>
    <url>http://github.com/querydsl/querydsl/querydsl-bom</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>sonatype-nexus-staging</id>
      <name>Nexus Release Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-core</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-codegen</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>codegen-utils</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-spatial</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-apt</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-collections</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-guava</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-sql</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-sql-spatial</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-sql-codegen</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-sql-spring</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-jpa</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-jpa-codegen</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-jdo</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-kotlin-codegen</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-lucene3</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-lucene4</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-lucene5</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-hibernate-search</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-mongodb</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-scala</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>querydsl-kotlin</artifactId>
        <version>${project.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
