<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.api.grpc</groupId>
  <artifactId>proto-google-cloud-monitoring-v3</artifactId>
  <version>3.31.0</version>
  <name>proto-google-cloud-monitoring-v3</name>
  <description>PROTO library for proto-google-cloud-monitoring-v3</description>
  <url>https://github.com/googleapis/google-cloud-java</url>
  <organization>
    <name>Google LLC</name>
  </organization>
  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>suztomo</id>
      <name>Tomo Suzuki</name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <scm child.scm.connection.inherit.append.path="false" child.scm.developerConnection.inherit.append.path="false" child.scm.url.inherit.append.path="false">
    <connection>scm:git:**************:googleapis/google-cloud-java.git</connection>
    <developerConnection>scm:git:**************:googleapis/google-cloud-java.git</developerConnection>
    <url>https://github.com/googleapis/google-cloud-java</url>
  </scm>
  <issueManagement>
    <system>GitHub Issues</system>
    <url>https://github.com/googleapis/google-cloud-java/issues</url>
  </issueManagement>
  <distributionManagement>
    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://google.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <url>https://google.oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>3.24.4</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>proto-google-common-protos</artifactId>
      <version>2.28.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>protobuf-java</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>api-common</artifactId>
      <version>2.20.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>auto-value-annotations</artifactId>
          <groupId>com.google.auto.value</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>javax.annotation-api</artifactId>
          <groupId>javax.annotation</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.auto.value</groupId>
      <artifactId>auto-value-annotations</artifactId>
      <version>1.10.4</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.2</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>javax.annotation</groupId>
      <artifactId>javax.annotation-api</artifactId>
      <version>1.3.2</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.errorprone</groupId>
      <artifactId>error_prone_annotations</artifactId>
      <version>2.22.0</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>32.1.2-jre</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <artifactId>failureaccess</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>listenablefuture</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jsr305</artifactId>
          <groupId>com.google.code.findbugs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>checker-qual</artifactId>
          <groupId>org.checkerframework</groupId>
        </exclusion>
        <exclusion>
          <artifactId>error_prone_annotations</artifactId>
          <groupId>com.google.errorprone</groupId>
        </exclusion>
        <exclusion>
          <artifactId>j2objc-annotations</artifactId>
          <groupId>com.google.j2objc</groupId>
        </exclusion>
        <exclusion>
          <artifactId>srczip</artifactId>
          <groupId>jdk</groupId>
        </exclusion>
      </exclusions>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>failureaccess</artifactId>
      <version>1.0.1</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>listenablefuture</artifactId>
      <version>9999.0-empty-to-avoid-conflict-with-guava</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>org.checkerframework</groupId>
      <artifactId>checker-qual</artifactId>
      <version>3.39.0</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
    <dependency>
      <groupId>com.google.j2objc</groupId>
      <artifactId>j2objc-annotations</artifactId>
      <version>2.8</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <id>google-maven-central-copy</id>
      <name>Google Maven Central copy</name>
      <url>https://maven-central.storage-download.googleapis.com/maven2</url>
    </repository>
    <repository>
      <id>maven-central</id>
      <name>Maven Central</name>
      <url>https://repo1.maven.org/maven2</url>
    </repository>
  </repositories>
</project>
