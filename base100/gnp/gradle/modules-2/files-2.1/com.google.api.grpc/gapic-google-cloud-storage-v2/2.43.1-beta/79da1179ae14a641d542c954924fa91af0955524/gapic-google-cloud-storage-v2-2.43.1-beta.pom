<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.api.grpc</groupId>
  <artifactId>gapic-google-cloud-storage-v2</artifactId>
  <version>2.43.1-beta</version><!-- {x-version-update:gapic-google-cloud-storage-v2:current} -->
  <name>gapic-google-cloud-storage-v2</name>
  <description>GRPC library for gapic-google-cloud-storage-v2</description>
  <parent>
    <groupId>com.google.cloud</groupId>
    <artifactId>google-cloud-storage-parent</artifactId>
    <version>2.43.1</version><!-- {x-version-update:google-cloud-storage:current} -->
  </parent>
  <dependencies>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-api</artifactId>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-stub</artifactId>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-protobuf</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>proto-google-cloud-storage-v2</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>grpc-google-cloud-storage-v2</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>proto-google-common-protos</artifactId>
    </dependency>
    <dependency>
      <groupId>org.threeten</groupId>
      <artifactId>threetenbp</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>api-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>gax</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.api.grpc</groupId>
      <artifactId>proto-google-iam-v1</artifactId>
    </dependency>

    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>gax-grpc</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
    </dependency>

    <dependency>
      <groupId>com.google.api</groupId>
      <artifactId>gax-grpc</artifactId>
      <classifier>testlib</classifier>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <profiles>
    <profile>
      <id>java9</id>
      <activation>
        <jdk>[9,)</jdk>
      </activation>
      <dependencies>
        <dependency>
          <groupId>javax.annotation</groupId>
          <artifactId>javax.annotation-api</artifactId>
        </dependency>
      </dependencies>
    </profile>
  </profiles>
</project>
