<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.api.grpc</groupId>
  <artifactId>proto-google-common-protos</artifactId>
  <version>2.45.1</version>
  <name>proto-google-common-protos</name>
  <description>PROTO library for proto-google-common-protos</description>
  <url>https://github.com/googleapis/sdk-platform-java</url>
  <organization>
    <name>Google LLC</name>
  </organization>
  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>chingor</id>
      <name>Jeff <PERSON></name>
      <email><EMAIL></email>
      <organization>Google</organization>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>
  <scm child.scm.connection.inherit.append.path="false" child.scm.developerConnection.inherit.append.path="false" child.scm.url.inherit.append.path="false">
    <connection>scm:git:**************:googleapis/sdk-platform-java.git</connection>
    <developerConnection>scm:git:**************:googleapis/sdk-platform-java.git</developerConnection>
    <url>https://github.com/googleapis/sdk-platform-java</url>
  </scm>
  <issueManagement>
    <system>GitHub Issues</system>
    <url>https://github.com/googleapis/sdk-platform-java/issues</url>
  </issueManagement>
  <distributionManagement>
    <repository>
      <id>sonatype-nexus-staging</id>
      <url>https://google.oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <url>https://google.oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
  </distributionManagement>
  <dependencies>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>3.25.5</version>
      <scope>compile</scope>
      <optional>false</optional>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <id>google-maven-central-copy</id>
      <name>Google Maven Central copy</name>
      <url>https://maven-central.storage-download.googleapis.com/maven2</url>
    </repository>
    <repository>
      <id>maven-central</id>
      <name>Maven Central</name>
      <url>https://repo1.maven.org/maven2</url>
    </repository>
  </repositories>
</project>
