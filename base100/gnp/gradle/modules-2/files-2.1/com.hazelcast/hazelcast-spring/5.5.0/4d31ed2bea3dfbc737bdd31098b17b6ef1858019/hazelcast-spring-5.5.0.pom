<!--
  ~ Copyright (c) 2008-2024, Hazelcast, Inc. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <name>hazelcast-spring</name>
    <artifactId>hazelcast-spring</artifactId>
    <packaging>jar</packaging>

    <parent>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast-root</artifactId>
        <version>5.5.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <properties>
        <!-- needed for CheckStyle -->
        <main.basedir>${project.parent.basedir}</main.basedir>

        <org.springframework.version>6.1.10</org.springframework.version>
        <javax.annotation.version>1.3.2</javax.annotation.version>
        <hazelcast.latest.version>4.0</hazelcast.latest.version>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <executions>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                        <configuration>
                            <instructions>
                                <Import-Package>
                                    !org.junit,
                                    !com.hazelcast.*,
                                    org.apache.log4j;resolution:=optional,
                                    org.apache.log4j.*;resolution:=optional,
                                    org.apache.logging.log4j;resolution:=optional,
                                    org.apache.logging.log4j.*;resolution:=optional,
                                    org.slf4j;resolution:=optional,
                                    org.apache.commons.logging;resolution:=optional,
                                    com.hazelcast.client;resolution:=optional,
                                    com.hazelcast.hibernate;resolution:=optional,
                                    *
                                </Import-Package>
                                <Fragment-Host>com.hazelcast</Fragment-Host>
                                <!--<Embed-Dependency>*;scope=compile|runtime</Embed-Dependency>-->
                            </instructions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>false</addClasspath>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                        </manifest>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                    </archive>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-notice-and-license</id>
                        <phase>process-classes</phase>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.hazelcast</groupId>
            <artifactId>hazelcast</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${org.springframework.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${org.springframework.version}</version>
            <scope>provided</scope>
        </dependency>

        <!--TEST DEPENDENCIES-->
        <dependency>
            <groupId>com.hazelcast</groupId>
            <artifactId>hazelcast</artifactId>
            <scope>test</scope>
            <version>${project.parent.version}</version>
            <classifier>tests</classifier>
        </dependency>
        <dependency>
            <groupId>org.apache.maven</groupId>
            <artifactId>maven-artifact</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.hazelcast</groupId>
            <artifactId>hazelcast-archunit-rules</artifactId>
            <version>${project.parent.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
