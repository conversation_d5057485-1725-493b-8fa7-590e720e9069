<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>hazelcast-root</artifactId>
    <groupId>com.hazelcast</groupId>
    <version>5.3.7</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>hazelcast</artifactId>
  <name>hazelcast</name>
  <description>Core Hazelcast Module</description>
  <build>
    <plugins>
      <plugin>
        <groupId>pl.project13.maven</groupId>
        <artifactId>git-commit-id-plugin</artifactId>
        <version>${maven.git.commit.id.plugin.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>revision</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <useNativeGit>false</useNativeGit>
          <dotGitDirectory>${project.basedir}/.git</dotGitDirectory>
          <failOnNoGitDirectory>false</failOnNoGitDirectory>
          <abbrevLength>7</abbrevLength>
          <gitDescribe>
            <skip>true</skip>
          </gitDescribe>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
        <version>${maven.source.plugin.version}</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <forceCreation>true</forceCreation>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-maven-plugin</artifactId>
        <version>${kotlin.version}</version>
        <executions>
          <execution>
            <id>test-compile</id>
            <goals>
              <goal>test-compile</goal>
            </goals>
            <configuration>
              <sourceDirs>
                <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
              </sourceDirs>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
            <configuration>
              <excludes>
                <exclude>**/*.serialization.compatibility.binary</exclude>
              </excludes>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <archive>
            <index>true</index>
            <compress>true</compress>
            <manifest>
              <mainClass>${hazelcast.serverMainClass}</mainClass>
              <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
              <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
            </manifest>
            <manifestEntries>
              <Automatic-Module-Name>com.hazelcast.core</Automatic-Module-Name>
            </manifestEntries>
          </archive>
          <excludes>
            <exclude>**/*.html</exclude>
            <exclude>**/*.sh</exclude>
            <exclude>**/*.bat</exclude>
            <exclude>META-INF/services/javax.annotation.processing.Processor</exclude>
          </excludes>
          <forceCreation>true</forceCreation>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <finalName>${project.build.finalName}</finalName>
              <createSourcesJar>true</createSourcesJar>
              <shadeSourcesContent>true</shadeSourcesContent>
              <shadeTestJar>true</shadeTestJar>
              <artifactSet>
                <excludes>
                  <exclude>com.fasterxml.jackson.core:jackson-annotations</exclude>
                </excludes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>com.fasterxml.jackson</pattern>
                  <shadedPattern>${relocation.root}.com.fasterxml.jackson</shadedPattern>
                  <excludes>
                    <exclude>com.fasterxml.jackson.annotation.*</exclude>
                  </excludes>
                </relocation>
                <relocation>
                  <pattern>org.snakeyaml</pattern>
                  <shadedPattern>${relocation.root}.org.snakeyaml</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.jctools</pattern>
                  <shadedPattern>${relocation.root}.org.jctools</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>io.github.classgraph</pattern>
                  <shadedPattern>${relocation.root}.io.github.classgraph</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>nonapi.io.github.classgraph</pattern>
                  <shadedPattern>${relocation.root}.nonapi.io.github.classgraph</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.everit.json.schema</pattern>
                  <shadedPattern>${relocation.root}.org.everit.json.schema</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.json</pattern>
                  <shadedPattern>${relocation.root}.org.json</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.objenesis</pattern>
                  <shadedPattern>${relocation.root}.org.objenesis</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>com.zaxxer.hikari</pattern>
                  <shadedPattern>${relocation.root}.com.zaxxer.hikari</shadedPattern>
                </relocation>
              </relocations>
              <transformers>
                <transformer />
                <transformer />
              </transformers>
              <filters>
                <filter>
                  <artifact>org.jctools:jctools-core</artifact>
                  <includes>
                    <include>org/jctools/queues/*</include>
                    <include>org/jctools/util/*</include>
                  </includes>
                </filter>
                <filter>
                  <artifact>com.fasterxml.jackson.core:*</artifact>
                  <excludes>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                    <exclude>META-INF/versions/**</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>org.*:*</artifact>
                  <excludes>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>io.github.classgraph:*</artifact>
                  <excludes>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>com.github.erosb:*</artifact>
                  <excludes>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>**/module-info.class</exclude>
                  </excludes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>${maven.bundle.plugin.version}</version>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>package</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
            <configuration>
              <instructions>
                <Bundle-Activator>com.hazelcast.osgi.impl.Activator</Bundle-Activator>
                <Export-Package>com.hazelcast.*</Export-Package>
                <Import-Package>!org.junit,
                                    !com.hazelcast.*,
                                    !com.fasterxml.jackson.*,
                                    !org.snakeyaml.*,
                                    !io.github.classgraph.*,
                                    !org.everit.json.*,
                                    !org.json.*,
                                    !org.objenesis.*,
                                    sun.misc;resolution:=optional,
                                    javax.cache;resolution:=optional,
                                    javax.cache.*;resolution:=optional,
                                    org.apache.log4j;resolution:=optional,
                                    org.apache.log4j.spi;resolution:=optional,
                                    org.apache.logging.log4j;resolution:=optional,
                                    org.apache.logging.log4j.spi;resolution:=optional,
                                    org.slf4j;resolution:=optional,
                                    org.codehaus.groovy.jsr223;resolution:=optional,
                                    com.damnhandy.uri.template;resolution:=optional,
                                    org.apache.commons.validator.routines;resolution:=optional,
                                    com.google.re2j;resolution:=optional,
                                    com.codahale.metrics;resolution:=optional,
                                    com.codahale.metrics.health;resolution:=optional,
                                    io.micrometer.core.instrument;resolution:=optional,
                                    io.prometheus.client;resolution:=optional,
                                    javassist;resolution:=optional,
                                    javassist.bytecode;resolution:=optional,
                                    org.hibernate;resolution:=optional,
                                    org.hibernate.engine.jdbc.connections.spi;resolution:=optional,
                                    org.hibernate.service;resolution:=optional,
                                    org.hibernate.service.spi;resolution:=optional,
                                    net.openhft.affinity;resolution:=optional,
                                    *</Import-Package>
                <Bundle-Name>Hazelcast(Core)</Bundle-Name>
                <Embed-Transitive>true</Embed-Transitive>
              </instructions>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <version>${maven.antrun.plugin.version}</version>
        <executions>
          <execution>
            <id>copy-hazelcast-xml</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <copy />
                <copy />
                <copy />
                <copy />
                <copy />
                <copy />
                <copy />
                <copy />
              </target>
            </configuration>
          </execution>
          <execution>
            <id>copy-and-fill-hazelcast-assembly-xml</id>
            <phase>process-resources</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <copy>
                  <fileset>
                    <include />
                  </fileset>
                  <globmapper />
                  <filterset>
                    <filter />
                    <filter />
                    <filter />
                  </filterset>
                </copy>
                <copy>
                  <fileset>
                    <include />
                  </fileset>
                  <globmapper />
                  <filterset>
                    <filter />
                    <filter />
                    <filter />
                  </filterset>
                </copy>
              </target>
            </configuration>
          </execution>
          <execution>
            <id>repack</id>
            <phase>package</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <unzip />
                <copy />
                <jar />
              </target>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <executions>
          <execution>
            <phase>process-sources</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <resources>
                <resource>
                  <directory>src/main/template</directory>
                  <includes>
                    <include>**/*.java</include>
                  </includes>
                  <filtering>true</filtering>
                </resource>
              </resources>
              <outputDirectory>src/main/java</outputDirectory>
              <overwrite>true</overwrite>
            </configuration>
          </execution>
          <execution>
            <id>add-notice-and-license</id>
            <phase>process-classes</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-clean-plugin</artifactId>
        <version>3.2.0</version>
        <executions>
          <execution>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
            <configuration>
              <filesets>
                <fileset>
                  <directory>src/main/java</directory>
                  <includes>
                    <include>**/GeneratedBuildProperties.java</include>
                  </includes>
                </fileset>
              </filesets>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.36</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>info.picocli</groupId>
      <artifactId>picocli</artifactId>
      <version>4.7.3</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.jline</groupId>
      <artifactId>jline-terminal</artifactId>
      <version>3.23.0</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.jline</groupId>
      <artifactId>jline-reader</artifactId>
      <version>3.23.0</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.github.stefanbirkner</groupId>
      <artifactId>system-lambda</artifactId>
      <version>1.2.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>javax.jms</groupId>
      <artifactId>javax.jms-api</artifactId>
      <version>2.0.1</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>javax.cache</groupId>
      <artifactId>cache-tests</artifactId>
      <version>1.1.1</version>
      <classifier>tests</classifier>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>javax.cache</groupId>
      <artifactId>cache-tests</artifactId>
      <version>1.1.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>ch.qos.reload4j</groupId>
      <artifactId>reload4j</artifactId>
      <version>1.2.24</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
      <version>2.20.0</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <version>2.20.0</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>net.spy</groupId>
      <artifactId>spymemcached</artifactId>
      <version>2.12.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.atomikos</groupId>
      <artifactId>transactions-jdbc</artifactId>
      <version>3.9.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>transactions-jta</artifactId>
          <groupId>com.atomikos</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.geronimo.specs</groupId>
      <artifactId>geronimo-jta_1.1_spec</artifactId>
      <version>1.1.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.14</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>httpcore</artifactId>
          <groupId>org.apache.httpcomponents</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-codec</artifactId>
          <groupId>commons-codec</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.core</artifactId>
      <version>4.2.0</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.codehaus.groovy</groupId>
      <artifactId>groovy-all</artifactId>
      <version>2.4.21</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.ops4j.pax.exam</groupId>
      <artifactId>pax-exam-junit4</artifactId>
      <version>2.6.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>pax-exam-spi</artifactId>
          <groupId>org.ops4j.pax.exam</groupId>
        </exclusion>
        <exclusion>
          <artifactId>ops4j-base-lang</artifactId>
          <groupId>org.ops4j.base</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.ops4j.pax.exam</groupId>
      <artifactId>pax-exam-container-paxrunner</artifactId>
      <version>2.6.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>pax-exam</artifactId>
          <groupId>org.ops4j.pax.exam</groupId>
        </exclusion>
        <exclusion>
          <artifactId>pax-exam-container-remote</artifactId>
          <groupId>org.ops4j.pax.exam</groupId>
        </exclusion>
        <exclusion>
          <artifactId>pax-exam-container-rbc-client</artifactId>
          <groupId>org.ops4j.pax.exam</groupId>
        </exclusion>
        <exclusion>
          <artifactId>ops4j-base-net</artifactId>
          <groupId>org.ops4j.base</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-logging-api</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
        <exclusion>
          <artifactId>pax-exam-spi</artifactId>
          <groupId>org.ops4j.pax.exam</groupId>
        </exclusion>
        <exclusion>
          <artifactId>ops4j-base-lang</artifactId>
          <groupId>org.ops4j.base</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.ops4j.pax.exam</groupId>
      <artifactId>pax-exam-link-mvn</artifactId>
      <version>2.6.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.ops4j.pax.runner</groupId>
      <artifactId>pax-runner-no-jcl</artifactId>
      <version>1.8.6</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>javax.inject</groupId>
      <artifactId>javax.inject</artifactId>
      <version>1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hdrhistogram</groupId>
      <artifactId>HdrHistogram</artifactId>
      <version>2.1.12</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.github.tomakehurst</groupId>
      <artifactId>wiremock-jre8-standalone</artifactId>
      <version>2.35.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>apache-artemis</artifactId>
      <version>2.11.0</version>
      <type>pom</type>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>artemis-jms-client-all</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>servlet-api</artifactId>
          <groupId>javax.servlet</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-boot</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-server</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-dto</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-cli</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-jms-server</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-jms-client</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-ra</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-spring-integration</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-rest</artifactId>
          <groupId>org.apache.activemq.rest</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-web</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-core-client</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-amqp-protocol</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-stomp-protocol</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-openwire-protocol</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-hornetq-protocol</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-mqtt-protocol</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>activemq-artemis-native</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-jdbc-store</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-tools</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-website</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jboss-logmanager</artifactId>
          <groupId>org.jboss.logmanager</groupId>
        </exclusion>
        <exclusion>
          <artifactId>slf4j-jboss-logmanager</artifactId>
          <groupId>org.jboss.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>wildfly-common</artifactId>
          <groupId>org.wildfly.common</groupId>
        </exclusion>
        <exclusion>
          <artifactId>airline</artifactId>
          <groupId>io.airlift</groupId>
        </exclusion>
        <exclusion>
          <artifactId>activemq-client</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>activemq-openwire-legacy</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jetty-all</artifactId>
          <groupId>org.eclipse.jetty.aggregate</groupId>
        </exclusion>
        <exclusion>
          <artifactId>tomcat-servlet-api</artifactId>
          <groupId>org.apache.tomcat</groupId>
        </exclusion>
        <exclusion>
          <artifactId>qpid-jms-client</artifactId>
          <groupId>org.apache.qpid</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-console</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>activemq-branding</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-plugin</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-beanutils</artifactId>
          <groupId>commons-beanutils</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-website</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty-all</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>geronimo-json_1.0_spec</artifactId>
          <groupId>org.apache.geronimo.specs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>johnzon-core</artifactId>
          <groupId>org.apache.johnzon</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-junit</artifactId>
      <version>2.11.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jboss-logging</artifactId>
          <groupId>org.jboss.logging</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-commons</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>geronimo-jms_2.0_spec</artifactId>
          <groupId>org.apache.geronimo.specs</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-jms-server</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-jms-client</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-server</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-core-client</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>rabbitmq</artifactId>
      <version>1.18.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>testcontainers</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.rabbitmq.jms</groupId>
      <artifactId>rabbitmq-jms</artifactId>
      <version>2.8.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>amqp-client</artifactId>
          <groupId>com.rabbitmq</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <version>2.1.214</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>postgresql</artifactId>
      <version>1.18.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jdbc</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>mysql</artifactId>
      <version>1.18.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jdbc</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>mssqlserver</artifactId>
      <version>1.18.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jdbc</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>mariadb</artifactId>
      <version>1.18.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jdbc</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
      <version>42.7.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>checker-qual</artifactId>
          <groupId>org.checkerframework</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>8.0.30</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>protobuf-java</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib</artifactId>
      <version>1.8.21</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>kotlin-stdlib-common</artifactId>
          <groupId>org.jetbrains.kotlin</groupId>
        </exclusion>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.github.tomakehurst</groupId>
      <artifactId>wiremock</artifactId>
      <version>2.27.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jetty-server</artifactId>
          <groupId>org.eclipse.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jetty-servlet</artifactId>
          <groupId>org.eclipse.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jetty-servlets</artifactId>
          <groupId>org.eclipse.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jetty-webapp</artifactId>
          <groupId>org.eclipse.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jetty-proxy</artifactId>
          <groupId>org.eclipse.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jackson-databind</artifactId>
          <groupId>com.fasterxml.jackson.core</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xmlunit-core</artifactId>
          <groupId>org.xmlunit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xmlunit-legacy</artifactId>
          <groupId>org.xmlunit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xmlunit-placeholders</artifactId>
          <groupId>org.xmlunit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>json-path</artifactId>
          <groupId>com.jayway.jsonpath</groupId>
        </exclusion>
        <exclusion>
          <artifactId>asm</artifactId>
          <groupId>org.ow2.asm</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jopt-simple</artifactId>
          <groupId>net.sf.jopt-simple</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-lang3</artifactId>
          <groupId>org.apache.commons</groupId>
        </exclusion>
        <exclusion>
          <artifactId>handlebars</artifactId>
          <groupId>com.github.jknack</groupId>
        </exclusion>
        <exclusion>
          <artifactId>handlebars-helpers</artifactId>
          <groupId>com.github.jknack</groupId>
        </exclusion>
        <exclusion>
          <artifactId>zjsonpatch</artifactId>
          <groupId>com.flipkart.zjsonpatch</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-fileupload</artifactId>
          <groupId>commons-fileupload</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.hazelcast</groupId>
      <artifactId>hazelcast-archunit-rules</artifactId>
      <version>5.3.7</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>archunit</artifactId>
          <groupId>com.tngtech.archunit</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.fabric8</groupId>
      <artifactId>kubernetes-server-mock</artifactId>
      <version>6.5.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>mockwebserver</artifactId>
          <groupId>io.fabric8</groupId>
        </exclusion>
        <exclusion>
          <artifactId>servicecatalog-client</artifactId>
          <groupId>io.fabric8</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>javax.cache</groupId>
      <artifactId>cache-api</artifactId>
      <version>1.1.1</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>hamcrest-core</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
      </exclusions>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-library</artifactId>
      <version>1.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>hamcrest-core</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.24.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>3.6.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-api-mockito2</artifactId>
      <version>2.0.9</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>powermock-api-support</artifactId>
          <groupId>org.powermock</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.powermock</groupId>
      <artifactId>powermock-module-junit4</artifactId>
      <version>2.0.9</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>powermock-module-junit4-common</artifactId>
          <groupId>org.powermock</groupId>
        </exclusion>
        <exclusion>
          <artifactId>hamcrest-core</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.reflections</groupId>
      <artifactId>reflections</artifactId>
      <version>0.9.10</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>slf4j-api</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>slf4j-simple</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>javassist</artifactId>
          <groupId>org.javassist</groupId>
        </exclusion>
        <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>annotations</artifactId>
      <version>3.0.1u2</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>net.bytebuddy</groupId>
      <artifactId>byte-buddy</artifactId>
      <version>1.14.4</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>net.bytebuddy</groupId>
      <artifactId>byte-buddy-agent</artifactId>
      <version>1.14.4</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>nl.jqno.equalsverifier</groupId>
      <artifactId>equalsverifier</artifactId>
      <version>3.8</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.tomcat.embed</groupId>
      <artifactId>tomcat-embed-core</artifactId>
      <version>10.0.23</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>tomcat-annotations-api</artifactId>
          <groupId>org.apache.tomcat</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <properties>
    <wiremock.version>2.27.2</wiremock.version>
    <javax.inject.version>1</javax.inject.version>
    <pax.exam.version>2.6.0</pax.exam.version>
    <pax.runner.version>1.8.6</pax.runner.version>
    <servlet.api.version>3.0.1</servlet.api.version>
    <main.basedir>${project.parent.basedir}</main.basedir>
    <jsp.api.version>2.2.1</jsp.api.version>
  </properties>
</project>
