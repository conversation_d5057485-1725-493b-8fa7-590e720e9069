<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>hazelcast-root</artifactId>
    <groupId>com.hazelcast</groupId>
    <version>5.5.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>hazelcast</artifactId>
  <name>hazelcast</name>
  <description>Core Hazelcast Module</description>
  <build>
    <plugins>
      <plugin>
        <groupId>pl.project13.maven</groupId>
        <artifactId>git-commit-id-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>revision</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <useNativeGit>false</useNativeGit>
          <dotGitDirectory>${project.basedir}/.git</dotGitDirectory>
          <failOnNoGitDirectory>false</failOnNoGitDirectory>
          <abbrevLength>7</abbrevLength>
          <gitDescribe>
            <skip>true</skip>
          </gitDescribe>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-maven-plugin</artifactId>
        <version>${kotlin.version}</version>
        <executions>
          <execution>
            <id>test-compile</id>
            <goals>
              <goal>test-compile</goal>
            </goals>
            <configuration>
              <sourceDirs>
                <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
              </sourceDirs>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <executions>
          <execution>
            <id>usercodedeployment-child-parent</id>
            <phase>process-test-classes</phase>
            <goals>
              <goal>single</goal>
            </goals>
            <configuration>
              <attach>false</attach>
              <outputDirectory>${project.build.directory}/ChildParent</outputDirectory>
              <descriptors>
                <descriptor>${project.basedir}/src/test/assembly/usercodedeployment-child-parent.xml</descriptor>
              </descriptors>
            </configuration>
          </execution>
          <execution>
            <id>usercodedeployment-entry-processor-with-anonymous-and-inner</id>
            <phase>process-test-classes</phase>
            <goals>
              <goal>single</goal>
            </goals>
            <configuration>
              <attach>false</attach>
              <outputDirectory>${project.build.directory}/EntryProcessorWithAnonymousAndInner</outputDirectory>
              <descriptors>
                <descriptor>${project.basedir}/src/test/assembly/usercodedeployment-entry-processor-with-anonymous-and-inner.xml</descriptor>
              </descriptors>
            </configuration>
          </execution>
          <execution>
            <id>usercodedeployment-incrementing-entry-processor</id>
            <phase>process-test-classes</phase>
            <goals>
              <goal>single</goal>
            </goals>
            <configuration>
              <attach>false</attach>
              <outputDirectory>${project.build.directory}/IncrementingEntryProcessor</outputDirectory>
              <descriptors>
                <descriptor>${project.basedir}/src/test/assembly/usercodedeployment-incrementing-entry-processor.xml</descriptor>
              </descriptors>
            </configuration>
          </execution>
          <execution>
            <id>usercodedeployment-incrementing-entry-processor-conflicting</id>
            <phase>process-test-classes</phase>
            <goals>
              <goal>single</goal>
            </goals>
            <configuration>
              <attach>false</attach>
              <outputDirectory>${project.build.directory}/IncrementingEntryProcessorConflicting</outputDirectory>
              <descriptors>
                <descriptor>${project.basedir}/src/test/assembly/usercodedeployment-incrementing-entry-processor-conflicting.xml</descriptor>
              </descriptors>
            </configuration>
          </execution>
          <execution>
            <id>usercodedeployment-shaded-classes</id>
            <phase>process-test-classes</phase>
            <goals>
              <goal>single</goal>
            </goals>
            <configuration>
              <attach>false</attach>
              <outputDirectory>${project.build.directory}/ShadedClasses</outputDirectory>
              <descriptors>
                <descriptor>${project.basedir}/src/test/assembly/usercodedeployment-shaded-classes.xml</descriptor>
              </descriptors>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
            <configuration>
              <excludes>
                <exclude>**/*.serialization.compatibility.binary</exclude>
              </excludes>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <archive>
            <manifest>
              <mainClass>${hazelcast.serverMainClass}</mainClass>
              <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
              <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
            </manifest>
            <manifestEntries>
              <Automatic-Module-Name>com.hazelcast.core</Automatic-Module-Name>
            </manifestEntries>
          </archive>
          <excludes>
            <exclude>**/*.html</exclude>
            <exclude>**/*.sh</exclude>
            <exclude>**/*.bat</exclude>
            <exclude>META-INF/services/javax.annotation.processing.Processor</exclude>
          </excludes>
          <forceCreation>true</forceCreation>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <finalName>${project.build.finalName}</finalName>
              <createSourcesJar>${maven.shade.createSourcesJar}</createSourcesJar>
              <shadeSourcesContent>${maven.shade.createSourcesJar}</shadeSourcesContent>
              <shadeTestJar>true</shadeTestJar>
              <artifactSet>
                <excludes>
                  <exclude>com.fasterxml.jackson.core:jackson-annotations</exclude>
                </excludes>
              </artifactSet>
              <relocations>
                <relocation>
                  <pattern>com.fasterxml.jackson</pattern>
                  <shadedPattern>${relocation.root}.com.fasterxml.jackson</shadedPattern>
                  <excludes>
                    <exclude>com.fasterxml.jackson.annotation.*</exclude>
                  </excludes>
                </relocation>
                <relocation>
                  <pattern>com/fasterxml/jackson/core</pattern>
                  <shadedPattern>${relocation.dir}/com/fasterxml/jackson/core</shadedPattern>
                  <rawString>true</rawString>
                </relocation>
                <relocation>
                  <pattern>org.snakeyaml</pattern>
                  <shadedPattern>${relocation.root}.org.snakeyaml</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.jctools</pattern>
                  <shadedPattern>${relocation.root}.org.jctools</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>io.github.classgraph</pattern>
                  <shadedPattern>${relocation.root}.io.github.classgraph</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>nonapi.io.github.classgraph</pattern>
                  <shadedPattern>${relocation.root}.nonapi.io.github.classgraph</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.everit.json.schema</pattern>
                  <shadedPattern>${relocation.root}.org.everit.json.schema</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.json</pattern>
                  <shadedPattern>${relocation.root}.org.json</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.objenesis</pattern>
                  <shadedPattern>${relocation.root}.org.objenesis</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>com.zaxxer.hikari</pattern>
                  <shadedPattern>${relocation.root}.com.zaxxer.hikari</shadedPattern>
                </relocation>
              </relocations>
              <transformers>
                <transformer />
                <transformer />
              </transformers>
              <filters>
                <filter>
                  <artifact>org.jctools:jctools-core</artifact>
                  <includes>
                    <include>org/jctools/queues/*</include>
                    <include>org/jctools/util/*</include>
                  </includes>
                </filter>
                <filter>
                  <artifact>com.fasterxml.jackson.core:*</artifact>
                  <excludes>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>org.*:*</artifact>
                  <excludes>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>io.github.classgraph:*</artifact>
                  <excludes>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>com.github.erosb:*</artifact>
                  <excludes>
                    <exclude>META-INF/MANIFEST.MF</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>**/module-info.class</exclude>
                  </excludes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>package</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
            <configuration>
              <instructions>
                <Bundle-Activator>com.hazelcast.osgi.impl.Activator</Bundle-Activator>
                <Export-Package>com.hazelcast.*</Export-Package>
                <Import-Package>!org.junit,
                                    !com.hazelcast.*,
                                    !com.fasterxml.jackson.*,
                                    !com.zaxxer.hikari.*,
                                    !org.snakeyaml.*,
                                    !io.github.classgraph.*,
                                    !org.everit.json.*,
                                    !org.json.*,
                                    !org.objenesis.*,
                                    sun.misc;resolution:=optional,
                                    javax.cache;resolution:=optional,
                                    javax.cache.*;resolution:=optional,
                                    org.apache.log4j;resolution:=optional,
                                    org.apache.log4j.spi;resolution:=optional,
                                    org.apache.logging.log4j;resolution:=optional,
                                    org.apache.logging.log4j.spi;resolution:=optional,
                                    org.slf4j;resolution:=optional,
                                    org.codehaus.groovy.jsr223;resolution:=optional,
                                    com.damnhandy.uri.template;resolution:=optional,
                                    org.apache.commons.validator.routines;resolution:=optional,
                                    com.google.re2j;resolution:=optional,
                                    com.codahale.metrics;resolution:=optional,
                                    com.codahale.metrics.health;resolution:=optional,
                                    io.micrometer.core.instrument;resolution:=optional,
                                    io.prometheus.client;resolution:=optional,
                                    javassist;resolution:=optional,
                                    javassist.bytecode;resolution:=optional,
                                    org.hibernate;resolution:=optional,
                                    org.hibernate.engine.jdbc.connections.spi;resolution:=optional,
                                    org.hibernate.service;resolution:=optional,
                                    org.hibernate.service.spi;resolution:=optional,
                                    net.openhft.affinity;resolution:=optional,
                                    jakarta.jms;resolution:=optional,
                                    org.osgi.framework;resolution:=optional,
                                    *</Import-Package>
                <Bundle-Name>Hazelcast(Core)</Bundle-Name>
                <Embed-Transitive>true</Embed-Transitive>
              </instructions>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <execution>
            <id>copy-hazelcast-xml</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <copy />
                <copy />
                <copy />
                <copy />
                <copy />
                <copy />
                <copy />
                <copy />
              </target>
            </configuration>
          </execution>
          <execution>
            <id>copy-and-fill-hazelcast-assembly-xml</id>
            <phase>process-resources</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <copy>
                  <fileset>
                    <include />
                  </fileset>
                  <globmapper />
                  <filterset>
                    <filter />
                    <filter />
                    <filter />
                  </filterset>
                </copy>
                <copy>
                  <fileset>
                    <include />
                  </fileset>
                  <globmapper />
                  <filterset>
                    <filter />
                    <filter />
                    <filter />
                  </filterset>
                </copy>
              </target>
            </configuration>
          </execution>
          <execution>
            <id>repack</id>
            <phase>package</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <unzip />
                <copy />
                <jar />
              </target>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <executions>
          <execution>
            <phase>process-sources</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <resources>
                <resource>
                  <directory>src/main/template</directory>
                  <includes>
                    <include>**/*.java</include>
                  </includes>
                  <filtering>true</filtering>
                </resource>
              </resources>
              <outputDirectory>src/main/java</outputDirectory>
              <overwrite>true</overwrite>
            </configuration>
          </execution>
          <execution>
            <id>add-notice-and-license</id>
            <phase>process-classes</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-clean-plugin</artifactId>
        <executions>
          <execution>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
            <configuration>
              <filesets>
                <fileset>
                  <directory>src/main/java</directory>
                  <includes>
                    <include>**/GeneratedBuildProperties.java</include>
                  </includes>
                </fileset>
              </filesets>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <executions>
          <execution>
            <id>usercodedeployment-sample</id>
            <goals>
              <goal>testCompile</goal>
            </goals>
            <configuration>
              <compileSourceRoots>${project.basedir}/src/test/class/sample</compileSourceRoots>
              <outputDirectory>${project.build.directory}/sample</outputDirectory>
            </configuration>
          </execution>
          <execution>
            <id>usercodedeployment-child-parent</id>
            <goals>
              <goal>testCompile</goal>
            </goals>
            <configuration>
              <compileSourceRoots>${project.basedir}/src/test/class/ChildParent</compileSourceRoots>
              <outputDirectory>${project.build.directory}/ChildParent</outputDirectory>
            </configuration>
          </execution>
          <execution>
            <id>usercodedeployment-entry-processor-with-anonymous-and-inner</id>
            <goals>
              <goal>testCompile</goal>
            </goals>
            <configuration>
              <compileSourceRoots>${project.basedir}/src/test/class/EntryProcessorWithAnonymousAndInner</compileSourceRoots>
              <outputDirectory>${project.build.directory}/EntryProcessorWithAnonymousAndInner</outputDirectory>
            </configuration>
          </execution>
          <execution>
            <id>usercodedeployment-incrementing-entry-processor</id>
            <goals>
              <goal>testCompile</goal>
            </goals>
            <configuration>
              <compileSourceRoots>${project.basedir}/src/test/class/IncrementingEntryProcessor</compileSourceRoots>
              <outputDirectory>${project.build.directory}/IncrementingEntryProcessor</outputDirectory>
            </configuration>
          </execution>
          <execution>
            <id>usercodedeployment-incrementing-entry-processor-conflicting</id>
            <goals>
              <goal>testCompile</goal>
            </goals>
            <configuration>
              <compileSourceRoots>${project.basedir}/src/test/class/IncrementingEntryProcessorConflicting</compileSourceRoots>
              <outputDirectory>${project.build.directory}/IncrementingEntryProcessorConflicting</outputDirectory>
            </configuration>
          </execution>
          <execution>
            <id>usercodedeployment-shaded-classes</id>
            <goals>
              <goal>testCompile</goal>
            </goals>
            <configuration>
              <compileSourceRoots>${project.basedir}/src/test/class/ShadedClasses</compileSourceRoots>
              <outputDirectory>${project.build.directory}/ShadedClasses</outputDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>2.0.13</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>info.picocli</groupId>
      <artifactId>picocli</artifactId>
      <version>4.7.6</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.jline</groupId>
      <artifactId>jline-terminal</artifactId>
      <version>3.26.2</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.jline</groupId>
      <artifactId>jline-reader</artifactId>
      <version>3.26.2</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>com.github.stefanbirkner</groupId>
      <artifactId>system-lambda</artifactId>
      <version>1.2.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>jakarta.jms</groupId>
      <artifactId>jakarta.jms-api</artifactId>
      <version>3.1.0</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>javax.cache</groupId>
      <artifactId>cache-tests</artifactId>
      <version>1.1.1</version>
      <classifier>tests</classifier>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-core</artifactId>
      <version>2.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>hamcrest</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>javax.cache</groupId>
      <artifactId>cache-tests</artifactId>
      <version>1.1.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>ch.qos.reload4j</groupId>
      <artifactId>reload4j</artifactId>
      <version>1.2.25</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-api</artifactId>
      <version>2.23.1</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.apache.logging.log4j</groupId>
      <artifactId>log4j-core</artifactId>
      <version>2.23.1</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>net.spy</groupId>
      <artifactId>spymemcached</artifactId>
      <version>2.12.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.atomikos</groupId>
      <artifactId>transactions-jdbc</artifactId>
      <version>3.9.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>transactions-jta</artifactId>
          <groupId>com.atomikos</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>javax.transaction</groupId>
      <artifactId>javax.transaction-api</artifactId>
      <version>1.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.core</artifactId>
      <version>4.2.0</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.codehaus.groovy</groupId>
      <artifactId>groovy-all</artifactId>
      <version>2.4.21</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.ops4j.pax.exam</groupId>
      <artifactId>pax-exam-junit4</artifactId>
      <version>2.6.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>pax-exam-spi</artifactId>
          <groupId>org.ops4j.pax.exam</groupId>
        </exclusion>
        <exclusion>
          <artifactId>ops4j-base-lang</artifactId>
          <groupId>org.ops4j.base</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.ops4j.pax.exam</groupId>
      <artifactId>pax-exam-container-paxrunner</artifactId>
      <version>2.6.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>pax-exam</artifactId>
          <groupId>org.ops4j.pax.exam</groupId>
        </exclusion>
        <exclusion>
          <artifactId>pax-exam-container-remote</artifactId>
          <groupId>org.ops4j.pax.exam</groupId>
        </exclusion>
        <exclusion>
          <artifactId>pax-exam-container-rbc-client</artifactId>
          <groupId>org.ops4j.pax.exam</groupId>
        </exclusion>
        <exclusion>
          <artifactId>ops4j-base-net</artifactId>
          <groupId>org.ops4j.base</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-logging-api</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
        <exclusion>
          <artifactId>pax-exam-spi</artifactId>
          <groupId>org.ops4j.pax.exam</groupId>
        </exclusion>
        <exclusion>
          <artifactId>ops4j-base-lang</artifactId>
          <groupId>org.ops4j.base</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.ops4j.pax.exam</groupId>
      <artifactId>pax-exam-link-mvn</artifactId>
      <version>2.6.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.ops4j.pax.runner</groupId>
      <artifactId>pax-runner-no-jcl</artifactId>
      <version>1.8.6</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>javax.inject</groupId>
      <artifactId>javax.inject</artifactId>
      <version>1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.hdrhistogram</groupId>
      <artifactId>HdrHistogram</artifactId>
      <version>2.2.2</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-jakarta-server</artifactId>
      <version>2.35.0</version>
      <type>pom</type>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>artemis-core-client</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-jakarta-client</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-server</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-journal</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-commons</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-jakarta-service-extensions</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jakarta.transaction-api</artifactId>
          <groupId>jakarta.transaction</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-junit</artifactId>
      <version>2.35.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>artemis-jms-client</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>servlet-api</artifactId>
          <groupId>javax.servlet</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-junit-commons</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-jms-server</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-server</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-core-client</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
        <exclusion>
          <artifactId>artemis-commons</artifactId>
          <groupId>org.apache.activemq</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>rabbitmq</artifactId>
      <version>1.19.8</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>testcontainers</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.rabbitmq.jms</groupId>
      <artifactId>rabbitmq-jms</artifactId>
      <version>3.2.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>amqp-client</artifactId>
          <groupId>com.rabbitmq</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <version>2.2.224</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>postgresql</artifactId>
      <version>1.19.8</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jdbc</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>mysql</artifactId>
      <version>1.19.8</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jdbc</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>mssqlserver</artifactId>
      <version>1.19.8</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jdbc</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>mariadb</artifactId>
      <version>1.19.8</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jdbc</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
      <version>42.7.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>checker-qual</artifactId>
          <groupId>org.checkerframework</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
      <version>9.0.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>protobuf-java</artifactId>
          <groupId>com.google.protobuf</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.microsoft.sqlserver</groupId>
      <artifactId>mssql-jdbc</artifactId>
      <version>12.6.3.jre11</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>oracle-xe</artifactId>
      <version>1.19.8</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jdbc</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>oracle-free</artifactId>
      <version>1.19.8</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jdbc</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.oracle.database.jdbc</groupId>
      <artifactId>ojdbc11</artifactId>
      <version>23.4.0.24.05</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib</artifactId>
      <version>2.0.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>annotations</artifactId>
          <groupId>org.jetbrains</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.wiremock</groupId>
      <artifactId>wiremock-standalone</artifactId>
      <version>3.8.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.hazelcast</groupId>
      <artifactId>hazelcast-archunit-rules</artifactId>
      <version>5.5.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>archunit</artifactId>
          <groupId>com.tngtech.archunit</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.fabric8</groupId>
      <artifactId>kubernetes-server-mock</artifactId>
      <version>6.13.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>mockwebserver</artifactId>
          <groupId>io.fabric8</groupId>
        </exclusion>
        <exclusion>
          <artifactId>servicecatalog-client</artifactId>
          <groupId>io.fabric8</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.maven</groupId>
      <artifactId>maven-resolver-provider</artifactId>
      <version>3.9.8</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>maven-model</artifactId>
          <groupId>org.apache.maven</groupId>
        </exclusion>
        <exclusion>
          <artifactId>maven-model-builder</artifactId>
          <groupId>org.apache.maven</groupId>
        </exclusion>
        <exclusion>
          <artifactId>maven-repository-metadata</artifactId>
          <groupId>org.apache.maven</groupId>
        </exclusion>
        <exclusion>
          <artifactId>maven-resolver-api</artifactId>
          <groupId>org.apache.maven.resolver</groupId>
        </exclusion>
        <exclusion>
          <artifactId>maven-resolver-spi</artifactId>
          <groupId>org.apache.maven.resolver</groupId>
        </exclusion>
        <exclusion>
          <artifactId>maven-resolver-util</artifactId>
          <groupId>org.apache.maven.resolver</groupId>
        </exclusion>
        <exclusion>
          <artifactId>maven-resolver-impl</artifactId>
          <groupId>org.apache.maven.resolver</groupId>
        </exclusion>
        <exclusion>
          <artifactId>plexus-utils</artifactId>
          <groupId>org.codehaus.plexus</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.16.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.17.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>javax.cache</groupId>
      <artifactId>cache-api</artifactId>
      <version>1.1.1</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>5.10.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-platform-engine</artifactId>
          <groupId>org.junit.platform</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-api</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.vintage</groupId>
      <artifactId>junit-vintage-engine</artifactId>
      <version>5.10.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-platform-engine</artifactId>
          <groupId>org.junit.platform</groupId>
        </exclusion>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <version>5.10.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-jupiter-api</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit-pioneer</groupId>
      <artifactId>junit-pioneer</artifactId>
      <version>2.2.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-platform-launcher</artifactId>
          <groupId>org.junit.platform</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter-api</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.26.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>uk.org.webcompere</groupId>
      <artifactId>system-stubs-jupiter</artifactId>
      <version>2.1.6</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>system-stubs-core</artifactId>
          <groupId>uk.org.webcompere</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>uk.org.webcompere</groupId>
      <artifactId>system-stubs-junit4</artifactId>
      <version>2.1.6</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>system-stubs-core</artifactId>
          <groupId>uk.org.webcompere</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>5.12.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <version>5.12.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-jupiter-api</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.reflections</groupId>
      <artifactId>reflections</artifactId>
      <version>0.10.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>slf4j-api</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>slf4j-simple</artifactId>
          <groupId>org.slf4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>javassist</artifactId>
          <groupId>org.javassist</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.github.spotbugs</groupId>
      <artifactId>spotbugs-annotations</artifactId>
      <version>4.8.6</version>
      <scope>provided</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>net.bytebuddy</groupId>
      <artifactId>byte-buddy</artifactId>
      <version>1.14.18</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>net.bytebuddy</groupId>
      <artifactId>byte-buddy-agent</artifactId>
      <version>1.14.18</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>nl.jqno.equalsverifier</groupId>
      <artifactId>equalsverifier</artifactId>
      <version>3.16.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.tomcat.embed</groupId>
      <artifactId>tomcat-embed-core</artifactId>
      <version>10.1.20</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>tomcat-annotations-api</artifactId>
          <groupId>org.apache.tomcat</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <properties>
    <servlet.api.version>3.0.1</servlet.api.version>
    <javax.inject.version>1</javax.inject.version>
    <main.basedir>${project.parent.basedir}</main.basedir>
    <pax.exam.version>2.6.0</pax.exam.version>
    <pax.runner.version>1.8.6</pax.runner.version>
    <jsp.api.version>2.2.1</jsp.api.version>
  </properties>
</project>
