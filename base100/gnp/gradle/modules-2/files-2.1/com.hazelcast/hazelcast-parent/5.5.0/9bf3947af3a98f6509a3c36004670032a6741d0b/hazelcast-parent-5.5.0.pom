<!--
  ~ Copyright (c) 2008-2024, Hazelcast, Inc. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                      http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.hazelcast</groupId>
    <artifactId>hazelcast-parent</artifactId>
    <packaging>pom</packaging>
    <version>5.5.0</version>

    <name>Hazelcast Parent POM</name>
    <description>Provides a base configuration for Hazelcast Platform builds</description>
    <url>http://www.hazelcast.com/</url>

    <scm>
        <connection>scm:git:git://github.com/hazelcast/hazelcast.git</connection>
        <developerConnection>scm:git:**************:hazelcast/hazelcast.git</developerConnection>
        <url>https://github.com/hazelcast/hazelcast/</url>
    </scm>

    <issueManagement>
        <system>Github</system>
        <url>https://github.com/hazelcast/hazelcast/issues</url>
    </issueManagement>

    <developers>
        <developer>
            <id>hazelcast-team</id>
            <name>Hazelcast team</name>
            <email><EMAIL></email>
            <organization>Hazelcast, Inc.</organization>
            <organizationUrl>https://hazelcast.com</organizationUrl>
        </developer>
    </developers>

    <organization>
        <name>Hazelcast, Inc.</name>
        <url>http://www.hazelcast.com/</url>
    </organization>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <properties>
        <jdk.version>17</jdk.version>
        <hazelcast.module.name>com.hazelcast.core</hazelcast.module.name>
        <relocation.root>com.hazelcast.shaded</relocation.root>
        <relocation.dir>com/hazelcast/shaded</relocation.dir>

        <maven.build.timestamp.format>yyyyMMdd</maven.build.timestamp.format>

        <!--used also as a dependency version in hazelcast-build-utils module-->
        <maven.shade.plugin.version>3.6.0</maven.shade.plugin.version>

        <os.maven.plugin.version>1.7.1</os.maven.plugin.version>

        <antlr4.version>4.9.3</antlr4.version>
        <avro.version>1.11.3</avro.version>
        <aws.sdk.version>1.12.760</aws.sdk.version>
        <aws.sdk2.version>2.26.19</aws.sdk2.version>
        <calcite.version>1.35.0</calcite.version>
        <classgraph.version>4.8.174</classgraph.version>
        <debezium.version>1.9.8.Final</debezium.version>
        <new.debezium.version>2.6.0.Final</new.debezium.version>
        <grpc.version>1.60.0</grpc.version>
        <hadoop.version>3.4.0</hadoop.version>
        <h2.version>2.2.224</h2.version>
        <gson.version>2.10.1</gson.version>
        <jackson.version>2.17.2</jackson.version>
        <jackson.mapper.asl.version>1.9.14.jdk17-redhat-00001</jackson.mapper.asl.version>
        <jaxb.version>2.3.1</jaxb.version>
        <jline.version>3.26.2</jline.version>
        <jms.api.version>3.1.0</jms.api.version>
        <json-surfer.version>0.11</json-surfer.version>
        <jsr107.api.version>1.1.1</jsr107.api.version> <!-- JCache -->
        <!--
        Confluent depends on Kafka, and consideration should be given when upgrading
        https://docs.confluent.io/platform/current/installation/versions-interoperability.html#cp-and-apache-ak-compatibility
        -->
        <kafka.version>3.6.1</kafka.version>
        <kotlin.version>2.0.0</kotlin.version>
        <reload4j.version>1.2.25</reload4j.version>
        <mssql.version>12.6.3.jre11</mssql.version>
        <netty.version>4.1.111.Final</netty.version>
        <objenesis.version>3.3</objenesis.version>
        <oracle.version>23.4.0.24.05</oracle.version>
        <osgi.version>4.2.0</osgi.version>
        <parquet.version>1.14.1</parquet.version>
        <picocli.version>4.7.6</picocli.version>
        <postgresql.version>42.7.3</postgresql.version>
        <prometheus.version>0.20.0</prometheus.version>

        <!--
        Protobuf libraries for each language have own major version, but share minor and patch
        See https://developers.google.com/protocol-buffers/docs/news/2022-05-06
        -->
        <protobuf.version>25.3</protobuf.version>
        <java.protobuf.version>3.${protobuf.version}</java.protobuf.version>
        <python.protobuf.version>4.${protobuf.version}</python.protobuf.version>

        <scala.version>2.13</scala.version>
        <slf4j.version>2.0.13</slf4j.version>
        <spring.version>4.3.0.RELEASE</spring.version>
        <snakeyaml.version>2.2</snakeyaml.version>
        <snakeyaml.engine.version>2.7</snakeyaml.engine.version>
        <mongodb.version>4.11.1</mongodb.version>

        <!-- test dependencies -->
        <activemq-artemis.version>2.35.0</activemq-artemis.version>
        <assertj.version>3.26.3</assertj.version>
        <atomikos.version>3.9.3</atomikos.version>
        <bytebuddy.version>1.14.18</bytebuddy.version>
        <commons-codec.version>1.17.0</commons-codec.version>
        <commons-io.version>2.16.1</commons-io.version>
        <commons-lang3.version>3.14.0</commons-lang3.version>
        <!--
        Confluent depends on Kafka, and consideration should be given when upgrading
        https://docs.confluent.io/platform/current/installation/versions-interoperability.html#cp-and-apache-ak-compatibility
        -->
        <confluent.version>7.6.1</confluent.version>
        <felix.utils.version>1.11.6</felix.utils.version>
        <http.core.version>4.4.16</http.core.version>
        <http.client.version>4.5.14</http.client.version>
        <jsr107.tck.version>1.1.1</jsr107.tck.version>
        <wiremock.version>3.8.0</wiremock.version>
        <reflections.version>0.10.2</reflections.version>
        <testcontainers.version>1.19.8</testcontainers.version>
        <jna.version>5.14.0</jna.version>
        <!--  When updating archunit.version, check/remove Java version assumptions in ArchUnitTestSupport -->
        <archunit.version>1.3.0</archunit.version>
        <errorprone.version>2.28.0</errorprone.version>
        <awaitility.version>4.2.1</awaitility.version>
        <hikari.version>5.1.0</hikari.version>
        <checkstyle.version>10.17.0</checkstyle.version>
        <hazelcast-hibernate53.version>5.2.0</hazelcast-hibernate53.version>

        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <!--
        License plugin is not thread-safe, leads to flaky failures when checking licenses, so it is skipped by default.
        to run the build with license plugin run with `-T1 -Dlicense.skipAddThirdParty=false`.
        The `-T1` parameter disables the parallel build.
        -->
        <license.skipAddThirdParty>true</license.skipAddThirdParty>

        <!-- 
        Sources JAR properties
        Typically `sources` are only required for release
        -->
        <!-- https://maven.apache.org/plugins/maven-source-plugin/jar-mojo.html#skipSource -->
        <maven.source.skip>true</maven.source.skip>
        <!-- Controls if sources are included in shaded JAR -->
        <maven.shade.createSourcesJar>false</maven.shade.createSourcesJar>

        <!-- https://github.com/hazelcast/attribution-maven-plugin/blob/main/README.md#configuration-properties -->
        <attribution.skip>true</attribution.skip>

        <main.basedir>..</main.basedir>
    </properties>

    <distributionManagement>
        <repository>
            <id>release-repository</id>
            <!-- Irrespective of this configuration, nexus-staging-maven-plugin
            will *also* deploy to it's own staging destination, configured in
            //project//plugin[artifactId='nexus-staging-maven-plugin']/configuration/nexusUrl
            (i.e. Maven central) -->
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2</url>
        </repository>
        <snapshotRepository>
            <id>snapshot-repository</id>
            <name>Maven2 Snapshot Repository</name>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
            <uniqueVersion>false</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>

    <dependencyManagement>
        <dependencies>
            <!--
            Pin dependencies to particular version.

            This doesn't add dependencies for modules, but if any of the modules uses any listed dependency, either
            directly or transitively, it will use the version specified here.
            This ensures that all jars with dependencies use the same version of a particular dependency, both for tests
            and for inclusion to the jar with dependencies.

            The enforcer plugin checks that no two modules have a dependency conflict, if they do simply add an entry
            with conflicting dependency here, using the higher version.
            -->

            <!-- Import versions from bom - avoids to importing many modules for certain dependencies, e.g. Jackson -->
            <dependency>
                <groupId>com.fasterxml.jackson</groupId>
                <artifactId>jackson-bom</artifactId>
                <version>${jackson.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-bom</artifactId>
                <version>9.4.55.v20240627</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>${netty.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-bom</artifactId>
                <version>${java.protobuf.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-bom</artifactId>
                <version>${grpc.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-client</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <!-- The following dependencies are not needed for our use of Hadoop -->
                    <exclusion>
                        <groupId>ch.qos.reload4j</groupId>
                        <artifactId>reload4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-reload4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-servlet</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-webapp</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet.jsp</groupId>
                        <artifactId>jsp-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun.jersey</groupId>
                        <artifactId>jersey-servlet</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.jline</groupId>
                        <artifactId>jline</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hadoop</groupId>
                        <artifactId>hadoop-yarn-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hadoop</groupId>
                        <artifactId>hadoop-yarn-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hadoop</groupId>
                        <artifactId>hadoop-yarn-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.kerby</groupId>
                        <artifactId>kerb-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.kerby</groupId>
                        <artifactId>kerb-simplekdc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.curator</groupId>
                        <artifactId>curator-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.curator</groupId>
                        <artifactId>curator-recipes</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.curator</groupId>
                        <artifactId>curator-framework</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro-mapred</artifactId>
                <version>${avro.version}</version>
                <exclusions>
                    <!-- The following dependencies are not needed for our use of Avro -->
                    <exclusion>
                        <groupId>org.apache.avro</groupId>
                        <artifactId>avro-ipc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.avro</groupId>
                        <artifactId>avro-ipc-jetty</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.parquet</groupId>
                <artifactId>parquet-avro</artifactId>
                <version>${parquet.version}</version>
                <exclusions>
                    <!--
                    The following library is not required, it only contains annotations and doclets
                    It also requires JDK11 since version 0.14
                    -->
                    <exclusion>
                        <groupId>org.apache.yetus</groupId>
                        <artifactId>audience-annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.12.0</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.3.3</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.26.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-math3</artifactId>
                <version>3.6.1</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.12.7</version>
            </dependency>
            <dependency>
                <groupId>org.xerial.snappy</groupId>
                <artifactId>snappy-java</artifactId>
                <version>1.1.10.5</version>
            </dependency>
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${mssql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>9.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc11</artifactId>
                <version>${oracle.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- NOTE: httpcore and httpclient versions are not in sync -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${http.core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${http.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.auto.value</groupId>
                <artifactId>auto-value-annotations</artifactId>
                <version>1.11.0</version>
            </dependency>
            <dependency>
                <groupId>com.nimbusds</groupId>
                <artifactId>nimbus-jose-jwt</artifactId>
                <version>9.40</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.woodstox</groupId>
                <artifactId>woodstox-core</artifactId>
                <version>7.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.woodstox</groupId>
                <artifactId>stax2-api</artifactId>
                <version>4.2.2</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-bundle</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bundle</artifactId>
                <version>${aws.sdk2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro</artifactId>
                <version>${avro.version}</version>
            </dependency>
            <dependency>
                <groupId>org.objenesis</groupId>
                <artifactId>objenesis</artifactId>
                <version>3.4</version>
            </dependency>
            <dependency>
                <groupId>com.github.luben</groupId>
                <artifactId>zstd-jni</artifactId>
                <version>1.5.6-3</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-bom</artifactId>
                <version>2.23.1</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>org.jline</groupId>
                <artifactId>jline</artifactId>
                <version>${jline.version}</version>
                <classifier>jdk8</classifier>
            </dependency>
            <dependency>
                <groupId>org.checkerframework</groupId>
                <artifactId>checker-qual</artifactId>
                <version>3.45.0</version>
            </dependency>
            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>antlr4-runtime</artifactId>
                <version>${antlr4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-mapper-asl</artifactId>
                <version>${jackson.mapper.asl.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-core-asl</artifactId>
                <version>${jackson.mapper.asl.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.reload4j</groupId>
                <artifactId>reload4j</artifactId>
                <version>${reload4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.lz4</groupId>
                <artifactId>lz4-java</artifactId>
                <version>1.8.0</version>
            </dependency>
            <dependency>
                <groupId>net.minidev</groupId>
                <artifactId>json-smart</artifactId>
                <version>2.5.1</version>
            </dependency>
            <dependency>
                <groupId>org.wildfly.openssl</groupId>
                <artifactId>wildfly-openssl</artifactId>
                <version>1.1.3.Final</version>
            </dependency>
            <dependency>
                <groupId>com.google.api-client</groupId>
                <artifactId>google-api-client</artifactId>
                <version>1.32.2</version>
            </dependency>
            <!-- Force higher version, compatible with M1 -->
            <!-- Otherwise Docker for Java (used by TestContainers) is not able to load all JNA files for aarch64 -->
            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>${jna.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <!--
            The following are test dependencies, they are not in the final distribution.
            We put them there for the dependency convergence task to succeed.
            -->
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${bytebuddy.version}</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy-agent</artifactId>
                <version>${bytebuddy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.30.2-GA</version>
            </dependency>
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>5.10.3</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.junit-pioneer</groupId>
                <artifactId>junit-pioneer</artifactId>
                <version>2.2.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-bom</artifactId>
                <version>5.12.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>${reflections.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-annotations</artifactId>
                <version>4.8.6</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>jsr305</artifactId>
                <version>3.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.tngtech.archunit</groupId>
                <artifactId>archunit</artifactId>
                <version>${archunit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.errorprone</groupId>
                <artifactId>error_prone_annotations</artifactId>
                <version>${errorprone.version}</version>
            </dependency>
            <dependency>
                <groupId>info.picocli</groupId>
                <artifactId>picocli</artifactId>
                <version>${picocli.version}</version>
            </dependency>
            <dependency>
                <groupId>org.awaitility</groupId>
                <artifactId>awaitility</artifactId>
                <version>${awaitility.version}</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-client-bom</artifactId>
                <version>6.13.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.j2objc</groupId>
                <artifactId>j2objc-annotations</artifactId>
                <version>3.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers-bom</artifactId>
                <version>${testcontainers.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <!-- Update ApacheDS Mina dep version 2.0.16->2.0.23. https://issues.apache.org/jira/browse/DIRMINA-1107 -->
            <dependency>
                <groupId>org.apache.mina</groupId>
                <artifactId>mina-core</artifactId>
                <version>2.0.25</version>
                <scope>test</scope>
            </dependency>

            <!-- All of this is needed to upgrade GCS -->
            <!-- see https://hazelcast.atlassian.net/browse/HZ-4706 -->
            <dependency>
                <groupId>com.google.cloud</groupId>
                <artifactId>libraries-bom</artifactId>
                <version>26.43.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.perfmark</groupId>
                <artifactId>perfmark-api</artifactId>
                <version>0.27.0</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-bom</artifactId>
                <version>${kotlin.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-resolver-provider</artifactId>
                <version>3.9.8</version>
            </dependency>
            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-artifact</artifactId>
                <version>3.9.8</version>
            </dependency>
            <dependency>
                <groupId>org.hdrhistogram</groupId>
                <artifactId>HdrHistogram</artifactId>
                <version>2.2.2</version>
            </dependency>
            <dependency>
                <groupId>org.neo4j.driver</groupId>
                <artifactId>neo4j-java-driver</artifactId>
                <version>5.22.0</version>
            </dependency>
            <dependency>
                <groupId>org.jctools</groupId>
                <artifactId>jctools-core</artifactId>
                <version>4.0.5</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.13.0</version>
                    <configuration>
                        <fork>true</fork>
                        <release>${jdk.version}</release>
                        <encoding>UTF-8</encoding>
                        <compilerArgs>
                            <arg>-Xlint:-deprecation,-removal</arg>
                        </compilerArgs>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven.shade.plugin.version}</version>
                    <configuration>
                        <filters combine.children="append">
                            <filter>
                                <artifact>*:*</artifact>
                                <excludes>
                                    <exclude>module-info.class</exclude>
                                    <exclude>META-INF/versions/9/module-info.class</exclude>
                                    <exclude>LICENSE</exclude>
                                    <exclude>NOTICE</exclude>
                                    <exclude>META-INF/*.SF</exclude>
                                    <exclude>META-INF/*.DSA</exclude>
                                    <exclude>META-INF/*.RSA</exclude>
                                    <exclude>META-INF/LICENSE</exclude>
                                    <exclude>META-INF/MANIFEST.MF</exclude>
                                    <exclude>META-INF/THIRD-PARTY.txt</exclude>
                                    <exclude>META-INF/DEPENDENCIES</exclude>
                                    <exclude>overview.html</exclude>
                                </excludes>
                            </filter>
                        </filters>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.7.0</version>
                    <configuration>
                        <!-- Allows generation when non-JPMS dependencies (e.g. Groovy) are on classpath -->
                        <legacyMode>true</legacyMode>
                        <detectJavaApiLink>false</detectJavaApiLink>
                        <maxmemory>1024</maxmemory>
                        <excludePackageNames>${maven.javadoc.plugin.excludePackageNames}</excludePackageNames>
                        <doclint>-missing</doclint>
                        <quiet>true</quiet>
                    </configuration>
                    <executions>
                        <execution>
                            <id>attach-javadocs</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.3.1</version>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.12</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.4.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.3.1</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <version>2.4.0</version>
                    <configuration>
                        <failOnBlacklist>true</failOnBlacklist>
                        <failOnMissing>true</failOnMissing>
                        <licenseMerges>
                            <!--
                              Only merge new 3rd Party dependency license if you get a license build failure
                            -->
                            <licenseMerge>
                                Apache-2.0 |
                                Apache License v2.0 |
                                Apache Software License 2.0 |
                                The Apache Software License, Version 2.0 |
                                The Apache Software License, version 2.0 |
                                Apache License, Version 2.0 |
                                Apache 2 |
                                Apache 2.0 |
                                The Apache License, Version 2.0 |
                                Apache Software License - Version 2.0 |
                                Apache License Version 2.0 |
                                Apache License, version 2.0 |
                                Apache License 2.0
                            </licenseMerge>
                            <licenseMerge>
                                MIT |
                                The MIT License |
                                MIT License |
                                MIT license |
                                The MIT License (MIT)
                            </licenseMerge>
                            <licenseMerge>
                                BSD-2-Clause |
                                BSD 2-Clause License |
                                BSD 2-Clause license |
                                The BSD 2-Clause License
                            </licenseMerge>
                            <licenseMerge>
                                BSD-3-Clause |
                                BSD 3-Clause License |
                                BSD |
                                BSD licence |
                                The BSD License |
                                3-Clause BSD License |
                                New BSD license |
                                New BSD License |
                                Modified BSD License |
                                The New BSD License |
                                Revised BSD |
                                BSD New license |
                                BSD 3-clause
                            </licenseMerge>
                            <licenseMerge>
                                CC0-1.0 |
                                CC0 |
                                Public Domain, per Creative Commons CC0
                            </licenseMerge>
                            <licenseMerge>
                                CDDL |
                                CDDL 1.1 |
                                COMMON DEVELOPMENT AND DISTRIBUTION LICENSE (CDDL) Version 1.0 |
                                CDDL + GPLv2 with classpath exception |
                                CDDL License |
                                CDDL/GPLv2+CE |
                                GPL2 w/ CPE
                            </licenseMerge>
                            <licenseMerge>
                                Eclipse Distribution License - v 1.0 |
                                EDL 1.0
                            </licenseMerge>
                            <licenseMerge>
                                EPL-1.0 |
                                EPL 1.0 |
                                Eclipse Public License 1.0 |
                                Eclipse Public License - Version 1.0
                            </licenseMerge>
                            <licenseMerge>
                                EPL-2.0 |
                                EPL 2.0 |
                                Eclipse Public License v2.0 |
                                Eclipse Public License - Version 2.0 |
                                Eclipse Public License, Version 2.0
                            </licenseMerge>
                            <licenseMerge>
                                Hazelcast Community License |
                                The Hazelcast Community License
                            </licenseMerge>
                            <licenseMerge>
                                Go License
                                The Go License
                            </licenseMerge>
                        </licenseMerges>
                        <excludedGroups>com.hazelcast</excludedGroups>
                        <excludedScopes>test,provided</excludedScopes>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.hazelcast.maven</groupId>
                    <artifactId>attribution-maven-plugin</artifactId>
                    <version>1.4.0</version>
                    <configuration>
                        <exclusionPatterns>
                            <!--
                              Exclude false positives from the attribution.txt files. Some lines matching the `copyrightPattern`
                              are actually not the copyright lines. Let's exclude them here.

                              Example:
                              com.fasterxml.jackson.core:jackson-databind:2.12.1
                                 (c) per-property override (from annotation on specific property or
                             -->
                            <exclusionPattern>^Copyright Laws of the United States.</exclusionPattern>
                            <exclusionPattern>per-property override</exclusionPattern>
                            <exclusionPattern>^copyright notice</exclusionPattern>
                            <exclusionPattern>FAILED_PRECONDITION</exclusionPattern>
                            <exclusionPattern>The ASF licenses this file to you under the Apache License
                            </exclusionPattern>
                            <exclusionPattern>Substitute for l, l</exclusionPattern>
                            <exclusionPattern>inside edge crossings that are between</exclusionPattern>
                            <exclusionPattern>The size of the key-exchange key</exclusionPattern>
                        </exclusionPatterns>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <version>3.1.2</version>
                </plugin>
                <plugin>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.7.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.3.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-assembly-plugin</artifactId>
                    <version>3.7.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>3.3.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>5.1.9</version>
                </plugin>
                <plugin>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.5.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>3.2.4</version>
                    <configuration>
                        <gpgArguments>
                            <gpgArgument>--pinentry-mode</gpgArgument>
                            <gpgArgument>loopback</gpgArgument>
                        </gpgArguments>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.googlecode.maven-download-plugin</groupId>
                    <artifactId>download-maven-plugin</artifactId>
                    <version>1.9.0</version>
                </plugin>
                <plugin>
                    <groupId>org.xolstice.maven.plugins</groupId>
                    <artifactId>protobuf-maven-plugin</artifactId>
                    <version>0.6.1</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>exec-maven-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>
                <plugin>
                    <groupId>pl.project13.maven</groupId>
                    <artifactId>git-commit-id-plugin</artifactId>
                    <version>2.1.10</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>cobertura-maven-plugin</artifactId>
                    <version>2.7</version>
                </plugin>
                <plugin>
                    <groupId>org.owasp</groupId>
                    <artifactId>dependency-check-maven</artifactId>
                    <version>10.0.2</version>
                    <configuration>
                        <format>ALL</format>
                        <skipProvidedScope>true</skipProvidedScope>
                        <nodeAuditAnalyzerEnabled>false</nodeAuditAnalyzerEnabled>
                        <suppressionFiles>owasp-check-suppressions.xml</suppressionFiles>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>3.4.0</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>license-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-third-party</id>
                        <goals>
                            <goal>add-third-party</goal>
                        </goals>
                        <phase>generate-resources</phase>
                        <configuration>
                            <outputDirectory>${project.build.outputDirectory}/META-INF</outputDirectory>
                            <force>true</force>
                            <fileTemplate>
                                ${main.basedir}/hazelcast-build-utils/src/main/resources/hazelcast-thirdparty-template.ftl
                            </fileTemplate>
                            <includedLicenses>
                                <!-- 
                                  Always use SPDX license identifiers listed in https://spdx.org/licenses (when available)
                                  note: GPL licenses are excluded in https://github.com/hazelcast/hazelcast/blob/master/hazelcast-build-utils/src/main/resources/hazelcast-thirdparty-template.ftl
                                -->
                                <includedLicense>Apache-2.0</includedLicense>
                                <includedLicense>MIT</includedLicense>
                                <includedLicense>MIT-0</includedLicense>
                                <includedLicense>BSD-2-Clause</includedLicense>
                                <includedLicense>BSD-3-Clause</includedLicense>
                                <includedLicense>CC0-1.0</includedLicense>
                                <includedLicense>CDDL</includedLicense>
                                <includedLicense>Eclipse Distribution License - v 1.0</includedLicense>
                                <includedLicense>EPL-1.0</includedLicense>
                                <includedLicense>EPL-2.0</includedLicense>
                                <includedLicense>Public Domain</includedLicense>
                                <includedLicense>Go License</includedLicense>
                            </includedLicenses>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.hazelcast.maven</groupId>
                <artifactId>attribution-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>per-jar-attribution</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <outputFile>${project.build.outputDirectory}/META-INF/attribution.txt</outputFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <execution>
                        <id>jdk-version</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireJavaVersion>
                                    <version>[${jdk.version},)</version>
                                </requireJavaVersion>
                                <enforceBytecodeVersion>
                                    <maxJdkVersion>${jdk.version}</maxJdkVersion>
                                </enforceBytecodeVersion>
                            </rules>
                        </configuration>
                    </execution>
                    <execution>
                        <id>dependency-convergence</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <dependencyConvergence/>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>extra-enforcer-rules</artifactId>
                        <version>1.8.0</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.4.0</version>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>checkstyle</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <configLocation>${checkstyle.configLocation}</configLocation>
                    <suppressionsLocation>${checkstyle.supressionsLocation}</suppressionsLocation>
                    <headerLocation>${checkstyle.headerLocation}</headerLocation>
                    <linkXRef>false</linkXRef>
                    <consoleOutput>true</consoleOutput>
                    <failsOnError>true</failsOnError>
                    <failOnViolation>true</failOnViolation>
                    <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    <enableRulesSummary>true</enableRulesSummary>
                    <propertyExpansion>main.basedir=${main.basedir}</propertyExpansion>
                    <excludes>**/module-info.java</excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <!--
            Enables faster local builds, by skipping ome validation steps (e.g. Checkstyle)
            -->
            <id>quick</id>
            <activation>
                <property>
                    <name>quick</name>
                </property>
            </activation>
            <properties>
                <!-- https://maven.apache.org/plugins/maven-checkstyle-plugin/checkstyle-mojo.html#skip -->
                <checkstyle.skip>true</checkstyle.skip>
                <!-- https://www.mojohaus.org/xml-maven-plugin/validate-mojo.html#skip -->
                <xml.skip>true</xml.skip>
                <!-- https://maven.apache.org/surefire/maven-surefire-plugin/test-mojo.html#skipTests -->
                <skipTests>true</skipTests>
                <!-- https://maven.apache.org/plugins/maven-javadoc-plugin/javadoc-mojo.html#skip -->
                <maven.javadoc.skip>true</maven.javadoc.skip>
                <!-- https://maven.apache.org/enforcer/maven-enforcer-plugin/enforce-mojo.html#skip -->
                <enforcer.skip>true</enforcer.skip>
                <!-- https://github.com/hazelcast/attribution-maven-plugin?tab=readme-ov-file#configuration-properties -->
                <attribution.skip>true</attribution.skip>
                <!-- https://www.mojohaus.org/license-maven-plugin/add-third-party-mojo.html#skipAddThirdParty -->
                <license.skipAddThirdParty>true</license.skipAddThirdParty>
                <!-- https://www.mojohaus.org/license-maven-plugin/aggregate-add-third-party-mojo.html -->
                <license.skipAggregateAddThirdParty>true</license.skipAggregateAddThirdParty>
                <!-- https://maven.apache.org/plugins/maven-compiler-plugin/compile-mojo.html#useIncrementalCompilation -->
                <maven.compiler.useIncrementalCompilation>false</maven.compiler.useIncrementalCompilation>
            </properties>
        </profile>

        <profile>
            <!--
            Configures release-specific options when doing an intermediate SNAPSHOT release

            Ensures Javadoc, sources etc. are generated and that an appropriate distribution destination is specified

            Unfortunately, it isn't possible to chain profiles, so we need to duplicate the javadoc plugin
            -->
            <id>release-snapshot</id>
            <properties>
                <javadoc>true</javadoc>
                <maven.source.skip>false</maven.source.skip>
                <maven.shade.createSourcesJar>true</maven.shade.createSourcesJar>
                <attribution.skip>false</attribution.skip>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <!--
            Configures release-specific options when doing an intermediate devel release

            Ensures Javadoc, sources etc. are generated and that an appropriate distribution destination is specified

            Unfortunately, it isn't possible to chain profiles, so we need to duplicate the javadoc plugin
            -->
            <id>release-devel</id>
            <properties>
                <javadoc>true</javadoc>
                <maven.source.skip>false</maven.source.skip>
                <maven.shade.createSourcesJar>true</maven.shade.createSourcesJar>
                <attribution.skip>false</attribution.skip>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
            <distributionManagement>
                <repository>
                    <id>devel-repository</id>
                    <url>https://hazelcast.jfrog.io/artifactory/devel</url>
                </repository>
            </distributionManagement>
        </profile>
        
        <!-- For use in CI (Continuous Integration) environments (Jenkins, GitHub actions etc) -->
        <profile>
            <id>CI</id>
            <activation>
                <property>
                    <!-- "CI" is an environment variable set in many CI pipelines, e.g. https://docs.gitlab.com/ee/ci/variables/predefined_variables.html -->
                    <name>env.CI</name>
                    <value>true</value>
                </property>
            </activation>
            <properties>
                <maven.test.redirectTestOutputToFile>true</maven.test.redirectTestOutputToFile>
            </properties>
        </profile>

        <profile>
            <!--
            Configures release-specific options when doing a final, production release

            Ensures Javadoc, sources etc. are generated and that the final JAR is cryptographically signed
            -->
            <id>release</id>
            <properties>
                <javadoc>true</javadoc>
                <maven.source.skip>false</maven.source.skip>
                <maven.shade.createSourcesJar>true</maven.shade.createSourcesJar>
                <attribution.skip>false</attribution.skip>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
