<!--
  ~ Copyright (c) 2008-2024, Hazelcast, Inc. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                      http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast-parent</artifactId>
        <version>5.5.0</version>
        <relativePath>hazelcast-parent/pom.xml</relativePath>
    </parent>

    <artifactId>hazelcast-root</artifactId>
    <packaging>pom</packaging>
    <version>5.5.0</version>
    <name>Hazelcast Root</name>
    <description>Hazelcast In-Memory DataGrid</description>
    <url>http://www.hazelcast.com/</url>

    <modules>
        <module>hazelcast-parent</module>
        <module>hazelcast-tpc-engine</module>
        <module>hazelcast</module>
        <module>hazelcast-archunit-rules</module>
        <module>hazelcast-spring</module>
        <module>hazelcast-spring-tests</module>
        <module>hazelcast-build-utils</module>
        <module>hazelcast-sql</module>
    </modules>

    <properties>
        <main.basedir>${project.basedir}</main.basedir>
        <target.dir>target</target.dir>
        <timestamp>${maven.build.timestamp}</timestamp>

        <hazelcast.distribution>Hazelcast</hazelcast.distribution>
        <hazelcast.serialization.version>1</hazelcast.serialization.version>
        <hazelcast.serverMainClass>com.hazelcast.core.server.HazelcastMemberStarter</hazelcast.serverMainClass>
        <hazelcast.previous.version>5.3.0</hazelcast.previous.version>

        <maven.javadoc.plugin.excludePackageNames>
            *.impl:*.impl.*:*.internal:*.internal.*:*.operations:*.proxy:
            com.hazelcast.aws.security:*.handlermigration:*.client.connection.nio:
            *.client.console:*.client.protocol.generator:*.cluster.client:
            *.concurrent:*.nio.ascii:*.nio.ssl:*.nio.tcp:*.partition.client:
            *.transaction.client:*.core.server:com.hazelcast.instance:com.hazelcast.PlaceHolder
        </maven.javadoc.plugin.excludePackageNames>

        <!-- Additional JVM system arguments -->
        <extraVmArgs/>
        <vmHeapSettings>-Xms512m -Xmx2G</vmHeapSettings>
        <!-- Java 9+ module system args to be appended during surefire/failsafe executions. -->
        <hazelcast.module.name>ALL-UNNAMED</hazelcast.module.name>

        <!-- needed for CheckStyle -->
        <checkstyle.configLocation>${main.basedir}/checkstyle/checkstyle.xml</checkstyle.configLocation>
        <checkstyle.supressionsLocation>${main.basedir}/checkstyle/suppressions.xml</checkstyle.supressionsLocation>
        <checkstyle.headerLocation>${main.basedir}/checkstyle/ClassHeaderApache.txt</checkstyle.headerLocation>

        <shade.jar-with-dependencies.phase>package</shade.jar-with-dependencies.phase>
        <fat.dependency.classifier>jar-with-dependencies</fat.dependency.classifier>

        <!--
        Allow access to Operating system metrics:
           open jdk.management/com.sun.management.internal
           export jdk.management/com.ibm.lang.management.internal

        Avoid warnings caused by reflection in
        SelectorOptimizer:
           open java.base/sun.nio.ch
        FilteringClassLoader:
           open java.base/java.lang
        TimedMemberStateFactoryHelper:
           open java.management/sun.management

        NOTE: There are additional arguments for IBM OpenJ9, see the specific profile
        -->
        <javaStandardModuleArgs>
            --add-exports java.base/jdk.internal.ref=${hazelcast.module.name}
            --add-opens jdk.management/com.sun.management.internal=${hazelcast.module.name}
            --add-opens java.base/sun.nio.ch=${hazelcast.module.name}
            --add-opens java.base/java.lang=${hazelcast.module.name}
            --add-opens java.management/sun.management=${hazelcast.module.name}
        </javaStandardModuleArgs>
        <javaModuleArgs>${javaStandardModuleArgs}</javaModuleArgs>
    </properties>
    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <scm>
        <connection>scm:git:git://github.com/hazelcast/hazelcast.git</connection>
        <developerConnection>scm:git:**************:hazelcast/hazelcast.git</developerConnection>
        <url>https://github.com/hazelcast/hazelcast/</url>
    </scm>
    <developers>
        <developer>
            <id>hazelcast-team</id>
            <name>Hazelcast team</name>
            <email><EMAIL></email>
            <organization>Hazelcast, Inc.</organization>
            <organizationUrl>https://hazelcast.com</organizationUrl>
        </developer>
    </developers>

    <contributors>
        <contributor>
            <name>Hazelcast Community</name>
            <email><EMAIL></email>
            <url>https://hazelcast.atlassian.net/wiki/display/COM/Hazelcast+Contributor+Agreement</url>
        </contributor>
    </contributors>

    <issueManagement>
        <system>Github</system>
        <url>https://github.com/hazelcast/hazelcast/issues</url>
    </issueManagement>
    <organization>
        <name>Hazelcast, Inc.</name>
        <url>http://www.hazelcast.com/</url>
    </organization>

    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
        <outputDirectory>${target.dir}/classes</outputDirectory>
        <testSourceDirectory>src/test/java</testSourceDirectory>
        <testOutputDirectory>${target.dir}/test-classes</testOutputDirectory>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.html</include>
                    <include>**/*.bat</include>
                    <include>**/*.sh</include>
                    <include>**/*.py</include>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yaml</include>
                    <include>**/*.xsd</include>
                    <include>**/*.txt</include>
                    <include>**/hazelcast-config-*.json</include>
                    <include>**/*.handlers</include>
                    <include>**/*.schemas</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.so</include>
                    <include>**/*.license</include>
                    <include>**/*.key</include>
                    <include>**/*.ftl</include>
                    <include>**/META-INF/services/*.*</include>
                </includes>
            </resource>
        </resources>

        <!-- ignore the testjob module -->
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <excludes>
                    <exclude>com/hazelcast/jet/server/testjob/**</exclude>
                </excludes>
            </testResource>
        </testResources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration combine.self="override">
                    <useFile>false</useFile>
                    <trimStackTrace>false</trimStackTrace>
                    <runOrder>failedfirst</runOrder>

                    <!-- 1C means 1 process per cpu core -->
                    <!-- forkCount>1C</forkCount>
                    <reuseForks>true</reuseForks>

                    <threadCount>1</threadCount>
                    <perCoreThreadCount>true</perCoreThreadCount>
                    <parallel>methods</parallel -->
                    <!-- JaCoCo will use the argLine set here. Test profiles override it. -->
                    <argLine>
                        ${vmHeapSettings}
                        ${javaModuleArgs}
                        -Dhazelcast.phone.home.enabled=false
                        -Dhazelcast.test.use.network=false
                        -Dlog4j.skipJansi=true
                        ${extraVmArgs}
                    </argLine>
                    <includes>
                        <include>**/**.java</include>
                    </includes>
                    <excludes>
                        <exclude>**/jsr/**.java</exclude>
                        <exclude>**/**IT.java</exclude>
                    </excludes>
                    <excludedGroups>
                        com.hazelcast.test.annotation.SlowTest,
                        com.hazelcast.test.annotation.NightlyTest
                    </excludedGroups>
                    <!-- Use TCP for IPC communication to avoid warnings: Corrupted STDOUT by directly writing... -->
                    <forkNode implementation="org.apache.maven.plugin.surefire.extensions.SurefireForkNodeFactory"/>
                    <!-- Fixes missing parameters in test name for tests run via mvn -->
                    <statelessTestsetReporter
                            implementation="org.apache.maven.plugin.surefire.extensions.junit5.JUnit5Xml30StatelessReporter">
                        <usePhrasedTestCaseMethodName>true</usePhrasedTestCaseMethodName>
                    </statelessTestsetReporter>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <configuration combine.self="override">
                    <trimStackTrace>false</trimStackTrace>
                    <argLine>
                        ${vmHeapSettings}
                        ${javaModuleArgs}
                        -Dhazelcast.phone.home.enabled=false
                        -Dhazelcast.test.use.network=true
                        -Dlog4j.skipJansi=true
                        ${extraVmArgs}
                    </argLine>
                    <useManifestOnlyJar>false</useManifestOnlyJar>
                    <useSystemClassLoader>true</useSystemClassLoader>
                    <excludedGroups>
                        com.hazelcast.test.annotation.SlowTest,
                        com.hazelcast.test.annotation.NightlyTest
                    </excludedGroups>
                    <!-- Use TCP for IPC communication to avoid warnings: Corrupted STDOUT by directly writing... -->
                    <forkNode implementation="org.apache.maven.plugin.surefire.extensions.SurefireForkNodeFactory"/>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>integration-test</goal>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-notice-and-license</id>
                        <phase>none</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.outputDirectory}/META-INF</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>..</directory>
                                    <includes>
                                        <include>NOTICE</include>
                                    </includes>
                                </resource>
                                <resource>
                                    <!-- Don't include the top level LICENSE file which is valid for whole repository
                                         use rather the ASF license file located in hazelcast-build-utils module.-->
                                    <directory>../hazelcast-build-utils/src/main/resources</directory>
                                    <includes>
                                        <include>LICENSE</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.owasp</groupId>
                <artifactId>dependency-check-maven</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>get-hz-3</id>
                        <goals>
                            <goal>get</goal>
                        </goals>
                        <phase>generate-test-resources</phase>
                        <configuration>
                            <artifact>com.hazelcast:hazelcast:4.0</artifact>
                            <artifact>com.hazelcast:hazelcast:4.0:jar:tests</artifact>

                            <artifact>com.hazelcast:hazelcast:4.0.1</artifact>
                            <artifact>com.hazelcast:hazelcast:4.0.1:jar:tests</artifact>

                            <artifact>com.hazelcast:hazelcast:4.0.2</artifact>
                            <artifact>com.hazelcast:hazelcast:4.0.2:jar:tests</artifact>

                            <artifact>com.hazelcast:hazelcast:4.0.3</artifact>
                            <artifact>com.hazelcast:hazelcast:4.0.3:jar:tests</artifact>

                            <artifact>com.hazelcast:hazelcast:${hazelcast.previous.version}</artifact>
                            <artifact>com.hazelcast:hazelcast:${hazelcast.previous.version}:jar:tests</artifact>

                        </configuration>
                    </execution>
                </executions>
                <inherited>false</inherited>
            </plugin>
            <plugin>
                <groupId>com.hazelcast.maven</groupId>
                <artifactId>attribution-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>aggregated-attribution-oss</id>
                        <inherited>false</inherited>
                        <goals>
                            <goal>aggregate</goal>
                        </goals>
                        <configuration>
                            <outputFile>${project.build.directory}/aggregated-attribution.txt</outputFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <!--
            Executes:
            - `ParallelJVMTest`s in parallel via several surefire forks (note: inside the fork tests still run serially)
            - Excludes:
                - `SlowTest`
                - `NightlyTest`
            - Typically `QuickTest`s

            Duplicated in OS & EE

            used by the PR builder
            -->
            <id>parallelTest</id>
            <properties>
                <skip-singlejvm-tests>false</skip-singlejvm-tests>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>default-test</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration combine.self="override">
                                    <useFile>false</useFile>
                                    <trimStackTrace>false</trimStackTrace>
                                    <!-- 0.5C means half as many forks as cpu cores -->
                                    <forkCount>0.5C</forkCount>
                                    <reuseForks>true</reuseForks>
                                    <argLine>
                                        ${vmHeapSettings}
                                        ${javaModuleArgs}
                                        -ea
                                        -Dhazelcast.phone.home.enabled=false
                                        -Dhazelcast.test.use.network=false
                                        -Dhazelcast.test.multiple.jvm=true
                                        -Dlog4j.configurationFile=log4j2.xml
                                        -Dlog4j.skipJansi=true
                                        ${extraVmArgs}
                                    </argLine>
                                    <includes>
                                        <include>**/**.java</include>
                                    </includes>
                                    <excludes>
                                        <exclude>**/jsr/**.java</exclude>
                                        <exclude>**/**IT.java</exclude>
                                    </excludes>
                                    <groups>
                                        com.hazelcast.test.annotation.ParallelJVMTest
                                    </groups>
                                    <excludedGroups>
                                        com.hazelcast.test.annotation.SlowTest,
                                        com.hazelcast.test.annotation.NightlyTest
                                    </excludedGroups>
                                    <systemPropertyVariables>
                                        <multipleJVM>true</multipleJVM>
                                    </systemPropertyVariables>
                                    <!-- Fixes missing parameters in test name for tests run via mvn -->
                                    <statelessTestsetReporter
                                            implementation="org.apache.maven.plugin.surefire.extensions.junit5.JUnit5Xml30StatelessReporter">
                                        <usePhrasedTestCaseMethodName>true</usePhrasedTestCaseMethodName>
                                    </statelessTestsetReporter>
                                </configuration>
                            </execution>
                            <execution>
                                <id>singlejvm</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration combine.self="override">
                                    <skip>${skip-singlejvm-tests}</skip>
                                    <useFile>false</useFile>
                                    <trimStackTrace>false</trimStackTrace>
                                    <argLine>
                                        ${vmHeapSettings}
                                        ${javaModuleArgs}
                                        -Dhazelcast.phone.home.enabled=false
                                        -Dhazelcast.test.use.network=false
                                        -Dlog4j.configurationFile=log4j2.xml
                                        -Dlog4j.skipJansi=true
                                        ${extraVmArgs}
                                    </argLine>
                                    <includes>
                                        <include>**/**.java</include>
                                    </includes>
                                    <excludes>
                                        <exclude>**/jsr/**.java</exclude>
                                        <exclude>**/**IT.java</exclude>
                                    </excludes>
                                    <excludedGroups>
                                        com.hazelcast.test.annotation.SlowTest,
                                        com.hazelcast.test.annotation.NightlyTest,
                                        com.hazelcast.test.annotation.ParallelJVMTest
                                    </excludedGroups>
                                    <!-- Fixes missing parameters in test name for tests run via mvn -->
                                    <statelessTestsetReporter
                                            implementation="org.apache.maven.plugin.surefire.extensions.junit5.JUnit5Xml30StatelessReporter">
                                        <usePhrasedTestCaseMethodName>true</usePhrasedTestCaseMethodName>
                                    </statelessTestsetReporter>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <configuration combine.self="override">
                            <useFile>false</useFile>
                            <trimStackTrace>false</trimStackTrace>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.test.use.network=false
                                -Dlog4j.configurationFile=log4j2.xml
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <useManifestOnlyJar>false</useManifestOnlyJar>
                            <useSystemClassLoader>true</useSystemClassLoader>
                            <excludedGroups>
                                com.hazelcast.test.annotation.SlowTest,
                                com.hazelcast.test.annotation.NightlyTest
                            </excludedGroups>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>integration-test</goal>
                                    <goal>verify</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <!--
            Configures execution to optimize runtime of the PR builder, by excluding tasks not required for build validation (e.g., shading)
            -->
            <id>pr-builder</id>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>usercodedeployment-child-parent</id>
                                <configuration>
                                    <skipAssembly>false</skipAssembly>
                                </configuration>
                            </execution>
                            <execution>
                                <id>usercodedeployment-entry-processor-with-anonymous-and-inner</id>
                                <configuration>
                                    <skipAssembly>false</skipAssembly>
                                </configuration>
                            </execution>
                            <execution>
                                <id>usercodedeployment-incrementing-entry-processor</id>
                                <configuration>
                                    <skipAssembly>false</skipAssembly>
                                </configuration>
                            </execution>
                            <execution>
                                <id>usercodedeployment-incrementing-entry-processor-conflicting</id>
                                <configuration>
                                    <skipAssembly>false</skipAssembly>
                                </configuration>
                            </execution>
                            <execution>
                                <id>usercodedeployment-shaded-classes</id>
                                <configuration>
                                    <skipAssembly>false</skipAssembly>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
            <properties>
                <!--
                    To improve PR builder runtime, skip *some* assembly-plugins that are not required for build validation
                    https://github.com/hazelcast/hazelcast/pull/18521
                -->
                <assembly.skipAssembly>true</assembly.skipAssembly>
                <!-- Set the classifier to empty in this profile to use regular jar-->
                <fat.dependency.classifier/>
                <shade.jar-with-dependencies.phase>none</shade.jar-with-dependencies.phase>
            </properties>
        </profile>

        <profile>
            <id>test-coverage-Local</id>
            <properties>
                <vmHeapSettings>-Xms128m -Xmx1G</vmHeapSettings>
            </properties>
            <modules>
                <module>hazelcast-coverage-report</module>
            </modules>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>cobertura-maven-plugin</artifactId>
                        <configuration>
                            <instrumentation>
                                <ignores/>
                                <excludes/>
                            </instrumentation>
                            <check/>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>clean</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration combine.self="override">
                            <useFile>false</useFile>
                            <trimStackTrace>false</trimStackTrace>
                            <testFailureIgnore>true</testFailureIgnore>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.test.use.network=false
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <includes>
                                <include>**/YourTestFileHear.java</include>
                            </includes>
                            <!-- Fixes missing parameters in test name for tests run via mvn -->
                            <statelessTestsetReporter
                                    implementation="org.apache.maven.plugin.surefire.extensions.junit5.JUnit5Xml30StatelessReporter">
                                <usePhrasedTestCaseMethodName>true</usePhrasedTestCaseMethodName>
                            </statelessTestsetReporter>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
            <reporting>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>cobertura-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </reporting>
        </profile>

        <profile>
            <id>test-coverage</id>
            <modules>
                <module>hazelcast-coverage-report</module>
            </modules>
            <properties>
                <argLine>
                    ${vmHeapSettings}
                    ${javaModuleArgs}
                    -Dhazelcast.phone.home.enabled=false
                    -Dhazelcast.test.use.network=false
                    -Dhazelcast.operation.call.timeout.millis=120000
                    -Dhazelcast.graceful.shutdown.max.wait=240
                    -Dlog4j.skipJansi=true
                    ${extraVmArgs}
                </argLine>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <configuration>
                            <excludes>
                                <exclude>**DummyProperty**</exclude>
                                <exclude>**DummyEntity**</exclude>
                                <exclude>**test-hibernate**</exclude>
                                <exclude>**HibernateStatisticsTestSupport**</exclude>
                                <exclude>**HibernateTestSupport**</exclude>
                                <exclude>**RegionFactoryDefaultTest**</exclude>
                            </excludes>
                        </configuration>
                        <executions>
                            <execution>
                                <id>prepare-agent</id>
                                <goals>
                                    <goal>prepare-agent</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration combine.self="override">
                            <useFile>false</useFile>
                            <trimStackTrace>false</trimStackTrace>
                            <testFailureIgnore>true</testFailureIgnore>
                            <includes>
                                <include>**/*.java</include>
                            </includes>
                            <excludes>
                                <exclude>**/**IT.java</exclude>
                            </excludes>
                            <excludedGroups>
                                com.hazelcast.test.annotation.NightlyTest
                            </excludedGroups>
                            <!-- Fixes missing parameters in test name for tests run via mvn -->
                            <statelessTestsetReporter
                                    implementation="org.apache.maven.plugin.surefire.extensions.junit5.JUnit5Xml30StatelessReporter">
                                <usePhrasedTestCaseMethodName>true</usePhrasedTestCaseMethodName>
                            </statelessTestsetReporter>
                        </configuration>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <configuration combine.self="override">
                            <trimStackTrace>false</trimStackTrace>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.test.use.network=true
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <excludedGroups>
                                com.hazelcast.test.annotation.NightlyTest
                            </excludedGroups>
                            <useManifestOnlyJar>false</useManifestOnlyJar>
                            <useSystemClassLoader>true</useSystemClassLoader>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>integration-test</goal>
                                    <goal>verify</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <!--
            Configures release-specific options when doing a final, production release
            -->
            <id>release</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>nexus-staging-maven-plugin</artifactId>
                        <version>1.7.0</version>
                        <extensions>true</extensions>
                        <configuration>
                            <serverId>release-repository</serverId>
                            <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                            <autoReleaseAfterClose>true</autoReleaseAfterClose>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <!--
            Executes:
            - `SlowTest`s
            - `NightlyTest`s
            - Excludes all other tests

            Duplicated in OS & EE

            Used for scheduled, longer running tests
            -->
            <id>nightly-build</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration combine.self="override">
                            <parallel>none</parallel>
                            <useFile>false</useFile>
                            <trimStackTrace>false</trimStackTrace>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.test.use.network=false
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <groups>
                                com.hazelcast.test.annotation.NightlyTest,
                                com.hazelcast.test.annotation.SlowTest
                            </groups>
                            <includes>
                                <include>**/**Test.java</include>
                            </includes>
                            <excludes>
                                <exclude>**/**IT.java</exclude>
                                <exclude>**/jsr/**.java</exclude>
                            </excludes>
                            <!-- Fixes missing parameters in test name for tests run via mvn -->
                            <statelessTestsetReporter
                                    implementation="org.apache.maven.plugin.surefire.extensions.junit5.JUnit5Xml30StatelessReporter">
                                <usePhrasedTestCaseMethodName>true</usePhrasedTestCaseMethodName>
                            </statelessTestsetReporter>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <configuration combine.self="override">
                            <trimStackTrace>false</trimStackTrace>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.test.use.network=true
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <useManifestOnlyJar>false</useManifestOnlyJar>
                            <useSystemClassLoader>true</useSystemClassLoader>
                            <includes>
                                <include>**/*IT.java</include>
                            </includes>
                            <groups>
                                com.hazelcast.test.annotation.NightlyTest,
                                com.hazelcast.test.annotation.SlowTest
                            </groups>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>integration-test</goal>
                                    <goal>verify</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <!--
            Executes all tests (`QuickTest` and `SlowTest`)

            Duplicated in OS & EE
            -->
            <id>all-tests</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration combine.self="override">
                            <useFile>false</useFile>
                            <trimStackTrace>false</trimStackTrace>
                            <parallel>none</parallel>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.test.use.network=false
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <includes>
                                <include>**/**.java</include>
                            </includes>
                            <excludes>
                                <exclude>**/jsr/**.java</exclude>
                                <exclude>**/**IT.java</exclude>
                            </excludes>
                            <!-- Use TCP for IPC communication to avoid warnings: Corrupted STDOUT by directly writing... -->
                            <forkNode implementation="org.apache.maven.plugin.surefire.extensions.SurefireForkNodeFactory"/>
                            <!-- Fixes missing parameters in test name for tests run via mvn -->
                            <statelessTestsetReporter
                                    implementation="org.apache.maven.plugin.surefire.extensions.junit5.JUnit5Xml30StatelessReporter">
                                <usePhrasedTestCaseMethodName>true</usePhrasedTestCaseMethodName>
                            </statelessTestsetReporter>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <configuration combine.self="override">
                            <trimStackTrace>false</trimStackTrace>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.test.use.network=true
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <useManifestOnlyJar>false</useManifestOnlyJar>
                            <useSystemClassLoader>true</useSystemClassLoader>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>integration-test</goal>
                                    <goal>verify</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <!--
            Profile which allows to skip the modulepath-tests module.
            The module runs by default, but can be skipped by adding -Dskip-modulepath-tests property.
            This is used in PR builder when we run only `mvn test-compile` which is not possible due
            to an automatic module name not being created in the hazelcast jar (no jar is built at all).
            -->
            <id>modulepath-tests</id>
            <activation>
                <property>
                    <name>!skip-modulepath-tests</name>
                </property>
            </activation>
            <modules>
                <module>modulepath-tests</module>
            </modules>
        </profile>

        <profile>
            <!-- same as default build (excludes Nightly & Slow tests), outputs serialized objects to a blob -->
            <!-- for compatibility testing -->
            <id>generate-compatibility-samples</id>
            <properties>
                <vmHeapSettings>-Xms4G -Xmx8G</vmHeapSettings>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration combine.self="override">
                            <properties>
                                <property>
                                    <name>listener</name>
                                    <value>com.hazelcast.test.compatibility.SamplingTestExecutionListener</value>
                                </property>
                            </properties>
                            <trimStackTrace>false</trimStackTrace>
                            <runOrder>failedfirst</runOrder>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.test.use.network=false
                                -Dhazelcast.test.sample.serialized.objects=${target.dir}/serialized-objects-${project.version}-
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <includes>
                                <include>**/**.java</include>
                            </includes>
                            <excludes>
                                <exclude>**/jsr/**.java</exclude>
                                <!-- code triggered by UserCodeDeploymentSmokeTest and OperationRunnerImplTest -->
                                <!-- casts to SerializationServiceV1 and fails when Node is configured with the -->
                                <!-- sampling serialization service -->
                                <exclude>**/OperationRunnerImplTest.java</exclude>
                                <exclude>**/**IT.java</exclude>
                                <exclude>**/jet/**/*.java</exclude>
                                <exclude>**/*Jet*.java</exclude>
                            </excludes>
                            <excludedGroups>
                                com.hazelcast.test.annotation.SlowTest,
                                com.hazelcast.test.annotation.NightlyTest,
                                com.hazelcast.test.annotation.SerializationSamplesExcluded
                            </excludedGroups>
                            <!-- Fixes missing parameters in test name for tests run via mvn -->
                            <statelessTestsetReporter
                                    implementation="org.apache.maven.plugin.surefire.extensions.junit5.JUnit5Xml30StatelessReporter">
                                <usePhrasedTestCaseMethodName>true</usePhrasedTestCaseMethodName>
                            </statelessTestsetReporter>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <!--
            In isolation, does nothing as typically enabled by default
            Works in conjunction with the `-Dquick` argument - which disables this profile - allowing additional modules to be skipped to reduce build time
            This implementation is preferred as it also supports excluding child modules

            Duplicated in OS & EE
            -->
            <id>not-quick</id>
            <activation>
                <property>
                    <name>!quick</name>
                </property>
            </activation>
            <modules>
                <module>extensions</module>
                <module>distribution</module>
                <module>hazelcast-it</module>
            </modules>
        </profile>
        <profile>
            <id>IBM</id>
            <activation>
                <property>
                    <name>java.vendor</name>
                    <value>IBM Corporation</value>
                </property>
            </activation>
            <properties>
                <javaModuleArgs>
                    ${javaStandardModuleArgs}
                    --add-exports jdk.management/com.ibm.lang.management.internal=${hazelcast.module.name}
                </javaModuleArgs>
            </properties>
        </profile>
    </profiles>

    <repositories>
        <repository>
            <!--
            This is the same as central in the super pom.
            Putting it here changes the order in which the repositories are queried.
            Most artefacts are stored in central so this provides best build times when a mirror is not used.
            Repository order reference:
            https://maven.apache.org/guides/mini/guide-multiple-repositories.html#repository-order
            -->
            <id>central</id>
            <name>Central Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>snapshot-repository</id>
            <name>Maven2 Snapshot Repository</name>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>confluent</id>
            <url>https://packages.confluent.io/maven/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>hazelcast-security</id>
            <name>Thirdparty security fixes</name>
            <url>https://repository.hazelcast.com/security-maven/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <!-- Red Hat Maven repository provides patches for several vulnerable libraries (dependencies) which are not patched in the Maven Central repository. -->
        <repository>
            <id>redhat-ga</id>
            <url>https://maven.repository.redhat.com/ga/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>javax.cache</groupId>
            <artifactId>cache-api</artifactId>
            <version>${jsr107.api.version}</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.snakeyaml</groupId>
            <artifactId>snakeyaml-engine</artifactId>
            <version>${snakeyaml.engine.version}</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit-pioneer</groupId>
            <artifactId>junit-pioneer</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>${assertj.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>uk.org.webcompere</groupId>
            <artifactId>system-stubs-jupiter</artifactId>
            <version>2.1.6</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>uk.org.webcompere</groupId>
            <artifactId>system-stubs-junit4</artifactId>
            <version>2.1.6</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <scope>test</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.github.spotbugs</groupId>
            <artifactId>spotbugs-annotations</artifactId>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>nl.jqno.equalsverifier</groupId>
            <artifactId>equalsverifier</artifactId>
            <version>3.16.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>10.1.20</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
