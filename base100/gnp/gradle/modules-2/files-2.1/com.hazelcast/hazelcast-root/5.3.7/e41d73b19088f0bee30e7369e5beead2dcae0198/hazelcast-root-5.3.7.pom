<!--
  ~ Copyright (c) 2008-2023, Hazelcast, Inc. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~ http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                      http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.hazelcast</groupId>
    <artifactId>hazelcast-root</artifactId>
    <packaging>pom</packaging>
    <version>5.3.7</version>
    <name>Hazelcast Root</name>
    <description>Hazelcast In-Memory DataGrid</description>
    <url>http://www.hazelcast.com/</url>

    <modules>
        <module>hazelcast-tpc-engine</module>
        <module>hazelcast</module>
        <module>hazelcast-archunit-rules</module>
        <module>hazelcast-spring</module>
        <module>hazelcast-spring-tests</module>
        <module>hazelcast-build-utils</module>
        <module>hazelcast-sql</module>
    </modules>

    <properties>
        <main.basedir>${project.basedir}</main.basedir>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <jdk.version>8</jdk.version>
        <target.dir>target</target.dir>
        <maven.build.timestamp.format>yyyyMMdd</maven.build.timestamp.format>
        <timestamp>${maven.build.timestamp}</timestamp>

        <hazelcast.distribution>Hazelcast</hazelcast.distribution>
        <hazelcast.serialization.version>1</hazelcast.serialization.version>
        <hazelcast.serverMainClass>com.hazelcast.core.server.HazelcastMemberStarter</hazelcast.serverMainClass>
        <hazelcast.previous.version>5.2.0</hazelcast.previous.version>

        <maven.compiler.plugin.version>3.11.0</maven.compiler.plugin.version>
        <maven.jar.plugin.version>3.3.0</maven.jar.plugin.version>
        <maven.source.plugin.version>3.2.1</maven.source.plugin.version>
        <maven.javadoc.plugin.version>3.5.0</maven.javadoc.plugin.version>
        <maven.javadoc.plugin.excludePackageNames>
            *.impl:*.impl.*:*.internal:*.internal.*:*.operations:*.proxy:
            com.hazelcast.aws.security:*.handlermigration:*.client.connection.nio:
            *.client.console:*.client.protocol.generator:*.cluster.client:
            *.concurrent:*.nio.ascii:*.nio.ssl:*.nio.tcp:*.partition.client:
            *.transaction.client:*.core.server:com.hazelcast.instance:com.hazelcast.PlaceHolder
        </maven.javadoc.plugin.excludePackageNames>
        <maven.antrun.plugin.version>3.0.0</maven.antrun.plugin.version>
        <maven.gpg.plugin.version>3.0.1</maven.gpg.plugin.version>
        <maven.assembly.plugin.version>3.5.0</maven.assembly.plugin.version>
        <maven.rar.plugin.version>2.2</maven.rar.plugin.version>
        <maven.bundle.plugin.version>2.4.0</maven.bundle.plugin.version>
        <maven.shade.plugin.version>3.2.4</maven.shade.plugin.version>
        <maven.dependency.plugin.version>3.5.0</maven.dependency.plugin.version>
        <maven.animal.sniffer.plugin.version>1.22</maven.animal.sniffer.plugin.version>
        <maven.git.commit.id.plugin.version>2.1.10</maven.git.commit.id.plugin.version>

        <maven.surefire.plugin.version>3.0.0</maven.surefire.plugin.version>
        <maven.checkstyle.plugin.version>3.2.2</maven.checkstyle.plugin.version>
        <maven.jacoco.plugin.version>0.8.8</maven.jacoco.plugin.version>
        <maven.failsafe.plugin.version>3.0.0</maven.failsafe.plugin.version>
        <maven.cobertura.plugin.version>2.7</maven.cobertura.plugin.version>
        <maven.enforcer.plugin.version>3.2.1</maven.enforcer.plugin.version>
        <maven.os.plugin.version>1.7.1</maven.os.plugin.version>
        <maven.protobuf.plugin.version>0.6.1</maven.protobuf.plugin.version>
        <maven.resources.plugin.version>3.3.0</maven.resources.plugin.version>
        <owasp.dependency-check.version>8.4.2</owasp.dependency-check.version>
        <maven.dependency.plugin.version>3.1.2</maven.dependency.plugin.version>
        <codehause.license.plugin.version>2.0.1</codehause.license.plugin.version>

        <!-- Version of the client used in Hazelcast 3 connector -->
        <hazelcast-3.version>3.12.13</hazelcast-3.version>

        <!-- Third-party dependencies (sorted alphabetically) -->
        <!--<affinity.version>3.2.3</affinity.version>-->
        <antlr4.version>4.9.3</antlr4.version>
        <avro.version>1.11.3</avro.version>
        <aws.sdk.version>1.12.688</aws.sdk.version>
        <calcite.version>1.32.0</calcite.version>
        <classgraph.version>4.8.158</classgraph.version>
        <debezium.version>1.9.7.Final</debezium.version>
        <grpc.version>1.60.0</grpc.version>
        <guava.version>32.0.1-jre</guava.version>
        <hadoop.version>3.3.5</hadoop.version>
        <h2.version>2.1.214</h2.version>
        <!-- The Jackson version must match the version in EE, if you change this you must send EE PR as well -->
        <jackson.version>2.15.2</jackson.version>
        <jackson.mapper.asl.version>1.9.14.jdk17-redhat-00001</jackson.mapper.asl.version>
        <jaxb.version>2.3.1</jaxb.version>
        <janino.version>3.1.10</janino.version>
        <jline.version>3.23.0</jline.version>
        <jms.api.version>2.0.1</jms.api.version>
        <json-surfer.version>0.11</json-surfer.version>
        <jsr107.api.version>1.1.1</jsr107.api.version> <!-- JCache -->
        <jsr250.api.version>1.2</jsr250.api.version> <!-- javax.annotations -->
        <kafka.version>3.4.0</kafka.version>
        <kotlin.version>1.8.21</kotlin.version>
        <reload4j.version>1.2.24</reload4j.version>
        <log4j2.version>2.20.0</log4j2.version>
        <mysql.connector.version>8.0.30</mysql.connector.version>
        <netty.version>4.1.100.Final</netty.version>
        <objenesis.version>3.3</objenesis.version>
        <osgi.version>4.2.0</osgi.version>
        <parquet.version>1.13.0</parquet.version>
        <picocli.version>4.7.3</picocli.version>
        <postgresql.version>42.7.3</postgresql.version>
        <prometheus.version>0.18.0</prometheus.version>

        <!--
        Protobuf libraries for each language have own major version, but share minor and patch
        See https://developers.google.com/protocol-buffers/docs/news/2022-05-06
        -->
        <protobuf.version>22.0</protobuf.version>
        <java.protobuf.version>3.22.1</java.protobuf.version>
        <python.protobuf.version>4.${protobuf.version}</python.protobuf.version>

        <scala.version>2.13</scala.version>
        <slf4j.version>1.7.36</slf4j.version>
        <spring.version>4.3.0.RELEASE</spring.version>
        <snakeyaml.version>2.0</snakeyaml.version>
        <snakeyaml.engine.version>2.6</snakeyaml.engine.version>
        <mongodb.version>4.9.1</mongodb.version>

        <!-- test dependencies -->
        <activemq-artemis.version>2.11.0</activemq-artemis.version>
        <activemq.version>5.15.11</activemq.version>
        <assertj.version>3.24.2</assertj.version>
        <atomikos.version>3.9.3</atomikos.version>
        <bytebuddy.version>1.14.4</bytebuddy.version>
        <commons-codec.version>1.15</commons-codec.version>
        <commons-io.version>2.11.0</commons-io.version>
        <commons-lang3.version>3.12.0</commons-lang3.version>
        <felix.utils.version>1.11.6</felix.utils.version>
        <findbugs.annotations.version>3.0.1u2</findbugs.annotations.version>
        <hamcrest.version>1.3</hamcrest.version>
        <http.core.version>4.4.16</http.core.version>
        <http.client.version>4.5.14</http.client.version>
        <jsr107.tck.version>1.1.1</jsr107.tck.version>
        <junit.version>4.13.2</junit.version>
        <mockito.version>3.6.0</mockito.version>
        <powermock.version>2.0.9</powermock.version>
        <reflections.version>0.9.10</reflections.version>
        <testcontainers.version>1.18.1</testcontainers.version>
        <jna.version>5.13.0</jna.version>
        <!--  When updating archunit.version, check/remove Java version assumptions in ArchUnitRulesTest.java -->
        <archunit.version>1.0.1</archunit.version>
        <errorprone.version>2.19.1</errorprone.version>
        <awaitility.version>4.2.0</awaitility.version>
        <kubernetes-server-mock.version>6.5.0</kubernetes-server-mock.version>
        <hikari.version>4.0.3</hikari.version>

        <maven.test.redirectTestOutputToFile>true</maven.test.redirectTestOutputToFile>

        <!-- The path to relocated external dependencies. -->
        <relocation.root>com.hazelcast.shaded</relocation.root>

        <!-- Additional JVM system arguments -->
        <extraVmArgs/>
        <vmHeapSettings>-Xms512m -Xmx2G</vmHeapSettings>
        <!-- Java 9+ module system args to be appended during surefire/failsafe executions. -->
        <hazelcast.module.name>ALL-UNNAMED</hazelcast.module.name>
        <javaModuleArgs/>

        <!-- needed for CheckStyle -->
        <checkstyle.version>8.38</checkstyle.version>
        <checkstyle.configLocation>${main.basedir}/checkstyle/checkstyle.xml</checkstyle.configLocation>
        <checkstyle.supressionsLocation>${main.basedir}/checkstyle/suppressions.xml</checkstyle.supressionsLocation>
        <checkstyle.headerLocation>${main.basedir}/checkstyle/ClassHeaderApache.txt</checkstyle.headerLocation>

        <shade.jar-with-dependencies.phase>package</shade.jar-with-dependencies.phase>
        <fat.dependency.classifier>jar-with-dependencies</fat.dependency.classifier>
    </properties>
    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <scm>
        <connection>scm:git:git://github.com/hazelcast/hazelcast.git</connection>
        <developerConnection>scm:git:**************:hazelcast/hazelcast.git</developerConnection>
        <url>https://github.com/hazelcast/hazelcast/</url>
    </scm>
    <developers>
        <developer>
            <id>hazelcast-team</id>
            <name>Hazelcast team</name>
            <email><EMAIL></email>
            <organization>Hazelcast, Inc.</organization>
            <organizationUrl>https://hazelcast.com</organizationUrl>
        </developer>
    </developers>

    <contributors>
        <contributor>
            <name>Hazelcast Community</name>
            <email><EMAIL></email>
            <url>https://hazelcast.atlassian.net/wiki/display/COM/Hazelcast+Contributor+Agreement</url>
        </contributor>
    </contributors>

    <issueManagement>
        <system>Github</system>
        <url>https://github.com/hazelcast/hazelcast/issues</url>
    </issueManagement>
    <organization>
        <name>Hazelcast, Inc.</name>
        <url>http://www.hazelcast.com/</url>
    </organization>

    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
        <outputDirectory>${target.dir}/classes</outputDirectory>
        <testSourceDirectory>src/test/java</testSourceDirectory>
        <testOutputDirectory>${target.dir}/test-classes</testOutputDirectory>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.html</include>
                    <include>**/*.bat</include>
                    <include>**/*.sh</include>
                    <include>**/*.py</include>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yaml</include>
                    <include>**/*.xsd</include>
                    <include>**/hazelcast-config-*.json</include>
                    <include>**/*.handlers</include>
                    <include>**/*.schemas</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.so</include>
                    <include>**/*.license</include>
                    <include>**/*.key</include>
                    <include>**/*.ftl</include>
                    <include>**/META-INF/services/*.*</include>
                </includes>
            </resource>
        </resources>

        <!-- ignore the testjob module -->
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <excludes>
                    <exclude>com/hazelcast/jet/server/testjob/**</exclude>
                </excludes>
            </testResource>
        </testResources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${maven.checkstyle.plugin.version}</version>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>checkstyle</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <configLocation>${checkstyle.configLocation}</configLocation>
                    <suppressionsLocation>${checkstyle.supressionsLocation}</suppressionsLocation>
                    <headerLocation>${checkstyle.headerLocation}</headerLocation>
                    <enableRSS>false</enableRSS>
                    <linkXRef>false</linkXRef>
                    <consoleOutput>true</consoleOutput>
                    <failsOnError>true</failsOnError>
                    <failOnViolation>true</failOnViolation>
                    <includeTestSourceDirectory>true</includeTestSourceDirectory>
                    <enableRulesSummary>true</enableRulesSummary>
                    <propertyExpansion>main.basedir=${main.basedir}</propertyExpansion>
                    <excludes>**/module-info.java</excludes>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>${checkstyle.version}</version>
                    </dependency>
                </dependencies>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.plugin.version}</version>
                <configuration>
                    <source>${jdk.version}</source>
                    <target>${jdk.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <!--
                    Workaround for compilation failure on Windows in hazelcast-sql module:
                    Compilation failure
                    org.immutables.value.internal.$processor$.$Processor threw com.sun.tools.javac.jvm.ClassReader$BadClassFile: bad class file: hazelcast\target\hazelcast-5.2-SNAPSHOT.jar(com/hazelcast/jet/package-info.class)
                    class file contains wrong class: com\hazelcast\jet\package-info
                    Please remove or make sure it appears in the correct subdirectory of the classpath.
                    -->
                    <excludes>
                        <exclude>**/package-info.java</exclude>
                    </excludes>
                    <testExcludes>
                        <exclude>**/package-info.java</exclude>
                    </testExcludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven.source.plugin.version}</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration combine.self="override">
                    <useFile>false</useFile>
                    <trimStackTrace>false</trimStackTrace>
                    <runOrder>failedfirst</runOrder>

                    <!-- 1C means 1 process per cpu core -->
                    <!-- forkCount>1C</forkCount>
                    <reuseForks>true</reuseForks>

                    <threadCount>1</threadCount>
                    <perCoreThreadCount>true</perCoreThreadCount>
                    <parallel>methods</parallel -->
                    <!-- JaCoCo will use the argLine set here. Test profiles override it. -->
                    <argLine>
                        ${vmHeapSettings}
                        ${javaModuleArgs}
                        -Dhazelcast.phone.home.enabled=false
                        -Dhazelcast.test.use.network=false
                        -Dlog4j.skipJansi=true
                        ${extraVmArgs}
                    </argLine>
                    <includes>
                        <include>**/**.java</include>
                    </includes>
                    <excludes>
                        <exclude>**/jsr/**.java</exclude>
                        <exclude>**/**IT.java</exclude>
                    </excludes>
                    <excludedGroups>
                        com.hazelcast.test.annotation.SlowTest,
                        com.hazelcast.test.annotation.NightlyTest
                    </excludedGroups>
                    <!-- Use TCP for IPC communication to avoid warnings: Corrupted STDOUT by directly writing... -->
                    <forkNode implementation="org.apache.maven.plugin.surefire.extensions.SurefireForkNodeFactory" />
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>${maven.failsafe.plugin.version}</version>
                <configuration combine.self="override">
                    <trimStackTrace>false</trimStackTrace>
                    <argLine>
                        ${vmHeapSettings}
                        ${javaModuleArgs}
                        -Dhazelcast.phone.home.enabled=false
                        -Dhazelcast.logging.type=none
                        -Dhazelcast.test.use.network=true
                        -Dlog4j.skipJansi=true
                        ${extraVmArgs}
                    </argLine>
                    <useManifestOnlyJar>false</useManifestOnlyJar>
                    <useSystemClassLoader>true</useSystemClassLoader>
                    <excludedGroups>
                        com.hazelcast.test.annotation.SlowTest,
                        com.hazelcast.test.annotation.NightlyTest
                    </excludedGroups>
                    <!-- Use TCP for IPC communication to avoid warnings: Corrupted STDOUT by directly writing... -->
                    <forkNode implementation="org.apache.maven.plugin.surefire.extensions.SurefireForkNodeFactory" />
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>integration-test</goal>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>${maven.enforcer.plugin.version}</version>
                <executions>
                    <execution>
                        <id>enforce-tools</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireJavaVersion>
                                    <!-- JDK 1.8+ is required for compilation. -->
                                    <version>[1.8.0,)</version>
                                </requireJavaVersion>
                                <enforceBytecodeVersion>
                                    <maxJdkVersion>1.8</maxJdkVersion>
                                    <excludes>
                                        <!--
                                        The archunit library containse several classes to process JDK9 modules.
                                        It is a test dependency and can be ignored.
                                        -->
                                        <exclude>com.tngtech.archunit:archunit</exclude>
                                    </excludes>
                                    <ignoreClasses>
                                        <!--
                                        Some dependencies contain JDK9 specific classes with JDK9 in the name
                                        e.g. wiremock.org.eclipse.jetty.alpn.java.server.JDK9ServerALPNProcessor
                                        -->
                                        <ignoreClass>*JDK9*</ignoreClass>
                                        <!--
                                        Classes from org:mongodb:bson-record-codec to map record to bson.
                                        Ignoring whole package, because we shade the module into hazelcast-jet-mongodb.
                                        -->
                                        <ignoreClass>org.bson.codecs.record.*</ignoreClass>
                                    </ignoreClasses>
                                </enforceBytecodeVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>extra-enforcer-rules</artifactId>
                        <!--
                        Version 1.6.1 produces harmless, but noisy warning about missing dependencies.
                        See https://github.com/mojohaus/extra-enforcer-rules/issues/225
                        -->
                        <version>1.5.1</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-notice-and-license</id>
                        <phase>none</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.outputDirectory}/META-INF</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>..</directory>
                                    <includes>
                                        <include>NOTICE</include>
                                    </includes>
                                </resource>
                                <resource>
                                    <!-- Don't include the top level LICENSE file which is valid for whole repository
                                         use rather the ASF license file located in hazelcast-build-utils module.-->
                                    <directory>../hazelcast-build-utils/src/main/resources</directory>
                                    <includes>
                                        <include>LICENSE</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>license-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-third-party</id>
                        <goals>
                            <goal>add-third-party</goal>
                        </goals>
                        <phase>generate-resources</phase>
                        <configuration>
                            <outputDirectory>${project.build.outputDirectory}/META-INF</outputDirectory>
                            <failOnBlacklist>true</failOnBlacklist>
                            <failOnMissing>true</failOnMissing>
                            <fileTemplate>${main.basedir}/hazelcast-build-utils/src/main/resources/hazelcast-thirdparty-template.ftl</fileTemplate>
                            <includedLicenses>
                                <!-- 
                                  Always use SPDX license identifiers listed in https://spdx.org/licenses (when available)
                                  note: GPL licenses are excluded in https://github.com/hazelcast/hazelcast/blob/master/hazelcast-build-utils/src/main/resources/hazelcast-thirdparty-template.ftl
                                -->
                                <includedLicense>Apache-2.0</includedLicense>
                                <includedLicense>MIT</includedLicense>
                                <includedLicense>BSD-2-Clause</includedLicense>
                                <includedLicense>BSD-3-Clause</includedLicense>
                                <includedLicense>CC0-1.0</includedLicense>
                                <includedLicense>CDDL</includedLicense>
                                <includedLicense>Eclipse Distribution License - v 1.0</includedLicense>
                                <includedLicense>EPL-1.0</includedLicense>
                                <includedLicense>EPL-2.0</includedLicense>
                                <includedLicense>JSON</includedLicense>
                                <includedLicense>Public Domain</includedLicense>
                            </includedLicenses>
                            <licenseMerges>
                                <!-- 
                                  Only merge new 3rd Party dependency license if you get a license build failure
                                -->
                                <licenseMerge>
                                    Apache-2.0 |
                                    Apache License v2.0 |
                                    Apache Software License 2.0 |
                                    The Apache Software License, Version 2.0 |
                                    The Apache Software License, version 2.0 |
                                    Apache License, Version 2.0 |
                                    Apache 2 |
                                    Apache 2.0 |
                                    The Apache License, Version 2.0 |
                                    Apache Software License - Version 2.0 |
                                    Apache License Version 2.0 |
                                    Apache License 2.0
                                </licenseMerge>
                                <licenseMerge>
                                    MIT |
                                    The MIT License |
                                    MIT License |
                                    MIT license |
                                    The MIT License (MIT)
                                </licenseMerge>
                                <licenseMerge>
                                    BSD-2-Clause |
                                    BSD 2-Clause License |
                                    BSD 2-Clause license
                                </licenseMerge>
                                <licenseMerge>
                                    BSD-3-Clause |
                                    BSD 3-Clause License |
                                    BSD |
                                    BSD licence |
                                    The BSD License |
                                    3-Clause BSD License |
                                    New BSD license |
                                    The New BSD License |
                                    Revised BSD |
                                    BSD New license |
                                    BSD 3-clause
                                </licenseMerge>
                                <licenseMerge>
                                    CC0-1.0 |
                                    CC0 |
                                    Public Domain, per Creative Commons CC0
                                </licenseMerge>
                                <licenseMerge>
                                    CDDL |
                                    CDDL + GPLv2 with classpath exception |
                                    CDDL License |
                                    CDDL/GPLv2+CE
                                </licenseMerge>
                                <licenseMerge>
                                    Eclipse Distribution License - v 1.0 |
                                    EDL 1.0
                                </licenseMerge>
                                <licenseMerge>
                                    EPL-1.0 |
                                    EPL 1.0 |
                                    Eclipse Public License - Version 1.0
                                </licenseMerge>
                                <licenseMerge>
                                    EPL-2.0 |
                                    EPL 2.0 |
                                    Eclipse Public License - Version 2.0 |
                                    Eclipse Public License, Version 2.0
                                </licenseMerge>
                                <licenseMerge>
                                    JSON |
                                    The JSON License
                                </licenseMerge>
                            </licenseMerges>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <excludedGroups>com.hazelcast</excludedGroups>
                    <excludedScopes>test,provided</excludedScopes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.owasp</groupId>
                <artifactId>dependency-check-maven</artifactId>
                <version>${owasp.dependency-check.version}</version>
                <configuration>
                    <format>ALL</format>
                    <skipProvidedScope>true</skipProvidedScope>
                    <nodeAuditAnalyzerEnabled>false</nodeAuditAnalyzerEnabled>
                    <suppressionFiles>owasp-check-suppressions.xml</suppressionFiles>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>${maven.dependency.plugin.version}</version>
                <executions>
                    <execution>
                        <id>get-hz-3</id>
                        <goals>
                            <goal>get</goal>
                        </goals>
                        <phase>generate-test-resources</phase>
                        <configuration>
                            <artifact>com.hazelcast:hazelcast:${hazelcast-3.version}</artifact>
                            <artifact>com.hazelcast:hazelcast:${hazelcast-3.version}:jar:tests</artifact>

                            <!-- Below are the jars used in compatibility tests. Install them to local repository -->
                            <artifact>com.hazelcast:hazelcast:3.12.13</artifact>
                            <artifact>com.hazelcast:hazelcast:3.12.13:jar:tests</artifact>

                            <artifact>com.hazelcast:hazelcast:4.0</artifact>
                            <artifact>com.hazelcast:hazelcast:4.0:jar:tests</artifact>

                            <artifact>com.hazelcast:hazelcast:4.0.1</artifact>
                            <artifact>com.hazelcast:hazelcast:4.0.1:jar:tests</artifact>

                            <artifact>com.hazelcast:hazelcast:4.0.2</artifact>
                            <artifact>com.hazelcast:hazelcast:4.0.2:jar:tests</artifact>

                            <artifact>com.hazelcast:hazelcast:4.0.3</artifact>
                            <artifact>com.hazelcast:hazelcast:4.0.3:jar:tests</artifact>

                            <artifact>com.hazelcast:hazelcast:${hazelcast.previous.version}</artifact>
                            <artifact>com.hazelcast:hazelcast:${hazelcast.previous.version}:jar:tests</artifact>

                        </configuration>
                    </execution>
                </executions>
                <inherited>false</inherited>
            </plugin>
            <plugin>
                <groupId>com.hazelcast.maven</groupId>
                <artifactId>attribution-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>aggregated-attribution</id>
                        <inherited>false</inherited>
                        <goals>
                            <goal>aggregate</goal>
                        </goals>
                        <configuration>
                            <outputFile>${project.build.directory}/aggregated-attribution.txt</outputFile>
                        </configuration>
                    </execution>
                    <execution>
                        <id>per-jar-attribution</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <phase>compile</phase>
                        <configuration>
                            <outputFile>${project.build.outputDirectory}/META-INF/attribution.txt</outputFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>animal-sniffer-maven-plugin</artifactId>
                <configuration>
                    <checkTestClasses>true</checkTestClasses>
                    <signature>
                        <groupId>org.codehaus.mojo.signature</groupId>
                        <artifactId>java18</artifactId>
                        <version>1.0</version>
                    </signature>
                    <ignores>
                        <ignore>java.lang.invoke.MethodHandle</ignore>
                        <ignore>sun.misc.Unsafe</ignore>
                    </ignores>
                </configuration>
                <executions>
                    <execution>
                        <id>source-java8-check</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>${maven.shade.plugin.version}</version>
                    <configuration>
                        <filters>
                            <filter>
                                <artifact>*:*</artifact>
                                <excludes>
                                    <exclude>module-info.class</exclude>
                                    <exclude>META-INF/*.SF</exclude>
                                    <exclude>META-INF/*.DSA</exclude>
                                    <exclude>META-INF/*.RSA</exclude>
                                </excludes>
                            </filter>
                        </filters>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>${maven.javadoc.plugin.version}</version>
                    <configuration>
                        <maxmemory>1024</maxmemory>
                        <excludePackageNames>${maven.javadoc.plugin.excludePackageNames}</excludePackageNames>
                        <doclint>-missing</doclint>
                    </configuration>
                    <executions>
                        <execution>
                            <id>attach-javadocs</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven.surefire.plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${maven.jacoco.plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven.jar.plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven.resources.plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <version>${codehause.license.plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.hazelcast.maven</groupId>
                    <artifactId>attribution-maven-plugin</artifactId>
                    <version>1.2.1</version>
                    <configuration>
                        <exclusionPatterns>
                            <!--
                              Exclude false positives from the attribution.txt files. Some lines matching the `copyrightPattern`
                              are actually not the copyright lines. Let's exclude them here.

                              Example:
                              com.fasterxml.jackson.core:jackson-databind:2.12.1
                                 (c) per-property override (from annotation on specific property or
                             -->
                            <exclusionPattern>^Copyright Laws of the United States.</exclusionPattern>
                            <exclusionPattern>per-property override</exclusionPattern>
                            <exclusionPattern>^copyright notice</exclusionPattern>
                            <exclusionPattern>FAILED_PRECONDITION</exclusionPattern>
                            <exclusionPattern>The ASF licenses this file to you under the Apache License</exclusionPattern>
                            <exclusionPattern>Substitute for l, l</exclusionPattern>
                            <exclusionPattern>inside edge crossings that are between</exclusionPattern>
                        </exclusionPatterns>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>animal-sniffer-maven-plugin</artifactId>
                    <version>${maven.animal.sniffer.plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <!-- used by the PR builder -->
            <id>parallelTest</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${maven.surefire.plugin.version}</version>
                        <executions>
                            <execution>
                                <id>default-test</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration combine.self="override">
                                    <useFile>false</useFile>
                                    <trimStackTrace>false</trimStackTrace>
                                    <!-- 0.5C means half as many forks as cpu cores -->
                                    <forkCount>0.5C</forkCount>
                                    <reuseForks>true</reuseForks>
                                    <argLine>
                                        ${vmHeapSettings}
                                        ${javaModuleArgs}
                                        -ea
                                        -Dhazelcast.phone.home.enabled=false
                                        -Dhazelcast.logging.type=none
                                        -Dhazelcast.test.use.network=false
                                        -Dhazelcast.test.multiple.jvm=true
                                        -Dlog4j.configurationFile=log4j2.xml
                                        -Dlog4j.skipJansi=true
                                        ${extraVmArgs}
                                    </argLine>
                                    <includes>
                                        <include>**/**.java</include>
                                    </includes>
                                    <excludes>
                                        <exclude>**/jsr/**.java</exclude>
                                        <exclude>**/**IT.java</exclude>
                                    </excludes>
                                    <groups>
                                        com.hazelcast.test.annotation.ParallelJVMTest
                                    </groups>
                                    <excludedGroups>
                                        com.hazelcast.test.annotation.SlowTest,
                                        com.hazelcast.test.annotation.NightlyTest
                                    </excludedGroups>
                                    <systemPropertyVariables>
                                        <multipleJVM>true</multipleJVM>
                                    </systemPropertyVariables>
                                </configuration>
                            </execution>
                            <execution>
                                <id>singlejvm</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <configuration combine.self="override">
                                    <useFile>false</useFile>
                                    <trimStackTrace>false</trimStackTrace>
                                    <argLine>
                                        ${vmHeapSettings}
                                        ${javaModuleArgs}
                                        -Dhazelcast.phone.home.enabled=false
                                        -Dhazelcast.logging.type=none
                                        -Dhazelcast.test.use.network=false
                                        -Dlog4j.configurationFile=log4j2.xml
                                        -Dlog4j.skipJansi=true
                                        ${extraVmArgs}
                                    </argLine>
                                    <includes>
                                        <include>**/**.java</include>
                                    </includes>
                                    <excludes>
                                        <exclude>**/jsr/**.java</exclude>
                                        <exclude>**/**IT.java</exclude>
                                    </excludes>
                                    <excludedGroups>
                                        com.hazelcast.test.annotation.SlowTest,
                                        com.hazelcast.test.annotation.NightlyTest,
                                        com.hazelcast.test.annotation.ParallelJVMTest
                                    </excludedGroups>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <version>${maven.failsafe.plugin.version}</version>
                        <configuration combine.self="override">
                            <useFile>false</useFile>
                            <trimStackTrace>false</trimStackTrace>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.logging.type=none
                                -Dhazelcast.test.use.network=false
                                -Dlog4j.configurationFile=log4j2.xml
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <useManifestOnlyJar>false</useManifestOnlyJar>
                            <useSystemClassLoader>true</useSystemClassLoader>
                            <excludedGroups>
                                com.hazelcast.test.annotation.SlowTest,
                                com.hazelcast.test.annotation.NightlyTest
                            </excludedGroups>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>integration-test</goal>
                                    <goal>verify</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>pr-builder</id>
            <properties>
                <assembly.skipAssembly>true</assembly.skipAssembly>
                <!-- Set the classifier to empty in this profile to use regular jar-->
                <fat.dependency.classifier/>
                <shade.jar-with-dependencies.phase>none</shade.jar-with-dependencies.phase>
            </properties>
        </profile>

        <profile>
            <id>LOCAL</id>
            <properties>
                <vmHeapSettings>-Xms128m -Xmx1G</vmHeapSettings>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${maven.surefire.plugin.version}</version>
                        <configuration combine.self="override">
                            <useFile>false</useFile>
                            <trimStackTrace>false</trimStackTrace>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.test.use.network=false
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <includes>
                                <include>**/**.java</include>
                            </includes>
                            <excludes>
                                <exclude>**/jsr/**.java</exclude>
                                <exclude>**/**IT.java</exclude>
                            </excludes>
                            <excludedGroups>
                                com.hazelcast.test.annotation.SlowTest,
                                com.hazelcast.test.annotation.NightlyTest
                            </excludedGroups>
                        </configuration>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <version>${maven.failsafe.plugin.version}</version>
                        <configuration combine.self="override">
                            <useFile>false</useFile>
                            <trimStackTrace>false</trimStackTrace>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.test.use.network=false
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <useManifestOnlyJar>false</useManifestOnlyJar>
                            <useSystemClassLoader>true</useSystemClassLoader>
                            <excludedGroups>
                                com.hazelcast.test.annotation.SlowTest,
                                com.hazelcast.test.annotation.NightlyTest
                            </excludedGroups>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>integration-test</goal>
                                    <goal>verify</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>test-coverage-Local</id>
            <properties>
                <vmHeapSettings>-Xms128m -Xmx1G</vmHeapSettings>
            </properties>
            <modules>
                <module>hazelcast-coverage-report</module>
            </modules>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>cobertura-maven-plugin</artifactId>
                        <version>${maven.cobertura.plugin.version}</version>
                        <configuration>
                            <instrumentation>
                                <ignores/>
                                <excludes/>
                            </instrumentation>
                            <check/>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>clean</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${maven.surefire.plugin.version}</version>
                        <configuration combine.self="override">
                            <useFile>false</useFile>
                            <trimStackTrace>false</trimStackTrace>
                            <testFailureIgnore>true</testFailureIgnore>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.logging.type=none
                                -Dhazelcast.test.use.network=false
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <includes>
                                <include>**/YourTestFileHear.java</include>
                            </includes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
            <reporting>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>cobertura-maven-plugin</artifactId>
                        <version>${maven.cobertura.plugin.version}</version>
                    </plugin>
                </plugins>
            </reporting>
        </profile>

        <profile>
            <id>test-coverage</id>
            <modules>
                <module>hazelcast-coverage-report</module>
            </modules>
            <properties>
                <argLine>
                    ${vmHeapSettings}
                    ${javaModuleArgs}
                    -Dhazelcast.phone.home.enabled=false
                    -Dhazelcast.logging.type=none
                    -Dhazelcast.test.use.network=false
                    -Dhazelcast.operation.call.timeout.millis=120000
                    -Dhazelcast.graceful.shutdown.max.wait=240
                    -Dlog4j.skipJansi=true
                    ${extraVmArgs}
                </argLine>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <version>${maven.jacoco.plugin.version}</version>
                        <configuration>
                            <excludes>
                                <exclude>**DummyProperty**</exclude>
                                <exclude>**DummyEntity**</exclude>
                                <exclude>**test-hibernate**</exclude>
                                <exclude>**HibernateStatisticsTestSupport**</exclude>
                                <exclude>**HibernateTestSupport**</exclude>
                                <exclude>**RegionFactoryDefaultTest**</exclude>
                            </excludes>
                        </configuration>
                        <executions>
                            <execution>
                                <id>prepare-agent</id>
                                <goals>
                                    <goal>prepare-agent</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${maven.surefire.plugin.version}</version>
                        <configuration combine.self="override">
                            <useFile>false</useFile>
                            <trimStackTrace>false</trimStackTrace>
                            <testFailureIgnore>true</testFailureIgnore>
                            <includes>
                                <include>**/*.java</include>
                            </includes>
                            <excludes>
                                <exclude>**/**IT.java</exclude>
                            </excludes>
                            <excludedGroups>
                                com.hazelcast.test.annotation.NightlyTest
                            </excludedGroups>
                        </configuration>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <version>${maven.failsafe.plugin.version}</version>
                        <configuration combine.self="override">
                            <trimStackTrace>false</trimStackTrace>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.logging.type=none
                                -Dhazelcast.test.use.network=true
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <excludedGroups>
                                com.hazelcast.test.annotation.NightlyTest
                            </excludedGroups>
                            <useManifestOnlyJar>false</useManifestOnlyJar>
                            <useSystemClassLoader>true</useSystemClassLoader>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>integration-test</goal>
                                    <goal>verify</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>release</id>
            <properties>
                <javadoc>true</javadoc>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>${maven.gpg.plugin.version}</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <gpgArguments>
                                <gpgArgument>--pinentry-mode</gpgArgument>
                                <gpgArgument>loopback</gpgArgument>
                            </gpgArguments>
                        </configuration>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                    </plugin>

                    <plugin>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>nexus-staging-maven-plugin</artifactId>
                        <!-- do not update to 1.6.10, it blocks release process -->
                        <version>1.6.8</version>
                        <extensions>true</extensions>
                        <configuration>
                            <serverId>release-repository</serverId>
                            <nexusUrl>https://oss.sonatype.org/</nexusUrl>
                            <autoReleaseAfterClose>true</autoReleaseAfterClose>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <!-- unfortunately it isn't possible to chain profiles, so we need to duplicate the javadoc plugin -->
        <profile>
            <id>release-snapshot</id>
            <properties>
                <javadoc>true</javadoc>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>release-devel</id>
            <properties>
                <javadoc>true</javadoc>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
            <distributionManagement>
                <repository>
                    <id>devel-repository</id>
                    <url>https://hazelcast.jfrog.io/artifactory/devel</url>
                </repository>
            </distributionManagement>
        </profile>

        <profile>
            <id>nightly-build</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${maven.surefire.plugin.version}</version>
                        <configuration combine.self="override">
                            <parallel>none</parallel>
                            <useFile>false</useFile>
                            <trimStackTrace>false</trimStackTrace>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.logging.type=none
                                -Dhazelcast.test.use.network=false
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <includes>
                                <include>**/**.java</include>
                            </includes>
                            <excludes>
                                <exclude>**/**IT.java</exclude>
                                <exclude>**/jsr/**.java</exclude>
                            </excludes>
                            <groups>
                                com.hazelcast.test.annotation.NightlyTest,
                                com.hazelcast.test.annotation.SlowTest
                            </groups>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <version>${maven.failsafe.plugin.version}</version>
                        <configuration combine.self="override">
                            <trimStackTrace>false</trimStackTrace>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.logging.type=none
                                -Dhazelcast.test.use.network=true
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <useManifestOnlyJar>false</useManifestOnlyJar>
                            <useSystemClassLoader>true</useSystemClassLoader>
                            <groups>
                                com.hazelcast.test.annotation.NightlyTest,
                                com.hazelcast.test.annotation.SlowTest
                            </groups>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>integration-test</goal>
                                    <goal>verify</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>all-tests</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${maven.surefire.plugin.version}</version>
                        <configuration combine.self="override">
                            <useFile>false</useFile>
                            <trimStackTrace>false</trimStackTrace>
                            <parallel>none</parallel>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.logging.type=none
                                -Dhazelcast.test.use.network=false
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <includes>
                                <include>**/**.java</include>
                            </includes>
                            <excludes>
                                <exclude>**/jsr/**.java</exclude>
                                <exclude>**/**IT.java</exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <version>${maven.failsafe.plugin.version}</version>
                        <configuration combine.self="override">
                            <trimStackTrace>false</trimStackTrace>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.logging.type=none
                                -Dhazelcast.test.use.network=true
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <useManifestOnlyJar>false</useManifestOnlyJar>
                            <useSystemClassLoader>true</useSystemClassLoader>
                        </configuration>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>integration-test</goal>
                                    <goal>verify</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>jdk-9</id>
            <activation>
                <jdk>[9,)</jdk>
            </activation>
            <properties>
                <!--
                Allow access to Operating system metrics:
                   open jdk.management/com.sun.management.internal
                   export jdk.management/com.ibm.lang.management.internal

                Avoid warnings caused by reflection in
                SelectorOptimizer:
                   open java.base/sun.nio.ch
                FilteringClassLoader:
                   open java.base/java.lang
                TimedMemberStateFactoryHelper:
                   open java.management/sun.management

                Powermock issue workaround (https://github.com/powermock/powermock/issues/905):
                   export java.xml/jdk.xml.internal
                -->
                <javaModuleArgs>
                    --add-exports java.base/jdk.internal.ref=${hazelcast.module.name}
                    --add-opens jdk.management/com.sun.management.internal=${hazelcast.module.name}
                    --add-opens java.base/sun.nio.ch=${hazelcast.module.name}
                    --add-opens java.base/java.lang=${hazelcast.module.name}
                    --add-opens java.management/sun.management=${hazelcast.module.name}
                    --add-exports java.xml/jdk.xml.internal=${hazelcast.module.name}
                    --add-exports jdk.management/com.ibm.lang.management.internal=${hazelcast.module.name}
                    --illegal-access=deny
                </javaModuleArgs>
            </properties>
        </profile>

        <profile>
            <id>modulepath-tests</id>
            <activation>
                <property>
                    <name>!quick</name>
                </property>
                <jdk>[9,)</jdk>
            </activation>
            <modules>
                <module>modulepath-tests</module>
            </modules>
        </profile>

        <profile>
            <!-- same as default build (excludes Nightly & Slow tests), outputs serialized objects to a blob -->
            <!-- for compatibility testing -->
            <id>generate-compatibility-samples</id>
            <properties>
                <vmHeapSettings>-Xms128m -Xmx1G</vmHeapSettings>
            </properties>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>${maven.surefire.plugin.version}</version>
                        <configuration combine.self="override">
                            <properties>
                                <property>
                                    <name>listener</name>
                                    <value>com.hazelcast.test.compatibility.SamplingRunListener</value>
                                </property>
                            </properties>
                            <trimStackTrace>false</trimStackTrace>
                            <runOrder>failedfirst</runOrder>
                            <argLine>
                                ${vmHeapSettings}
                                ${javaModuleArgs}
                                -Dhazelcast.phone.home.enabled=false
                                -Dhazelcast.test.use.network=false
                                -Dhazelcast.test.sample.serialized.objects=${target.dir}/serialized-objects-${project.version}-
                                -Dlog4j.skipJansi=true
                                ${extraVmArgs}
                            </argLine>
                            <includes>
                                <include>**/**.java</include>
                            </includes>
                            <excludes>
                                <exclude>**/jsr/**.java</exclude>
                                <!-- code triggered by UserCodeDeploymentSmokeTest and OperationRunnerImplTest -->
                                <!-- casts to SerializationServiceV1 and fails when Node is configured with the -->
                                <!-- sampling serialization service -->
                                <exclude>**/OperationRunnerImplTest.java</exclude>
                                <exclude>**/**IT.java</exclude>
                                <exclude>**/jet/**/*.java</exclude>
                                <exclude>**/*Jet*.java</exclude>
                            </excludes>
                            <excludedGroups>
                                com.hazelcast.test.annotation.SlowTest,
                                com.hazelcast.test.annotation.NightlyTest,
                                com.hazelcast.test.annotation.SerializationSamplesExcluded
                            </excludedGroups>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>not-quick</id>
            <activation>
                <property>
                    <name>!quick</name>
                </property>
            </activation>
            <modules>
                <module>extensions</module>
                <module>distribution</module>
                <module>hazelcast-it</module>
            </modules>
        </profile>
        <profile>
            <id>quick</id>
            <activation>
                <property>
                    <name>quick</name>
                </property>
            </activation>
            <properties>
                <checkstyle.skip>true</checkstyle.skip>
                <skipTests>true</skipTests>
                <maven.javadoc.skip>true</maven.javadoc.skip>
                <maven.source.skip>true</maven.source.skip>
            </properties>
        </profile>
    </profiles>

    <distributionManagement>
        <repository>
            <id>release-repository</id>
            <url>https://oss.sonatype.org/service/local/staging/deploy/maven2</url>
        </repository>
        <snapshotRepository>
            <id>snapshot-repository</id>
            <name>Maven2 Snapshot Repository</name>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
            <uniqueVersion>false</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <!--
            This is the same as central in the super pom.
            Putting it here changes the order in which the repositories are queried.
            Most artefacts are stored in central so this provides best build times when a mirror is not used.
            Repository order reference:
            https://maven.apache.org/guides/mini/guide-multiple-repositories.html#repository-order
            -->
            <id>central</id>
            <name>Central Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>snapshot-repository</id>
            <name>Maven2 Snapshot Repository</name>
            <url>https://oss.sonatype.org/content/repositories/snapshots</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>confluent</id>
            <url>https://packages.confluent.io/maven/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>hazelcast-security</id>
            <name>Thirdparty security fixes</name>
            <url>https://repository.hazelcast.com/security-maven/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <!-- Red Hat Maven repository provides patches for several vulnerable libraries (dependencies) which are not patched in the Maven Central repository. -->
        <repository>
            <id>redhat-ga</id>
            <url>https://maven.repository.redhat.com/ga/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>never</updatePolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <dependencyManagement>
        <dependencies>
            <!--
            Pin dependencies to particular version.

            This doesn't add dependencies for modules, but if any of the modules uses any listed dependency, either
            directly or transitively, it will use the version specified here.
            This ensures that all jars with dependencies use the same version of a particular dependency, both for tests
            and for inclusion to the jar with dependencies.

            The enforcer plugin checks that no two modules have a dependency conflict, if they do simply add an entry
            with conflicting dependency here, using the higher version.
            -->

            <!-- Import versions from bom - avoids to importing many modules for certain dependencies, e.g. Jackson -->
            <dependency>
                <groupId>com.fasterxml.jackson</groupId>
                <artifactId>jackson-bom</artifactId>
                <version>${jackson.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-bom</artifactId>
                <version>9.4.53.v20231009</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>${netty.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-bom</artifactId>
                <version>${java.protobuf.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-bom</artifactId>
                <version>${grpc.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.auth</groupId>
                <artifactId>google-auth-library-bom</artifactId>
                <version>1.16.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.http-client</groupId>
                <artifactId>google-http-client-bom</artifactId>
                <version>1.43.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.google.oauth-client</groupId>
                <artifactId>google-oauth-client-bom</artifactId>
                <version>1.34.1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-client</artifactId>
                <version>${hadoop.version}</version>
                <exclusions>
                    <!-- The following dependencies are not needed for our use of Hadoop -->
                    <exclusion>
                        <groupId>ch.qos.reload4j</groupId>
                        <artifactId>reload4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-reload4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-servlet</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.eclipse.jetty</groupId>
                        <artifactId>jetty-webapp</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet.jsp</groupId>
                        <artifactId>jsp-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun.jersey</groupId>
                        <artifactId>jersey-servlet</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.jline</groupId>
                        <artifactId>jline</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hadoop</groupId>
                        <artifactId>hadoop-yarn-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hadoop</groupId>
                        <artifactId>hadoop-yarn-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hadoop</groupId>
                        <artifactId>hadoop-yarn-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.kerby</groupId>
                        <artifactId>kerb-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.kerby</groupId>
                        <artifactId>kerb-simplekdc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.curator</groupId>
                        <artifactId>curator-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.curator</groupId>
                        <artifactId>curator-recipes</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.curator</groupId>
                        <artifactId>curator-framework</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro-mapred</artifactId>
                <version>${avro.version}</version>
                <exclusions>
                    <!-- The following dependencies are not needed for our use of Avro -->
                    <exclusion>
                        <groupId>org.apache.avro</groupId>
                        <artifactId>avro-ipc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.avro</groupId>
                        <artifactId>avro-ipc-jetty</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.parquet</groupId>
                <artifactId>parquet-avro</artifactId>
                <version>${parquet.version}</version>
                <exclusions>
                    <!--
                    The following library is not required, it only contains annotations and doclets
                    It also requires JDK11 since version 0.14
                    -->
                    <exclusion>
                        <groupId>org.apache.yetus</groupId>
                        <artifactId>audience-annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>3.2.2</version>
            </dependency>
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.22</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.12.5</version>
            </dependency>
            <dependency>
                <groupId>org.xerial.snappy</groupId>
                <artifactId>snappy-java</artifactId>
                <version>1.1.10.5</version>
            </dependency>
            <dependency>
                <groupId>com.google.api.grpc</groupId>
                <artifactId>proto-google-common-protos</artifactId>
                <version>2.14.3</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.version}</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <!-- NOTE: httpcore and httpclient versions are not in sync -->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${http.core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${http.client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.auto.value</groupId>
                <artifactId>auto-value-annotations</artifactId>
                <version>1.10.1</version>
            </dependency>
            <dependency>
                <groupId>com.nimbusds</groupId>
                <artifactId>nimbus-jose-jwt</artifactId>
                <version>9.31</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.woodstox</groupId>
                <artifactId>woodstox-core</artifactId>
                <version>6.5.1</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.woodstox</groupId>
                <artifactId>stax2-api</artifactId>
                <version>4.2.1</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.10.1</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-bundle</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro</artifactId>
                <version>${avro.version}</version>
            </dependency>
            <dependency>
                <groupId>org.objenesis</groupId>
                <artifactId>objenesis</artifactId>
                <version>3.3</version>
            </dependency>
            <dependency>
                <groupId>com.github.luben</groupId>
                <artifactId>zstd-jni</artifactId>
                <version>1.5.5-2</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jline</groupId>
                <artifactId>jline</artifactId>
                <version>${jline.version}</version>
            </dependency>
            <dependency>
                <groupId>org.checkerframework</groupId>
                <artifactId>checker-qual</artifactId>
                <version>3.34.0</version>
            </dependency>
            <dependency>
                <groupId>org.antlr</groupId>
                <artifactId>antlr4-runtime</artifactId>
                <version>${antlr4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-mapper-asl</artifactId>
                <version>${jackson.mapper.asl.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-core-asl</artifactId>
                <version>${jackson.mapper.asl.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.reload4j</groupId>
                <artifactId>reload4j</artifactId>
                <version>${reload4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.lz4</groupId>
                <artifactId>lz4-java</artifactId>
                <version>1.8.0</version>
            </dependency>
            <dependency>
                <groupId>net.minidev</groupId>
                <artifactId>json-smart</artifactId>
                <version>2.4.10</version>
            </dependency>
            <dependency>
              <groupId>org.wildfly.openssl</groupId>
              <artifactId>wildfly-openssl</artifactId>
              <version>1.1.3.Final</version>
            </dependency>
            <dependency>
                <groupId>com.google.api-client</groupId>
                <artifactId>google-api-client</artifactId>
                <version>1.32.2</version>
            </dependency>
            <dependency>
                <groupId>com.google.api.grpc</groupId>
                <artifactId>proto-google-iam-v1</artifactId>
                <version>1.9.3</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>commons-compiler</artifactId>
                <version>${janino.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>janino</artifactId>
                <version>${janino.version}</version>
            </dependency>

            <!-- Force higher version, compatible with M1 -->
            <!-- Otherwise Docker for Java (used by TestContainers) is not able to load all JNA files for aarch64 -->
            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>${jna.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb.version}</version>
            </dependency>
            <!--
            The following are test dependencies, they are not in the final distribution.
            We put them there for the dependency convergence task to succeed.
            -->
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${bytebuddy.version}</version>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy-agent</artifactId>
                <version>${bytebuddy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.29.2-GA</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>${reflections.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>annotations</artifactId>
                <version>${findbugs.annotations.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.findbugs</groupId>
                <artifactId>jsr305</artifactId>
                <version>3.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.tngtech.archunit</groupId>
                <artifactId>archunit</artifactId>
                <version>${archunit.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.errorprone</groupId>
                <artifactId>error_prone_annotations</artifactId>
                <version>${errorprone.version}</version>
            </dependency>
            <dependency>
                <groupId>info.picocli</groupId>
                <artifactId>picocli</artifactId>
                <version>${picocli.version}</version>
            </dependency>
            <dependency>
                <groupId>org.awaitility</groupId>
                <artifactId>awaitility</artifactId>
                <version>${awaitility.version}</version>
            </dependency>
            <dependency>
                <groupId>io.fabric8</groupId>
                <artifactId>kubernetes-server-mock</artifactId>
                <version>${kubernetes-server-mock.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>javax.cache</groupId>
            <artifactId>cache-api</artifactId>
            <version>${jsr107.api.version}</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.snakeyaml</groupId>
            <artifactId>snakeyaml-engine</artifactId>
            <version>${snakeyaml.engine.version}</version>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-library</artifactId>
            <version>${hamcrest.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>${assertj.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>${powermock.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>${log4j2.version}</version>
            <scope>test</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
            <scope>provided</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>nl.jqno.equalsverifier</groupId>
            <artifactId>equalsverifier</artifactId>
            <version>3.8</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-core</artifactId>
            <version>10.0.23</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
