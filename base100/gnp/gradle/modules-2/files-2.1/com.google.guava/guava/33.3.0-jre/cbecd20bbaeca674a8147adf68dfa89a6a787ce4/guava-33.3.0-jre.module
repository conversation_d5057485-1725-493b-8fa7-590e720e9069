{"formatVersion": "1.1", "component": {"group": "com.google.guava", "module": "guava", "version": "33.3.0-jre", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"maven": {"version": "3.9.6", "buildId": "Apache Maven 3.9.6 (bc0240f3c744dd6b6ec2920b3cd08dcc295161ae)"}}, "variants": [{"name": "jreApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": "8", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.google.guava", "module": "failureaccess", "version": {"requires": "1.0.2"}}, {"group": "com.google.guava", "module": "listenablefuture", "version": {"requires": "9999.0-empty-to-avoid-conflict-with-guava"}}, {"group": "com.google.code.findbugs", "module": "jsr305", "version": {"requires": "3.0.2"}}, {"group": "org.checkerframework", "module": "checker-qual", "version": {"requires": "3.43.0"}}, {"group": "com.google.errorprone", "module": "error_prone_annotations", "version": {"requires": "2.28.0"}}, {"group": "com.google.j2objc", "module": "j2objc-annotations", "version": {"requires": "3.0.0"}}], "files": [{"name": "guava-33.3.0-jre.jar", "url": "guava-33.3.0-jre.jar"}], "capabilities": [{"group": "com.google.guava", "name": "guava", "version": "33.3.0-jre"}, {"group": "com.google.collections", "name": "google-collections", "version": "33.3.0-jre"}]}, {"name": "jreRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": "8", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.google.guava", "module": "failureaccess", "version": {"requires": "1.0.2"}}, {"group": "com.google.guava", "module": "listenablefuture", "version": {"requires": "9999.0-empty-to-avoid-conflict-with-guava"}}, {"group": "com.google.code.findbugs", "module": "jsr305", "version": {"requires": "3.0.2"}}, {"group": "org.checkerframework", "module": "checker-qual", "version": {"requires": "3.43.0"}}, {"group": "com.google.errorprone", "module": "error_prone_annotations", "version": {"requires": "2.28.0"}}], "files": [{"name": "guava-33.3.0-jre.jar", "url": "guava-33.3.0-jre.jar"}], "capabilities": [{"group": "com.google.guava", "name": "guava", "version": "33.3.0-jre"}, {"group": "com.google.collections", "name": "google-collections", "version": "33.3.0-jre"}]}, {"name": "androidApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": "8", "org.gradle.jvm.environment": "android", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.google.guava", "module": "failureaccess", "version": {"requires": "1.0.2"}}, {"group": "com.google.guava", "module": "listenablefuture", "version": {"requires": "9999.0-empty-to-avoid-conflict-with-guava"}}, {"group": "com.google.code.findbugs", "module": "jsr305", "version": {"requires": "3.0.2"}}, {"group": "org.checkerframework", "module": "checker-qual", "version": {"requires": "3.43.0"}}, {"group": "com.google.errorprone", "module": "error_prone_annotations", "version": {"requires": "2.28.0"}}, {"group": "com.google.j2objc", "module": "j2objc-annotations", "version": {"requires": "3.0.0"}}], "files": [{"name": "guava-33.3.0-android.jar", "url": "../33.3.0-android/guava-33.3.0-android.jar"}], "capabilities": [{"group": "com.google.guava", "name": "guava", "version": "33.3.0-jre"}, {"group": "com.google.collections", "name": "google-collections", "version": "33.3.0-jre"}]}, {"name": "androidRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": "8", "org.gradle.jvm.environment": "android", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.google.guava", "module": "failureaccess", "version": {"requires": "1.0.2"}}, {"group": "com.google.guava", "module": "listenablefuture", "version": {"requires": "9999.0-empty-to-avoid-conflict-with-guava"}}, {"group": "com.google.code.findbugs", "module": "jsr305", "version": {"requires": "3.0.2"}}, {"group": "org.checkerframework", "module": "checker-qual", "version": {"requires": "3.43.0"}}, {"group": "com.google.errorprone", "module": "error_prone_annotations", "version": {"requires": "2.28.0"}}], "files": [{"name": "guava-33.3.0-android.jar", "url": "../33.3.0-android/guava-33.3.0-android.jar"}], "capabilities": [{"group": "com.google.guava", "name": "guava", "version": "33.3.0-jre"}, {"group": "com.google.collections", "name": "google-collections", "version": "33.3.0-jre"}]}]}