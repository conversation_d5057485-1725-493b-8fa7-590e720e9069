### ssh

```bash
ssh root@46.202.130.153   ( W3sf4rth2c5r2# )
```

# Resumen de Comandos: Conectar macOS a Clúster Kubernetes en Ubuntu

Esta guía resume el proceso completo para configurar el acceso remoto desde una máquina macOS a un clúster de Kubernetes autogestionado en un servidor Ubuntu 24.04.

## 1. Pre-requisitos en la Máquina Local (macOS)

Instalación de la herramienta de línea de comandos de Kubernetes.

```bash
brew install kubectl
```

## 2. Preparación en el Servidor (Ubuntu 24.04)

Configuración en el nodo maestro (control-plane) para permitir el acceso remoto.

### Conexión por SSH

```bash
ssh tu_usuario@<IP_PUBLICA_DEL_VPS>
```

### Crear `kubeconfig` para Acceso Remoto

Estos comandos crean una copia del `kubeconfig` reemplazando la IP local por la IP pública del servidor.

```bash
PUBLIC_IP=$(curl -s -4 ifconfig.co) && \
sudo cat /etc/kubernetes/admin.conf | sed "s/server: .*/server: https:\/\/$PUBLIC_IP:6443/" > ~/kubeconfig-remote

sudo chown $(whoami):$(whoami) ~/kubeconfig-remote
```

## 3. Copia y Configuración en la Máquina Local (macOS)

Transferir el archivo de configuración al Mac y fusionarlo con la configuración local de `kubectl`.

### Copiar el archivo desde el servidor

Ejecutar desde una nueva terminal en el Mac.

```bash
scp tu_usuario@<IP_PUBLICA_DEL_VPS>:~/kubeconfig-remote ~/Downloads
```

### Fusionar el `kubeconfig`

Integra de forma segura la nueva configuración sin sobreescribir las existentes.

```bash
# Crear directorio .kube si no existe
mkdir -p ~/.kube

# Mover y renombrar el archivo descargado
mv ~/Downloads/kubeconfig-remote ~/.kube/config-vps-ubuntu

# Fusionar la configuración de forma segura
KUBECONFIG=~/.kube/config:~/.kube/config-vps-ubuntu kubectl config view --flatten > ~/.kube/config.new && \
mv ~/.kube/config.new ~/.kube/config

# Limpiar el archivo temporal
rm ~/.kube/config-vps-ubuntu
```

## 4. Configuración Final del Firewall (Servidor Ubuntu)

Asegurar el servidor abriendo solo los puertos necesarios.

### Permitir Puertos Esenciales

**¡Importante!** Permitir SSH primero para no perder el acceso.

```bash
# Permitir acceso SSH
sudo ufw allow ssh

# Permitir acceso a la API de Kubernetes
sudo ufw allow 6443/tcp
```

### Activar y Verificar Firewall

```bash
# Activar el firewall (escribe 'y' cuando pregunte)
sudo ufw enable

# Comprobar el estado final
sudo ufw status
```

## 5. Verificación y Uso desde macOS

Pasos finales en el Mac para confirmar que la conexión funciona.

### Gestionar Contextos de `kubectl`

`kubectl` necesita saber a qué clúster apuntar.

```bash
# 1. Ver todos los contextos disponibles (busca el de tu VPS)
kubectl config get-contexts

# 2. Cambiar al contexto correcto (reemplaza el nombre)
kubectl config use-context <nombre-del-contexto-correcto>
```

### Comando Final de Verificación

Si todo ha ido bien, este comando devolverá la lista de nodos de tu clúster.

```bash
kubectl get nodes
```

## 6. Herramienta Adicional Recomendada (macOS)

Para una gestión más cómoda del clúster desde la terminal.

```bash
# Instalar k9s
brew install k9s

# Ejecutar k9s
k9s
```
