Para ver el listado de **proyectos de Google Cloud Platform (GCP)** disponibles en tu cuenta usando la herramienta de línea de comandos `gcloud`, puedes usar el siguiente comando:

## Login

```bash
gcloud auth login
```

## Listar proyectos

```bash
gcloud projects list
```

Este comando muestra una lista de todos los proyectos asociados a tu cuenta o a la organización si tienes acceso a múltiples proyectos.

### Ejemplo de salida:

```
PROJECT_ID              NAME                PROJECT_NUMBER
my-project-12345        My Project          123456789012
another-project         Another Project     234567890123
```

---

## Opciones útiles

- Para mostrar información adicional como el estado del proyecto:

```bash
gcloud projects list --format="table(projectId, name, lifecycleState)"
```

---

## Ver proyecto actual configurado

Si quieres saber cuál es el proyecto activo actualmente en tu configuración local:

```bash
gcloud config get-value project
```

O también puedes usar:

```bash
gcloud config list
```

# Bucket

- Listado buckets

```bash
gsutil ls
```
