# Restauración de DB2 con Docker Compose

Este proyecto permite levantar una instancia de DB2 en Docker y restaurar una base de datos desde un backup.

## 1. Configuración

Configura las variables de entorno en el fichero `.env`:

- `CONTAINER_NAME`: Nombre del contenedor Docker.
- `DB_HOST_PORT`: Puerto local mapeado al 50000 del contenedor DB2.
- `DB2_INSTANCE`: Usuario de la instancia DB2 (ej. `db2inst1`).
- `DB2_PASSWORD`: Contraseña del usuario de la instancia DB2.
- `DB_NAME`: Nombre de la base de datos a restaurar.
- `LOCAL_BACKUP_PATH`: Ruta local al fichero de backup (ej. `GNPDB296.0.db2inst1.DBPART000.20250703055039.001.zip`).
- `UNZIPPED_BACKUP_NAME`: Nombre del fichero de backup una vez descomprimido (ej. `GNPDB296.0.db2inst1.DBPART000.20250703055039.001`).
- `DB_TIMESTAMP`: Timestamp del backup (ej. `20250703055039`).

## 2. Arrancar DB2
### 2.1 Limpiar instalaciones anteriores

```bash
docker-compose down -v
```

### 2.2 Arrancar DB2

```bash
docker-compose up -d
```
- Revisar los logs hasta que la instancia esté lista.
```bash
docker-compose logs -f
    #gnp-db2-b  | DATA #1 : Hexdump, 32 bytes
    #gnp-db2-b  | 0x00007FFFFFFC6C80 : 2A20 2020 2020 454E 4420 2020 2020 202A    *     END      *
    #gnp-db2-b  | 0x00007FFFFFFC6C90 : 2A2A 2A2A 2045 5850 4F52 5420 2A2A 2A2A    **** EXPORT ****
```

## 3. Cargar Variables de Entorno (en el host)

```bash
export $(grep -v '^#' .env | xargs)
```

## 4. Copiar Ficheros al Contenedor (desde el host)

**Paso 1: Copiar el fichero de backup**

```bash
docker cp ${LOCAL_BACKUP_PATH} ${CONTAINER_NAME}:/tmp/
```

**Paso 2: Copiar el fichero .env**

```bash
docker cp .env ${CONTAINER_NAME}:/tmp/.env
```

## 5. Restaurar Base de Datos (Manual)

Asegúrate de que el contenedor DB2 esté en ejecución y los ficheros copiados.

**Paso 1: Preparar Entorno (desde el host, como root)**

Crea los directorios necesarios y asigna los permisos correctos para el usuario DB2. **Ejecuta este comando desde tu máquina local:**

```bash
docker exec -u root ${CONTAINER_NAME} bash -c "
  mkdir -p /database/data /database/logs/${DB_NAME} && 
  chown -R ${DB2_INSTANCE}:db2iadm1 /database/data /database/logs && 
  chmod 755 /database/data && 
  chmod 777 /database/logs && 
  chmod 755 /database/logs/${DB_NAME} 
"
```

**Paso 2: Descomprimir Backup (si es un .zip) (desde el host, como root)**

Si tu fichero de backup es un `.zip`, descomprímelo en `/tmp/` dentro del contenedor y asigna los permisos correctos. **Ejecuta este comando desde tu máquina local:**

```bash
docker exec -u root ${CONTAINER_NAME} bash -c "  unzip -o /tmp/${LOCAL_BACKUP_PATH} -d /tmp/ &&   chown ${DB2_INSTANCE}:db2iadm1 /tmp/${UNZIPPED_BACKUP_NAME} &&   chmod 640 /tmp/${UNZIPPED_BACKUP_NAME} &&   rm /tmp/${LOCAL_BACKUP_PATH} "
```
*(Nota: Esto asume que `unzip` está instalado en el contenedor. Si no, se necesitaría un paso previo para instalarlo o usar otro método de descompresión.)*

**Paso 3: Conectarse al Contenedor DB2 como ${DB2_INSTANCE}**

```bash
docker exec -it ${CONTAINER_NAME} bash -c "su - ${DB2_INSTANCE}"
```

**A partir de aquí, todos los comandos se ejecutan DENTRO del contenedor, en la sesión de ${DB2_INSTANCE}.**

**Paso 4: Cargar Variables de Entorno (dentro del contenedor)**

```bash
source /tmp/.env
```

**Paso 5: Limpiar Base de Datos Existente (dentro del contenedor)**

```bash
db2 force applications all
db2 drop database ${DB_NAME}
```

**Paso 6: Verificar Integridad del Backup (dentro del contenedor)**

```bash
db2ckbkp /tmp/${UNZIPPED_BACKUP_NAME}
```

**Paso 7: Verificar directorio de logs (dentro del contenedor)**

```bash
echo "DB_NAME: ${DB_NAME}"
ls -la /database/logs/
ls -la /database/logs/${DB_NAME}
```

**Paso 8: Iniciar Restauración (Restore Redirect) (dentro del contenedor)**

```bash
db2 RESTORE DATABASE ${DB_NAME} FROM '/tmp' TAKEN AT ${DB_TIMESTAMP} INTO ${DB_NAME} NEWLOGPATH "/database/logs/${DB_NAME}" REDIRECT WITHOUT ROLLING FORWARD WITHOUT PROMPTING
```

**Paso 9: Redirigir Storage Group (dentro del contenedor)**

```bash
db2 SET STOGROUP PATHS FOR IBMSTOGROUP ON '/database/data'
```

**Paso 10: Continuar Restauración (dentro del contenedor)**

```bash
db2 RESTORE DATABASE ${DB_NAME} CONTINUE
```

**Paso 11: Monitorear Progreso del Restore (dentro del contenedor)**

```bash
db2 list utilities show detail
```

**Paso 12: Finalizar y Poner la BD Online (Rollforward) (dentro del contenedor)**

```bash
db2 ROLLFORWARD DATABASE ${DB_NAME} TO END OF LOGS AND COMPLETE
```

**Paso 13: Verificar la Restauración (dentro del contenedor)**

```bash
db2 connect to ${DB_NAME}
db2 "select count(*) as total_tables from syscat.tables where type='T'"
db2 connect reset
```

## 6. Datos de Conexión

- **Host**: `localhost:${DB_HOST_PORT}`
- **Base de Datos**: `${DB_NAME}`
- **Usuario**: `${DB2_INSTANCE}`
- **Contraseña**: `${DB2_PASSWORD}`
