services:
  db2:
    image: icr.io/db2_community/db2
    platform: linux/amd64
    container_name: ${CONTAINER_NAME}
    hostname: ${CONTAINER_NAME}
    restart: always
    privileged: true
    ports:
      - "${DB_HOST_PORT}:50000"
    env_file:
      - .env
    environment:
      LICENSE: accept
      DB2INSTANCE: ${DB2_INSTANCE}
      DB2INST1_PASSWORD: ${DB2_PASSWORD}
      BLU: false
      ENABLE_ORACLE_COMPATIBILITY: false
      UPDATEAVAIL: NO
      TO_CREATE_SAMPLEDB: false
      REPODB: false
      IS_OSXFS: false
      PERSISTENT_HOME: false
      HADR_ENABLED: false
    volumes:
      - db2_volume:/database
volumes:
  db2_volume: {}