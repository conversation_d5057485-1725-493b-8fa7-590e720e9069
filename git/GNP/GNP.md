### Ejecutar procesos

- Fichero application-local.yml del proceso a ejecutar

* TTSORT

```ymal
context:
  sort-program-name: docker exec ttsort-container /app/ttsortx64
os390:
  base-path: /tmp/gnp/cuentacorriente
```

- DATASOURCE

* Local

```yaml
spring:
  datasource:
    #url: *******************************************************;
    url: ******************************************************;
    user: db2inst1
    password: db2inst1
  batch:
    jdbc:
      initialize-schema: always
```

- Tunel gnp

```bash
gcloud compute ssh mm-noprod-usc1a-db2-qa --zone us-central1-f --project gnp-data --tunnel-through-iap -- -L 25010:localhost:25010
```

- Aumentar memoria, en build-gradel

bootRun con > spring.profiles.active=local

```groovy
bootRun {
    // Asigna 1GB de memoria inicial y hasta 4GB de memoria máxima
    jvmArgs = ["-Xms1g", "-Xmx4g"]
}
```

### Buckets

- login

```
gcloud auth login ( con la cuenta de GNP)
```

 - Para pruebas locales
  Proyecto: gnp-cuentacorriente-dev
  Bucket: gnp-mm-usc1-cuentacorriente-fs-sync-qas
  
 ![alt text](image.png)

- listar proyectos

```
gcloud projects list
# con detalle
gcloud projects list --format=json
# solo el id
gcloud projects list --format="value(projectId)"
```

- seleccionar proyecto

```
# ver proyecto actual
gcloud config get-value project

# elegir proyecto 'gpn-data'
gcloud config set project gnp-data
```

- listar buckets

```
gsutil ls
```

- listar archivos de un bucket

```
gsutil ls gs://gnp-data
```

- descargar archivo

```
gsutil cp gs://gnp-ia-dev/requirements.txt .
```
